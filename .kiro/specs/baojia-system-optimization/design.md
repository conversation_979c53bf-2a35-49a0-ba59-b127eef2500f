# 设计文档

## 概述

本设计文档描述了报价系统优化的技术实现方案，包括模板可见性改进、报价列表权限控制、保存行为优化、页面状态管理和产品筛选集成。设计基于现有的Spring Boot后端和Vue.js前端架构，采用增量式改进方式，确保向后兼容性。

## 架构

### 系统架构概述
- **后端**: Spring Boot + MyBatis Plus + MySQL
- **前端**: Vue 3 + Ant Design Vue + Pinia状态管理
- **数据库**: 现有的`y_baojia_content`表

### 核心组件
1. **BaojiaContentService**: 业务逻辑层，处理权限控制和数据筛选
2. **BaojiaContentController**: API控制层，提供RESTful接口
3. **前端页面组件**: list.vue（列表页）、add.vue（编辑页）
4. **状态管理**: Pinia store用于页面状态持久化
5. **模板选择组件**: TemplateSelectModal优化

## 组件和接口

### 1. 模板可见性优化

#### 后端修改
**文件**: `BaojiaContentServiceImpl.java`

```java
// 在queryContent方法中修改模板查询逻辑
if (queryForm.getContentType() != null && queryForm.getContentType() == 1) {
    // 模板类型(contentType=1)：所有人可见，不限制userId
    // 移除userId限制，允许查看所有模板
    queryForm.setUserId(null);
}
```

#### 前端修改
**文件**: `TemplateSelectModal.vue`

```javascript
// 移除当前用户限制，查询所有模板
const queryParams = reactive({
    keyword: '',
    contentType: 1, // 只查询模板类型
    userId: null, // 不限制用户，查询所有模板
    disabledFlag: false
});
```

### 2. 报价列表权限控制

#### 后端API扩展
**新增查询参数**: `listScope` (my|all)

```java
// BaojiaContentQueryForm.java 新增字段
private String listScope; // "my" 或 "all"

// BaojiaContentServiceImpl.java 修改查询逻辑
if (queryForm.getContentType() != null && queryForm.getContentType() == 2) {
    if ("my".equals(queryForm.getListScope())) {
        // 我的报价：只显示当前用户的
        queryForm.setUserId(currentUser.getUserId());
    } else if ("all".equals(queryForm.getListScope())) {
        // 全部报价：显示所有用户的，但前端控制操作权限
        queryForm.setUserId(null);
    }
}
```

#### 前端列表页改进
**文件**: `list.vue`

```javascript
// 新增列表类型状态
const listType = ref('my'); // 'my' | 'all'

// 查询参数调整
const queryForm = reactive({
    contentType: 2, // 报价类型
    listScope: 'my', // 默认显示我的报价
    // ... 其他参数
});

// 列表类型切换处理
function onListTypeChange(e) {
    listType.value = e.target.value;
    queryForm.listScope = e.target.value;
    queryData();
}

// 操作权限控制
function canEdit(record) {
    return listType.value === 'my' || record.userId === currentUser.userId;
}

function canDelete(record) {
    return listType.value === 'my' || record.userId === currentUser.userId;
}
```

### 3. 保存行为优化

#### 后端逻辑调整
**文件**: `BaojiaContentServiceImpl.java`

```java
// 修改updateContent方法，确保更新而非新建
@Override
@Transactional(rollbackFor = Exception.class)
public ResponseDTO<String> updateContent(BaojiaContentUpdateForm updateForm) {
    // 验证记录存在性
    var entity = this.getById(updateForm.getId());
    if (entity == null || Boolean.TRUE.equals(entity.getDeletedFlag())) {
        return ResponseDTO.userErrorParam("数据不存在");
    }
    
    // 权限验证
    if (!hasPermission(entity)) {
        return ResponseDTO.userErrorParam("无权限修改此数据");
    }
    
    // 更新内容，保持原有创建信息
    BeanUtils.copyProperties(updateForm, entity, 
        "userId", "createUserId", "createUserName", "createTime");
    
    this.updateById(entity);
    return ResponseDTO.ok();
}
```

#### 前端保存逻辑
**文件**: `add.vue`

```javascript
// 保存处理逻辑优化
async function onSubmit() {
    try {
        await formRef.value.validate();
        
        const formData = {
            ...form,
            content: await excelRef.value.getJsonData()
        };
        
        let response;
        if (form.id) {
            // 更新现有记录
            response = await templateApi.update(formData);
        } else {
            // 创建新记录
            response = await templateApi.add(formData);
            // 保存返回的ID，后续保存使用更新接口
            if (response.ok && response.data) {
                form.id = response.data;
            }
        }
        
        if (response.ok) {
            message.success(form.id ? '更新成功' : '保存成功');
        }
    } catch (error) {
        message.error('保存失败');
    }
}
```

### 4. 页面状态管理

#### Pinia Store设计
**新文件**: `stores/baojiaPageState.js`

```javascript
import { defineStore } from 'pinia';

export const useBaojiaPageStateStore = defineStore('baojiaPageState', {
    state: () => ({
        // 报价编辑页状态
        quotationEditState: {
            form: null,
            excelData: null,
            lastSaved: null,
            isDirty: false
        },
        // 模板编辑页状态
        templateEditState: {
            form: null,
            excelData: null,
            lastSaved: null,
            isDirty: false
        }
    }),
    
    actions: {
        // 保存报价编辑状态
        saveQuotationState(formData, excelData) {
            this.quotationEditState = {
                form: { ...formData },
                excelData: excelData,
                lastSaved: Date.now(),
                isDirty: true
            };
        },
        
        // 恢复报价编辑状态
        restoreQuotationState() {
            return this.quotationEditState;
        },
        
        // 清除状态
        clearQuotationState() {
            this.quotationEditState = {
                form: null,
                excelData: null,
                lastSaved: null,
                isDirty: false
            };
        }
    },
    
    persist: {
        key: 'baojia-page-state',
        storage: sessionStorage
    }
});
```

#### 页面状态持久化
**文件**: `add.vue`

```javascript
import { useBaojiaPageStateStore } from '@/stores/baojiaPageState';

const pageStateStore = useBaojiaPageStateStore();

// 页面离开时保存状态
onBeforeRouteLeave((to, from, next) => {
    if (isDirty.value) {
        const excelData = excelRef.value?.getJsonData();
        pageStateStore.saveQuotationState(form, excelData);
    }
    next();
});

// 页面进入时恢复状态
onMounted(() => {
    const savedState = pageStateStore.restoreQuotationState();
    if (savedState.form && savedState.isDirty) {
        // 询问用户是否恢复
        Modal.confirm({
            title: '发现未保存的数据',
            content: '是否恢复之前的编辑内容？',
            onOk: () => {
                Object.assign(form, savedState.form);
                if (savedState.excelData) {
                    nextTick(() => {
                        excelRef.value?.setJsonData(savedState.excelData);
                    });
                }
            },
            onCancel: () => {
                pageStateStore.clearQuotationState();
            }
        });
    }
});
```

### 5. 产品筛选集成

#### 产品管理API集成
基于现有的`y_product_type`表进行集成

**新文件**: `api/business/product-type-api.js`

```javascript
import { postRequest, getRequest } from '@/lib/axios';

export const productTypeApi = {
    // 获取产品类型列表
    getProductTypes: (params) => {
        return postRequest('/product-type/query', params);
    },
    
    // 获取所有启用的产品类型（用于下拉选择）
    getAllEnabled: () => {
        return getRequest('/product-type/all-enabled');
    }
};
```

#### 后端产品筛选支持
**文件**: `BaojiaContentQueryForm.java`

```java
// 新增产品类型筛选字段
private Long productTypeId; // 产品类型ID
private String productTypeName; // 产品类型名称

// BaojiaContentServiceImpl.java 查询逻辑扩展
// 在queryContent方法中添加产品类型筛选条件
if (queryForm.getProductTypeId() != null) {
    // 根据产品类型ID筛选，通过productName字段关联
    // 需要查询y_product_type表获取对应的产品类型名称进行匹配
}
```

#### 新增产品类型控制器
**新文件**: `ProductTypeController.java`

```java
@RestController
@RequestMapping("/product-type")
@Tag(name = "业务-产品类型管理")
public class ProductTypeController {
    
    @GetMapping("/all-enabled")
    @Operation(summary = "获取所有启用的产品类型")
    public ResponseDTO<List<ProductTypeVO>> getAllEnabled() {
        // 查询y_product_type表中所有启用的产品类型
        return productTypeService.getAllEnabled();
    }
}
```

#### 前端筛选组件
**文件**: `list.vue`

```javascript
// 产品类型数据
const productTypes = ref([]);

// 查询表单扩展
const queryForm = reactive({
    // ... 现有字段
    productTypeId: null,
    productTypeName: null
});

// 获取产品类型列表
async function loadProductTypes() {
    try {
        const response = await productTypeApi.getAllEnabled();
        if (response.ok) {
            productTypes.value = response.data;
        }
    } catch (error) {
        console.error('加载产品类型失败:', error);
    }
}

// 产品类型筛选
function onProductTypeChange(value, option) {
    queryForm.productTypeId = value;
    queryForm.productTypeName = option?.label;
    queryData();
}
```

## 数据模型

### 现有表结构保持不变
`y_baojia_content`表无需修改，通过业务逻辑层实现权限控制。

### 现有产品管理表结构
基于现有的`y_product_type`表进行产品筛选集成，无需创建新表。

```sql
-- 现有产品类型表（参考）
-- y_product_type 表包含产品线信息
-- 具体字段结构需要根据实际表结构进行适配
```

## 错误处理

### 后端错误处理
1. **权限验证失败**: 返回403状态码和明确错误信息
2. **数据不存在**: 返回404状态码
3. **参数验证失败**: 返回400状态码和详细验证信息
4. **系统异常**: 记录日志并返回500状态码

### 前端错误处理
1. **网络请求失败**: 显示友好的错误提示
2. **权限不足**: 跳转到无权限页面或显示权限提示
3. **数据加载失败**: 提供重试机制
4. **状态恢复失败**: 清除损坏的状态数据

## 测试策略

实际功能测试将由开发完成后进行手动验证，确保各项优化功能按预期工作。