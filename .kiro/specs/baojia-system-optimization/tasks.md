# 实施计划

- [x] 1. 优化模板可见性功能





  - 修改后端查询逻辑，使所有用户都能查看所有模板
  - 更新模板选择组件，移除用户限制
  - _需求: 1.1, 1.2, 1.3_

- [x] 2. 实现报价列表权限控制





- [x] 2.1 后端API扩展支持列表类型参数


  - 在BaojiaContentQueryForm中添加listScope字段
  - 修改BaojiaContentServiceImpl的queryContent方法，根据listScope参数控制数据范围
  - _需求: 2.1, 2.2_

- [x] 2.2 前端列表页面权限控制实现


  - 在list.vue中添加"我的报价"和"全部报价"切换功能
  - 实现基于列表类型的操作权限控制（编辑、删除按钮显示控制）
  - 添加报价人筛选功能（仅在"全部报价"模式下显示）
  - _需求: 2.1, 2.2, 2.3, 2.4_

- [x] 3. 修复报价保存行为





- [x] 3.1 后端保存逻辑优化


  - 确保updateContent方法正确更新现有记录而不创建新记录
  - 验证保存时保持原有创建元数据不变
  - _需求: 3.1, 3.2, 3.4_

- [x] 3.2 前端保存逻辑改进


  - 修改add.vue中的onSubmit方法，确保有ID时使用更新接口
  - 在首次保存成功后保存返回的ID，后续操作使用更新接口
  - _需求: 3.1, 3.2, 3.3_

- [x] 4. 实现页面状态持久化









- [x] 4.1 创建Pinia状态管理store


  - 创建baojiaPageState.js store文件
  - 实现报价编辑状态和模板编辑状态的保存、恢复、清除功能
  - 配置sessionStorage持久化
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [x] 4.2 报价编辑页面状态管理集成




  - 在add.vue中集成状态管理store
  - 实现页面离开时自动保存状态
  - 实现页面进入时询问用户是否恢复状态
  - 添加状态变更检测机制
  - _需求: 4.1, 4.2, 4.5_

- [x] 4.3 模板编辑页面状态管理集成


  - 在模板编辑页面中集成状态管理store
  - 实现与报价编辑页面相同的状态持久化逻辑
  - _需求: 4.3, 4.4, 4.5_

- [-] 5. 集成产品管理数据筛选



- [x] 5.1 创建产品类型API接口


  - 创建product-type-api.js文件
  - 实现获取产品类型列表的API调用方法
  - 添加获取所有启用产品类型的接口
  - _需求: 5.1, 5.2_

- [x] 5.2 后端产品类型筛选支持


  - 在BaojiaContentQueryForm中添加产品类型筛选字段
  - 修改查询逻辑支持按产品类型筛选
  - 创建ProductTypeController提供产品类型查询接口
  - _需求: 5.3, 5.4_

- [x] 5.3 前端产品筛选功能实现






  - 在list.vue中添加产品类型筛选下拉框
  - 在模板和报价编辑页面中集成产品类型选择
  - 实现产品类型数据的加载和筛选逻辑
  - _需求: 5.1, 5.2, 5.3, 5.4, 5.5_

- [ ] 6. 系统集成和优化
- [ ] 6.1 错误处理和用户体验优化
  - 添加适当的错误提示和加载状态
  - 优化权限不足时的用户提示
  - 确保所有异步操作都有适当的错误处理
  - _需求: 所有相关需求_

- [ ] 6.2 代码重构和性能优化
  - 重构重复代码，提高代码复用性
  - 优化查询性能，确保大数据量下的响应速度
  - 清理不必要的代码和注释
  - _需求: 所有相关需求_