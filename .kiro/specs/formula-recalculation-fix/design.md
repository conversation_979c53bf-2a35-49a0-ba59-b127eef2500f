# 公式重新计算修复 - 设计文档

## 概述

通过深入分析 Univer 源码，问题的根本原因已经确定：**Univer 使用了全局缓存**，这些缓存在模块级别定义，不属于任何特定实例，导致跨实例的数据污染。

### 关键发现

Univer 中存在以下全局缓存：

1. **`CELL_INVERTED_INDEX_CACHE`** - 全局单元格倒排索引缓存
2. **`FORMULA_REF_TO_ARRAY_CACHE`** - 全局公式引用数组缓存  
3. **`FORMULA_AST_CACHE`** - 全局公式AST缓存

这些缓存是模块级别的单例，在整个应用生命周期中共享，不会因为创建新的 Univer 实例而自动清理。

## 架构

### 问题根源分析

通过实际测试发现了问题的精确机制：

**测试场景**：
1. 打开模板：A1=1, A2=2, A3=A1+A2=3
2. 打开报价：A1=1, A2=2, A3=A1+A2=3  
3. 修改报价：A1=3, A3=5
4. 修改模板：A1=2, A3=4
5. 关闭报价，重新打开报价：A1=1, A2=5 (错误！)

**关键发现**：
- 两个不同组件（模板/报价）互不影响，各自维护独立的缓存空间
- 问题出现在**同一组件关闭重开**时，全局缓存中仍保留该组件之前的计算结果
- 缓存键基于 `unitId + sheetId + 位置`，每个组件实例使用不同的 unitId
- `ArrayFormulaCellInterceptorController` 优先使用缓存数据，导致显示错误的历史值

### 问题流程

```
组件首次打开 → 缓存为空 → 显示JSON正确值 → 公式计算 → 结果存入全局缓存
     ↓
组件关闭 → Univer实例销毁 → 但全局缓存未清理
     ↓  
组件重新打开 → 新实例创建 → 拦截器发现缓存有数据 → 用缓存值覆盖JSON值 → 显示错误
```

### 解决方案策略

基于问题的精确机制，最简单有效的解决方案是：

**为每次创建的工作簿生成唯一的 unitId**

这种方案的优势：
1. **完全避免缓存冲突**：每次都是全新的 unitId，不会读取到旧缓存
2. **不需要清理全局缓存**：让缓存自然过期，避免影响其他实例  
3. **简单可靠**：不依赖 Univer 内部实现细节
4. **向后兼容**：不会破坏现有功能

## 组件和接口

### 1. 唯一 unitId 生成机制

**目标**：为每次创建的工作簿生成唯一的 unitId，避免缓存冲突

**核心实现**：
```javascript
// 在 initUniver 函数中
const initUniver = async (json) => {
    if (!univerContainer.value) return;
    
    // 清理旧实例
    if (univer) {
        univerAPI.dispose();
        univer.dispose();
        univer = null;
        univerAPI = null;
        univerWorkbook = null;
    }
    
    // 创建新实例
    const result = createUniver({
        locale: LocaleType.ZH_CN,
        locales: {
            [LocaleType.ZH_CN]: {
                ...UniverPresetSheetsCoreZhCN
            }
        },
        theme: defaultTheme,
        presets: [
            UniverSheetsCorePreset({
                container: univerContainer.value,
                formula: {
                    initialFormulaComputing: CalculationMode.FORCED
                }
            }),
            UniverSheetsDrawingPreset()
        ]
    });
    
    univer = result;
    univerAPI = result.univerAPI;
    
    // 关键：为每个工作簿生成唯一的 unitId
    if (json) {
        const uniqueJson = generateUniqueWorkbookData(json);
        console.log('使用唯一ID:', uniqueJson.id);
        univerWorkbook = univerAPI.createWorkbook(uniqueJson);
    } else {
        // 默认工作簿也使用唯一ID
        univerWorkbook = univerAPI.createWorkbook({
            id: generateUniqueId(),
            name: 'Excel工作簿',
            sheetOrder: ['sheet1'],
            styles: {
                0: {
                    ff: 'SimSun',
                    fs: 11,
                    cl: { rgb: '#000000' }
                }
            },
            sheets: {
                sheet1: {
                    id: 'sheet1',
                    name: 'Sheet1',
                    cellData: {},
                    defaultStyle: '0'
                }
            }
        });
    }
    
    // 设置监听器
    setTimeout(() => {
        setupSheetChangeListener();
        updateSheetState();
    }, 100);
    
    emit('ready', univerAPI);
};

// 生成唯一ID
const generateUniqueId = () => {
    return `workbook_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 为工作簿数据生成唯一ID
const generateUniqueWorkbookData = (originalJson) => {
    const uniqueId = generateUniqueId();
    
    return {
        ...originalJson,
        id: uniqueId,
        // 如果需要，也可以为sheet生成唯一ID
        // sheets: updateSheetIds(originalJson.sheets)
    };
};

// 可选：更新sheet ID（通常不需要，因为缓存主要基于workbook的unitId）
const updateSheetIds = (sheets) => {
    const newSheets = {};
    const sheetIdMapping = {};
    
    Object.keys(sheets).forEach(oldSheetId => {
        const newSheetId = `sheet_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        sheetIdMapping[oldSheetId] = newSheetId;
        
        newSheets[newSheetId] = {
            ...sheets[oldSheetId],
            id: newSheetId
        };
    });
    
    return { newSheets, sheetIdMapping };
};
```



## 实现细节

这个解决方案的核心思路是：**通过为每次创建的工作簿生成唯一的 unitId，完全避免全局缓存冲突**。

### 关键优势

1. **简单有效**：不需要理解 Univer 复杂的内部机制
2. **完全隔离**：每次都是全新的缓存空间，不会有历史数据干扰
3. **向后兼容**：不会影响现有功能
4. **易于维护**：代码改动最小，风险最低

### 潜在考虑

- **内存使用**：全局缓存会积累更多数据，但通常有 LRU 限制
- **性能影响**：无法复用计算结果，但对于组件重新加载场景影响很小