# 公式重新计算修复 - 需求文档

## 介绍

此功能解决了 Univer Excel 组件中的一个关键问题：在新实例中重新加载相同 JSON 数据时，公式无法正确重新计算。问题表现如下：

1. **第一次加载**：JSON 数据正确加载，公式显示正确值并响应数据变化
2. **第二次加载**（组件重新创建后）：JSON 数据包含正确值，但公式显示来自先前计算的缓存值，并且不再响应数据变化

根本原因是 `ArrayFormulaCellInterceptorController` 使用缓存的 `arrayFormulaCellData` 在渲染期间覆盖单元格值，而此缓存在创建新实例时没有被正确清理。

## 需求

### 核心需求

**用户故事：** 作为使用 Univer Excel 组件的开发者，我希望每次创建新实例时公式都能正确工作，无论是否存在先前的缓存数据，以便组件行为保持一致和可预测。

#### 验收标准

1. **实例隔离**：当创建新 Univer 实例时，公式引擎应以完全干净的状态初始化，不受先前实例的任何缓存数据影响
2. **正确显示**：当使用包含公式和值的 JSON 数据创建实例时，公式应显示 JSON 中的正确值，而不是任何缓存的计算结果
3. **响应性恢复**：当新实例中的数据发生变化时，公式应能正确重新计算并更新显示值
4. **一致性保证**：相同的 JSON 数据在多次创建新实例时应产生完全相同的显示和行为结果
5. **强制计算支持**：系统应提供机制在实例初始化时强制重新计算所有公式，确保公式引擎正确启动