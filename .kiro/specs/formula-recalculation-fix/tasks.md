# 实现计划

- [x] 1. 修改前端组件以生成唯一 unitId


  - 在 `initUniver` 函数中添加唯一 ID 生成逻辑
  - 为每次创建的工作簿生成唯一的 unitId，避免全局缓存冲突
  - _需求: 1, 2, 3, 4, 5_

- [x] 1.1 实现唯一 ID 生成函数


  - 创建 `generateUniqueId()` 函数，使用时间戳和随机字符串组合
  - 创建 `generateUniqueWorkbookData()` 函数，为工作簿数据分配唯一 ID
  - _需求: 1, 2_

- [x] 1.2 修改工作簿创建逻辑

  - 在 `univerAPI.createWorkbook()` 调用前为 JSON 数据分配唯一 ID
  - 确保默认工作簿也使用唯一 ID
  - 添加日志输出以便调试验证
  - _需求: 1, 2, 3_

- [x] 1.3 保持现有功能完整性

  - 确保 sheet 切换监听器正常工作
  - 确保组件暴露的 API 方法正常工作
  - 保持与父组件的事件通信
  - _需求: 4, 5_
- [ ] 2. 测试和验证修复效果
  - 创建测试用例验证同一组件重新打开时公式显示正确
  - 验证不同组件实例之间不会相互影响
  - 验证公式计算和响应性正常工作
  - _需求: 1, 2, 3, 4, 5_

- [x] 2.1 创建自动化测试脚本


  - 编写测试脚本模拟组件关闭重开场景
  - 验证公式值在重新打开后显示正确
  - 验证公式在数据变化时能正确重新计算
  - _需求: 1, 2, 3_

- [ ] 2.2 进行手动测试验证
  - 测试模板组件：打开 → 修改 → 关闭 → 重新打开，验证公式值正确
  - 测试报价组件：打开 → 修改 → 关闭 → 重新打开，验证公式值正确
  - 测试多实例场景：同时打开模板和报价，验证互不影响
  - _需求: 1, 2, 3, 4, 5_

- [ ] 3. 优化和完善
  - 添加错误处理机制，确保 ID 生成失败时有降级方案
  - 优化性能，确保唯一 ID 生成不影响组件加载速度
  - 添加必要的注释和文档
  - _需求: 4, 5_