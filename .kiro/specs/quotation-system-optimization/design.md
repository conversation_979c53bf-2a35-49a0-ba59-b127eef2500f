# 设计文档

## 概述

本设计文档针对报价系统的三个关键优化需求提供技术解决方案：修复Excel导出时公式转换为数值的问题、解决模板权限访问限制、以及移除"新增报价组"功能。系统基于Vue 3 + UniverJS + ExcelJS的技术架构，需要在前端和后端两个层面进行相应的修改。

## 架构

### 系统组件架构
```
前端层 (Vue 3)
├── 模板管理页面 (template/add.vue, template/list.vue)
├── 报价管理页面 (baojia/add.vue, baojia/list.vue)  
├── Excel编辑器组件 (ExcelEditor)
├── 模板选择组件 (TemplateSelectModal)
└── 导出功能模块 (exportUniverSheetWithExcelJS.js)

中间层 (UniverJS)
├── 工作簿数据管理
├── 公式处理引擎
└── 样式和格式管理

导出层 (ExcelJS)
├── Excel文件生成
├── 公式转换处理
└── 格式兼容性处理

后端层 (Spring Boot)
├── 模板API (template-api)
├── 权限控制逻辑
└── 数据访问层
```

### 数据流架构
```
用户操作 → Vue组件 → UniverJS → ExcelJS → Excel文件
                ↓
            后端API → 数据库
```

## 组件和接口

### 需求1：修复公式导出问题

#### 问题分析
通过分析UniverJS源码，发现了公式导出问题的根本原因：

1. **`si`属性的真实含义**: `si`是公式ID（formula ID），用于标识共享公式。在自动填充时，UniverJS会为第一个公式单元格设置`f`（公式字符串）和`si`（公式ID），后续填充的单元格只保留`si`属性，不保留`f`属性。

2. **自动填充机制**: 根据`FormulaAutoFillController`的实现，当用户拖拽填充公式时：
   - 第一个单元格：`{f: "=E11*H11", si: "randomId", v: calculatedValue}`
   - 后续单元格：`{si: "randomId", v: calculatedValue}` （注意：没有`f`属性）

3. **导出问题根源**: 当前导出逻辑只检查`f`属性来判断是否为公式，忽略了只有`si`属性的共享公式单元格，导致这些单元格被当作普通数值导出。

4. **UniverJS内部处理**: UniverJS能正确显示这些公式，是因为它的公式引擎能够根据`si`属性找到对应的公式定义并进行相对引用调整。

#### 技术方案

**文件**: `frontend/src/views/business/template/components/excel/exportUniverSheetWithExcelJS.js`

**核心修改点**:
1. 增强`handleFormulaCell`函数的公式识别能力
2. 改进`setCellValue`函数中的公式处理逻辑
3. 添加公式验证和降级处理机制

**解决方案**:

基于对UniverJS源码的深入分析，我们发现UniverJS内部使用`moveFormulaRefOffset`方法来处理共享公式。我们应该复用这个逻辑而不是重新实现：

```javascript
// 1. 增强setCellValue函数，使用UniverJS的公式处理逻辑
function setCellValue(excelCell, univerCellData, rowIndex, colIndex, univerWorkbookSnapshot, sheetData) {
    try {
        const univerValue = univerCellData.v;
        const univerCellType = univerCellData.t;
        const univerFormula = univerCellData.f;
        const univerSi = univerCellData.si; // 公式ID

        // 处理有公式字符串的单元格
        if (univerFormula && typeof univerFormula === 'string') {
            return handleFormulaCell(excelCell, univerFormula, univerValue);
        }

        // 处理共享公式单元格（只有si，没有f）- 使用UniverJS的逻辑
        if (univerSi && !univerFormula) {
            return handleSharedFormulaCellWithUniverLogic(
                excelCell, 
                univerCellData, 
                rowIndex, 
                colIndex, 
                sheetData
            );
        }

        // 其他情况按原逻辑处理...
        return handleTypedCell(excelCell, univerValue, univerCellType);
    } catch (error) {
        console.error('单元格值设置失败:', error);
        excelCell.value = univerCellData.v || '';
    }
}

// 2. 使用UniverJS的逻辑处理共享公式单元格
function handleSharedFormulaCellWithUniverLogic(excelCell, univerCellData, currentRow, currentCol, sheetData) {
    const siValue = univerCellData.si;
    
    // 复制UniverJS FormulaDataModel.getFormulaStringByCell 的逻辑
    const formulaString = getFormulaStringByCell(currentRow, currentCol, siValue, sheetData);
    
    if (formulaString) {
        excelCell.value = {
            formula: formulaString.startsWith('=') ? formulaString.substring(1) : formulaString,
            result: univerCellData.v !== undefined ? univerCellData.v : 0
        };
        console.log(`使用UniverJS逻辑重建共享公式: ${formulaString} (si: ${siValue})`);
        return;
    }
    
    // 如果无法重建公式，降级为数值
    console.warn(`无法重建共享公式，降级为数值 (si: ${siValue})`);
    excelCell.value = univerCellData.v;
}

// 3. 复制UniverJS的getFormulaStringByCell逻辑
function getFormulaStringByCell(row, column, si, sheetData) {
    if (!si) return null;
    
    let formulaString = null;
    
    // 遍历所有单元格，查找具有相同si且有公式字符串的单元格
    // 这里复制了UniverJS FormulaDataModel 中的逻辑
    const cellData = sheetData.cellData;
    
    for (const r in cellData) {
        const rowData = cellData[r];
        if (!rowData) continue;
        
        for (const c in rowData) {
            const cell = rowData[c];
            if (!cell) continue;
            
            const { f, si: currentId } = cell;
            
            // 找到具有相同si且有公式字符串的单元格
            if (f && typeof f === 'string' && si === currentId) {
                // 使用简化版的moveFormulaRefOffset逻辑
                formulaString = moveFormulaRefOffset(
                    f,
                    column - parseInt(c),  // 列偏移
                    row - parseInt(r)      // 行偏移
                );
                
                return formulaString;
            }
        }
    }
    
    return formulaString;
}

// 4. 简化版的moveFormulaRefOffset实现（基于UniverJS的逻辑）
function moveFormulaRefOffset(formulaString, refOffsetX, refOffsetY) {
    try {
        // 如果没有偏移，直接返回原公式
        if (refOffsetX === 0 && refOffsetY === 0) {
            return formulaString;
        }
        
        // 使用正则表达式处理单元格引用
        // 这里简化了UniverJS的复杂逻辑，只处理基本的单元格引用
        const result = formulaString.replace(/([A-Z]+)(\d+)/g, (match, colStr, rowStr) => {
            // 检查是否为绝对引用
            const isAbsoluteCol = colStr.startsWith('$');
            const isAbsoluteRow = rowStr.startsWith('$');
            
            // 清理$符号
            const cleanColStr = colStr.replace('$', '');
            const cleanRowStr = rowStr.replace('$', '');
            
            // 转换为数字
            const originalCol = columnStringToNumber(cleanColStr);
            const originalRow = parseInt(cleanRowStr);
            
            // 应用偏移（绝对引用不偏移）
            const newCol = isAbsoluteCol ? originalCol : originalCol + refOffsetX;
            const newRow = isAbsoluteRow ? originalRow : originalRow + refOffsetY;
            
            // 转换回Excel格式
            const newColStr = numberToColumnString(newCol);
            const finalColStr = isAbsoluteCol ? `$${newColStr}` : newColStr;
            const finalRowStr = isAbsoluteRow ? `$${newRow}` : newRow.toString();
            
            return `${finalColStr}${finalRowStr}`;
        });
        
        return result;
    } catch (error) {
        console.error('公式偏移计算失败:', error);
        return formulaString; // 返回原公式作为降级处理
    }
}

// 5. 辅助函数：列字符串转数字 (A=1, B=2, ..., Z=26, AA=27)
function columnStringToNumber(colStr) {
    let result = 0;
    for (let i = 0; i < colStr.length; i++) {
        result = result * 26 + (colStr.charCodeAt(i) - 64);
    }
    return result;
}

// 6. 辅助函数：数字转列字符串
function numberToColumnString(colNum) {
    let result = '';
    while (colNum > 0) {
        colNum--;
        result = String.fromCharCode(65 + (colNum % 26)) + result;
        colNum = Math.floor(colNum / 26);
    }
    return result;
}

// 7. 修改processCellData函数
async function processCellData(excelWorksheet, sheetData, univerWorkbookSnapshot, dataRange) {
    for (let row = 0; row <= dataRange.maxRow; row++) {
        for (let col = 0; col <= dataRange.maxCol; col++) {
            const cellData = sheetData.cellData[row]?.[col];
            if (cellData) {
                try {
                    const excelCell = excelWorksheet.getCell(row + 1, col + 1);
                    
                    // 传递sheetData用于公式重建
                    setCellValue(excelCell, cellData, row, col, univerWorkbookSnapshot, sheetData);
                    
                    // 设置样式...
                    const styleData = extractCellStyle(cellData, sheetData, univerWorkbookSnapshot);
                    if (styleData) {
                        applyCellStyle(excelCell, styleData);
                    }
                } catch (cellError) {
                    console.warn(`处理单元格 ${row},${col} 时出错:`, cellError);
                }
            }
        }
    }
}
```

**关键优势**:
1. **与UniverJS保持一致**: 使用与UniverJS相同的公式处理逻辑
2. **简化实现**: 不需要重新发明轮子，复用已验证的逻辑
3. **更好的兼容性**: 确保导出的公式与UniverJS显示的公式完全一致
4. **降级处理**: 如果公式处理失败，优雅降级为数值

### 需求2：修复模板权限问题

#### 问题分析
当前系统在模板查询时可能存在用户权限过滤，导致普通用户无法访问管理员创建的模板。

#### 技术方案

**前端修改**:
- **文件**: `frontend/src/components/business/template-select-modal/index.vue`
- **修改**: 确保查询参数不包含用户限制条件

**后端修改**:
- **文件**: 后端模板查询接口
- **修改**: 移除基于创建用户的权限过滤逻辑

**前端查询逻辑优化**:
```javascript
// 在queryTemplateList函数中确保查询所有模板
async function queryTemplateList() {
    const params = {
        ...queryParams,
        disabledFlag: false, // 只查询启用的模板
        // 移除任何用户相关的过滤条件
        // userId: undefined,
        // createUserId: undefined
    };
    
    const res = await templateApi.query(params);
    // 处理响应...
}
```

### 需求3：移除报价组创建功能

#### 问题分析
当前在两个文件中存在"新增报价组"的右键菜单功能：
1. `frontend/src/views/business/template/add.vue`
2. `frontend/src/views/business/baojia/add.vue`

#### 技术方案

**修改文件1**: `frontend/src/views/business/template/add.vue`
- 移除`addQuoteGroupMenu`菜单项的创建代码
- 删除`addQuoteGroup`函数定义

**修改文件2**: `frontend/src/views/business/baojia/add.vue`  
- 移除`addQuoteGroupMenu`菜单项的创建代码
- 删除`addQuoteGroup`函数定义

**具体修改点**:
```javascript
// 在registerCustomSalesMenu函数中移除以下代码块
/*
const addQuoteGroupMenu = api.createMenu({
    id: 'add-quote-group',
    title: '新增报价组',
    tooltip: '在当前位置下方新增报价组',
    action: () => {
        // ... 功能代码
    },
    order: 100
});
addQuoteGroupMenu.appendTo('contextMenu.others');
*/

// 删除整个addQuoteGroup函数
/*
function addQuoteGroup(currentRowIndex) {
    // ... 函数实现
}
*/
```

## 数据模型

### 公式数据结构
```javascript
// UniverJS公式单元格数据结构（完整版本）
{
    v: calculatedValue,    // 计算结果值
    t: CellValueType,      // 单元格类型 (2=数字)
    f: "=E11*H11",        // 公式字符串
    si: "ZgG0zE"          // 填充序列标识（可选，用于拖拽填充的单元格）
}

// UniverJS填充序列单元格数据结构（问题案例）
{
    v: 0,                 // 计算结果为0
    t: 2,                 // 数字类型
    si: "ZgG0zE",         // 填充序列标识
    // 注意：缺少f属性，这是导出问题的根源
}

// ExcelJS公式对象结构
{
    formula: "E11*H11",    // 不含=号的公式
    result: calculatedValue // 计算结果
}
```

### 模板查询参数
```javascript
// 模板查询参数结构
{
    keyword: "",           // 关键字搜索
    contentType: 1,        // 模板类型(1=模板,2=报价)
    generateType: undefined, // 生成类型筛选
    disabledFlag: false,   // 只查询启用的
    pageNum: 1,           // 页码
    pageSize: 10          // 页大小
    // 移除用户相关过滤字段
}
```

## 错误处理

### 公式导出错误处理
1. **公式识别失败**: 降级为数值导出，记录警告日志
2. **公式格式错误**: 清理格式后重试，失败则降级
3. **计算结果异常**: 使用默认值(0)替代

### 模板权限错误处理
1. **查询失败**: 显示友好错误信息，不提及权限问题
2. **模板加载失败**: 提供重试机制
3. **网络异常**: 显示网络错误提示

### 功能移除错误处理
1. **菜单注册失败**: 静默处理，不影响其他功能
2. **函数调用残留**: 添加防护性检查

## 测试策略

### 公式导出测试
1. **单元测试**: 测试各种公式类型的转换正确性
2. **集成测试**: 测试完整的导出流程
3. **兼容性测试**: 验证导出文件在Excel中的表现

### 模板权限测试
1. **权限测试**: 验证不同用户角色的模板访问能力
2. **数据完整性测试**: 确保所有模板都能正确显示
3. **性能测试**: 验证查询性能不受影响

### 功能移除测试
1. **UI测试**: 确认右键菜单中不再显示相关选项
2. **功能测试**: 验证移除功能不影响其他操作
3. **回归测试**: 确保现有功能正常工作

## 性能考虑

### 导出性能优化
- 批量处理公式转换，减少单个处理开销
- 添加进度提示，改善用户体验
- 优化内存使用，避免大文件导出时内存溢出

### 查询性能优化
- 保持现有的分页查询机制
- 优化数据库查询索引
- 添加适当的缓存策略

## 安全考虑

### 数据安全
- 确保模板数据的完整性和一致性
- 防止恶意公式注入
- 保护敏感业务数据

### 访问控制
- 虽然移除了模板创建权限限制，但保持基本的访问控制
- 确保只有授权用户能够访问系统功能
- 记录关键操作的审计日志