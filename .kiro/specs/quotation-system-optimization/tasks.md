# 实施计划

- [x] 1. 修复Excel导出公式问题





  - 修改exportUniverSheetWithExcelJS.js中的setCellValue函数，增加对共享公式（si属性）的处理
  - 实现handleSharedFormulaCellWithUniverLogic函数，使用UniverJS的逻辑处理共享公式单元格
  - 实现getFormulaStringByCell函数，复制UniverJS FormulaDataModel的逻辑查找主公式
  - 实现moveFormulaRefOffset函数，基于UniverJS的公式偏移计算逻辑
  - 添加columnStringToNumber和numberToColumnString辅助函数进行Excel列名转换
  - 修改processCellData函数，传递sheetData参数用于公式重建
  - 测试各种公式类型的导出正确性，特别是拖拽填充创建的共享公式
  - 验证导出的公式与UniverJS显示的公式完全一致
  - _需求: 1.1, 1.2, 1.3, 1.4_

- [x] 2. 修复模板权限访问问题







  - 检查template-select-modal组件的queryTemplateList函数
  - 确保查询参数中不包含用户权限相关的过滤条件
  - 验证所有用户都能访问管理员创建的模板
  - 测试模板选择功能在不同用户角色下的表现
  - 如需要，协调后端移除模板查询接口中的用户权限过滤逻辑
  - _需求: 2.1, 2.2, 2.3, 2.4_

- [x] 3. 移除新增报价组功能





  - 从template/add.vue的registerCustomSalesMenu函数中移除addQuoteGroupMenu菜单项创建代码
  - 从template/add.vue中删除addQuoteGroup函数定义及相关代码
  - 从baojia/add.vue的registerCustomSalesMenu函数中移除addQuoteGroupMenu菜单项创建代码
  - 从baojia/add.vue中删除addQuoteGroup函数定义及相关代码
  - 测试右键菜单确认不再显示"新增报价组"选项
  - 验证移除功能不影响其他Excel编辑器功能（选择销售、选择物料等）
  - _需求: 3.1, 3.2, 3.3, 3.4_