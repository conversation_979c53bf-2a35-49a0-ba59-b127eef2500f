# 标签页编辑功能增强设计文档

## 概述

本设计文档描述了如何实现标签页编辑功能的增强，包括数据保护提示机制、标签页重复问题修复和动态标题显示。设计遵循简单有效的原则，优先解决核心问题。

## 架构

### 系统组件关系
```
列表页面 (List Components)
├── 报价列表 (/baojia/list)
├── 模板列表 (/template/list)
└── showForm() 方法 - 负责编辑跳转和数据保护检测

编辑页面 (Edit Components)  
├── 报价编辑 (/baojia/add)
├── 模板编辑 (/template/add)
└── 动态标题管理

标签页管理 (Tab Management)
├── 用户Store (useUserStore)
├── 标签页导航 (tagNav)
└── 路由系统 (Vue Router)

菜单导航 (Menu Navigation)
├── 菜单导航逻辑 (menu-navigation.js)
└── 路由构建 (buildRoutes)
```

## 组件和接口

### 1. 数据保护提示机制

#### 1.1 检测逻辑设计
```javascript
// 在列表页面的 showForm 方法中实现
function showForm(data) {
  const tagNav = userStore.getTagNav || [];
  
  // 检查是否有对应的编辑标签页打开
  const hasOpenEditTab = tagNav.some(tag => {
    const menuRouterList = userStore.getMenuRouterList || [];
    const menuRouter = menuRouterList.find(menu => 
      menu.menuId.toString() === tag.menuName
    );
    return menuRouter && menuRouter.path === '/baojia/add'; // 或 '/template/add'
  });
  
  if (hasOpenEditTab) {
    // 显示数据保护提示
    showDataProtectionDialog(data);
  } else {
    // 直接跳转
    navigateToEdit(data);
  }
}
```

#### 1.2 提示对话框设计
```javascript
function showDataProtectionDialog(data) {
  Modal.confirm({
    title: '检测到未保存数据',
    content: '您已打开编辑页面，可能存在未保存的数据。请选择操作：',
    okText: '覆盖数据',
    cancelText: '取消',
    onOk() {
      closeExistingTabAndNavigate(data);
    },
    onCancel() {
      // 保持当前状态不变
    }
  });
}
```

#### 1.3 标签页关闭和重新打开逻辑
```javascript
function closeExistingTabAndNavigate(data) {
  // 1. 找到现有的编辑标签页
  const existingTag = findExistingEditTab();
  
  // 2. 关闭现有标签页
  if (existingTag) {
    userStore.closeTagNav(existingTag.menuName, false);
  }
  
  // 3. 延迟跳转，确保标签页关闭完成
  setTimeout(() => {
    navigateToEdit(data);
  }, 100);
}
```

### 2. 标签页重复问题修复

#### 2.1 路由名称统一
```javascript
// 确保静态路由和动态路由使用一致的命名规则
// frontend/src/router/modules/template.js
{
  path: 'list',
  name: 'TemplateList', // 使用明确的名称
  component: () => import('/src/views/business/template/list.vue'),
  meta: {
    title: '模板列表',
    menuType: MENU_TYPE_ENUM.MENU.value,
    icon: 'BarsOutlined'
  }
}
```

#### 2.2 标签页重复检测和清理
```javascript
// 在用户Store的setTagNav方法中添加重复检测
setTagNav(route, from) {
  // 检查是否存在相同路径的重复标签页
  const currentPath = route.path;
  const duplicateTags = (this.tagNav || []).filter((tag) => {
    if (tag.menuName === route.name) return false;
    const menuRouter = this.menuRouterList.find((m) => 
      m.menuId.toString() === tag.menuName
    );
    return menuRouter && menuRouter.path === currentPath;
  });

  // 移除重复的标签页
  duplicateTags.forEach(tag => {
    const index = this.tagNav.findIndex(t => t.menuName === tag.menuName);
    if (index > -1) {
      this.tagNav.splice(index, 1);
      this.deleteKeepAliveIncludes(tag.menuName);
    }
  });
  
  // 继续正常的标签页设置逻辑...
}
```

#### 2.3 菜单导航增强
```javascript
// 在menu-navigation.js中添加重复标签页清理
export function smartMenuNavigation(menu, router) {
  const userStore = useUserStore();
  const tagNav = userStore.getTagNav || [];
  
  // 检查是否已存在相同路径的标签页
  const existingTag = tagNav.find((tag) => {
    const menuRouterList = userStore.getMenuRouterList || [];
    const menuRouter = menuRouterList.find((m) => 
      m.menuId.toString() === tag.menuName
    );
    return menuRouter && menuRouter.path === menu.path;
  });

  if (existingTag) {
    // 跳转到已存在的标签页
    router.push({ 
      name: existingTag.menuName, 
      query: existingTag.menuQuery || {} 
    });
  } else {
    // 清理可能存在的重复标签页
    const duplicateTags = tagNav.filter((tag) => {
      const menuRouterList = userStore.getMenuRouterList || [];
      const menuRouter = menuRouterList.find((m) => 
        m.menuId.toString() === tag.menuName
      );
      return menuRouter && menuRouter.path === menu.path && 
             tag.menuName !== menu.menuId.toString();
    });
    
    // 关闭重复的标签页
    duplicateTags.forEach(tag => {
      userStore.closeTagNav(tag.menuName, false);
    });
    
    // 跳转到新页面
    userStore.deleteKeepAliveIncludes(menu.menuId.toString());
    router.push({ name: menu.menuId.toString() });
  }
}
```

### 3. 动态标题显示

#### 3.1 动态标题计算
```javascript
// 在编辑页面组件中实现
const pageTitle = computed(() => {
  if (route.path.includes('/baojia/add')) {
    return form.id ? '修改报价单' : '新建报价单';
  } else if (route.path.includes('/template/add')) {
    return form.id ? '修改模板' : '新建模板';
  }
  return '编辑页面';
});
```

#### 3.2 标题更新机制
```javascript
// 使用现有的useDynamicTitle组合式函数
import { useDynamicTitle } from '/@/composables/useDynamicTitle.js';

const { setPageTitle, clearPageTitle } = useDynamicTitle();

// 设置动态标题
setPageTitle(pageTitle);

// 页面卸载时清理标题
onUnmounted(() => {
  clearPageTitle();
});
```

#### 3.3 标题更新时机
```javascript
// 监听表单ID变化，自动更新标题
watch(
  () => form.id,
  () => {
    // 标题会通过computed自动更新
    // useDynamicTitle会自动处理标签页标题和浏览器标题的同步
  }
);
```

## 数据模型

### 标签页数据结构
```javascript
interface TagNavItem {
  menuName: string;      // 菜单ID，用作唯一标识
  menuTitle: string;     // 显示标题
  menuQuery?: object;    // 路由查询参数
  menuIcon?: string;     // 图标
  fromMenuName?: string; // 来源菜单
  fromMenuQuery?: object; // 来源查询参数
}
```

### 用户Store状态
```javascript
interface UserStoreState {
  tagNav: TagNavItem[];           // 标签页列表
  menuRouterList: MenuRouter[];   // 菜单路由列表
  keepAliveIncludes: string[];    // 缓存组件列表
}
```

## 错误处理

### 1. 标签页操作失败
```javascript
try {
  userStore.closeTagNav(existingTag.menuName, false);
} catch (error) {
  console.error('关闭标签页失败:', error);
  // 继续执行跳转，不阻塞用户操作
  navigateToEdit(data);
}
```

### 2. 路由跳转失败
```javascript
try {
  router.push({ path: '/baojia/add', query: { id: data.id } });
} catch (error) {
  console.error('路由跳转失败:', error);
  message.error('页面跳转失败，请重试');
}
```

### 3. 数据加载失败
```javascript
try {
  const res = await templateApi.detail(route.query.id);
  // 处理数据...
} catch (error) {
  console.error('数据加载失败:', error);
  message.error('数据加载失败，请检查网络连接');
  // 保持当前状态，不清空表单
}
```

## 性能考虑

### 1. 标签页检测优化
- 使用缓存避免重复计算
- 限制标签页数量，避免内存泄漏

### 2. 路由跳转优化
- 使用延迟跳转避免竞态条件
- 复用已存在的标签页，减少组件重新创建

### 3. 内存管理
- 及时清理不需要的标签页
- 正确处理组件卸载时的资源清理

## 兼容性

### 浏览器兼容性
- 支持现代浏览器的标签页API
- 兼容Vue 3和Vue Router 4

### 现有功能兼容性
- 不影响现有的标签页功能
- 保持与现有路由系统的兼容性
- 确保缓存机制正常工作