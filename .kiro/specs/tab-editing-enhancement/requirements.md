# 标签页编辑功能增强需求文档

## 介绍

本需求旨在解决报价单和模板编辑功能中的标签页管理和数据保护问题，按照简单有效的实现方式，优先解决核心问题。

## 需求

### 需求1：数据保护提示机制

**用户故事：** 作为用户，我希望在已有编辑标签页打开的情况下，点击编辑其他记录时，系统能够提示我可能丢失未保存的数据，让我选择是否覆盖，这样我就不会意外丢失重要工作。

#### 验收标准
1. WHEN 用户已打开报价单编辑标签页，再次点击列表中任意"编辑"或"新建"按钮 THEN 系统应显示"检测到未保存数据"提示框
2. WHEN 用户已打开模板编辑标签页，再次点击列表中任意"编辑"或"新建"按钮 THEN 系统应显示"检测到未保存数据"提示框
3. WHEN 用户在提示框中选择"覆盖数据" THEN 系统应关闭现有编辑标签页，然后重新打开新的编辑页面
4. WHEN 用户在提示框中选择"取消" THEN 系统应保持当前状态不变，不进行任何跳转
5. IF 没有打开的编辑标签页 THEN 系统应直接跳转到编辑页面，不显示提示

### 需求2：标签页重复问题修复

**用户故事：** 作为用户，我希望在模板列表页面刷新后，不会出现重复的标签页，这样我就能有清晰的页面导航体验。

#### 验收标准
1. WHEN 用户在模板列表页面按F5刷新 THEN 系统应只显示一个模板列表标签页
2. WHEN 用户在报价列表页面按F5刷新 THEN 系统应只显示一个报价列表标签页
3. WHEN 刷新后的标签页被点击 THEN 应能正确跳转到对应页面
4. WHEN 刷新后查看左侧菜单 THEN 对应的菜单项应正确高亮显示
5. IF 刷新前有多个标签页 THEN 刷新后应保持其他标签页不变，只确保当前页面标签页唯一

### 需求3：编辑页面动态标题显示

**用户故事：** 作为用户，我希望编辑页面的标题能够准确反映当前的操作状态，这样我就能清楚地知道当前在进行新建还是编辑操作。

#### 验收标准
1. WHEN 用户点击"新建"按钮进入编辑页面 THEN 编辑页面标签页标题应显示"新建报价单"或"新建模板"
2. WHEN 用户点击"编辑"按钮进入编辑页面 THEN 编辑页面标签页标题应显示"修改报价单"或"修改模板"
3. WHEN 用户通过覆盖操作从新建切换到编辑 THEN 编辑页面标题应自动更新为"修改"状态
4. WHEN 用户通过覆盖操作从编辑切换到新建 THEN 编辑页面标题应自动更新为"新建"状态
5. WHEN 编辑页面标题发生变化 THEN 浏览器标题栏也应同步更新
6. WHEN 用户在列表页面操作 THEN 列表页面的标签页标题应保持不变（如"报价列表"、"模板列表"）