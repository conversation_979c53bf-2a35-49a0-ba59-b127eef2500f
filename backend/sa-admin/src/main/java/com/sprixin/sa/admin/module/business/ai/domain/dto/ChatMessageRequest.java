package com.sprixin.sa.admin.module.business.ai.domain.dto;

import java.util.List;

/**
 * 聊天消息请求DTO
 */
public class ChatMessageRequest {

    /**
     * 会话ID，用于关联会话历史
     */
    private String sessionId;

    /**
     * 用户消息
     */
    private String message;
    
    /**
     * 模型参数设置
     */
    private ModelParameters parameters;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public ModelParameters getParameters() {
        return parameters;
    }

    public void setParameters(ModelParameters parameters) {
        this.parameters = parameters;
    }

    /**
     * 模型参数设置
     */
    public static class ModelParameters {
        /**
         * 温度参数，控制创造性
         */
        private Float temperature;
        
        /**
         * 最大生成token数
         */
        private Integer maxTokens;
        
        /**
         * 是否启用搜索增强
         */
        private Boolean enableSearch;
        
        /**
         * top_p参数，控制输出多样性
         */
        private Float topP;
        
        /**
         * 频率惩罚，避免重复
         */
        private Float frequencyPenalty;
        
        /**
         * 存在惩罚，避免重复
         */
        private Float presencePenalty;
        
        /**
         * 是否使用流式响应
         */
        private Boolean useStreamResponse;

        public Float getTemperature() {
            return temperature;
        }

        public void setTemperature(Float temperature) {
            this.temperature = temperature;
        }

        public Integer getMaxTokens() {
            return maxTokens;
        }

        public void setMaxTokens(Integer maxTokens) {
            this.maxTokens = maxTokens;
        }

        public Boolean getEnableSearch() {
            return enableSearch;
        }

        public void setEnableSearch(Boolean enableSearch) {
            this.enableSearch = enableSearch;
        }
        
        public Float getTopP() {
            return topP;
        }

        public void setTopP(Float topP) {
            this.topP = topP;
        }

        public Float getFrequencyPenalty() {
            return frequencyPenalty;
        }

        public void setFrequencyPenalty(Float frequencyPenalty) {
            this.frequencyPenalty = frequencyPenalty;
        }

        public Float getPresencePenalty() {
            return presencePenalty;
        }

        public void setPresencePenalty(Float presencePenalty) {
            this.presencePenalty = presencePenalty;
        }
        
        public Boolean getUseStreamResponse() {
            return useStreamResponse;
        }

        public void setUseStreamResponse(Boolean useStreamResponse) {
            this.useStreamResponse = useStreamResponse;
        }
    }
} 