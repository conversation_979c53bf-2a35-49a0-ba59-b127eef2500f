package com.sprixin.sa.admin.module.business.ai.domain.dto;

import java.util.List;

/**
 * 聊天消息响应DTO
 */
public class ChatMessageResponse {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * AI响应消息
     */
    private String message;

    /**
     * 会话消息历史记录
     */
    private List<ChatMessage> history;

    /**
     * 请求ID，用于追踪
     */
    private String requestId;

    /**
     * 使用的token数量
     */
    private Integer tokenUsage;

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<ChatMessage> getHistory() {
        return history;
    }

    public void setHistory(List<ChatMessage> history) {
        this.history = history;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Integer getTokenUsage() {
        return tokenUsage;
    }

    public void setTokenUsage(Integer tokenUsage) {
        this.tokenUsage = tokenUsage;
    }

    /**
     * 聊天消息实体
     */
    public static class ChatMessage {
        /**
         * 角色：user(用户) 或 assistant(AI助手)
         */
        private String role;
        
        /**
         * 消息内容
         */
        private String content;
        
        /**
         * 消息时间戳
         */
        private Long timestamp;

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public Long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(Long timestamp) {
            this.timestamp = timestamp;
        }
    }
} 