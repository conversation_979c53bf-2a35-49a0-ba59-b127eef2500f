package com.sprixin.sa.admin.module.business.ai.service;

import com.sprixin.sa.admin.module.business.ai.domain.dto.ChatMessageRequest;
import com.sprixin.sa.admin.module.business.ai.domain.dto.ChatMessageResponse;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * AI助手服务接口
 */
public interface AiAssistantService {

    /**
     * 处理用户消息
     * @param request 聊天消息请求
     * @return 聊天消息响应
     */
    ChatMessageResponse processMessage(ChatMessageRequest request);
    
    /**
     * 流式处理消息
     * @param request 聊天消息请求
     * @param emitter SSE事件发射器
     */
    void processMessageStream(ChatMessageRequest request, SseEmitter emitter);

    /**
     * 获取会话历史记录
     * @param sessionId 会话ID
     * @return 聊天历史记录
     */
    ChatMessageResponse getSessionHistory(String sessionId);

    /**
     * 创建新会话
     * @return 新会话ID
     */
    String createNewSession();
} 