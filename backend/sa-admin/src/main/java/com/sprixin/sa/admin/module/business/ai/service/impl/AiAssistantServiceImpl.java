package com.sprixin.sa.admin.module.business.ai.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sprixin.sa.admin.module.business.ai.domain.dto.ChatMessageRequest;
import com.sprixin.sa.admin.module.business.ai.domain.dto.ChatMessageResponse;
import com.sprixin.sa.admin.module.business.ai.service.AiAssistantService;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
// 已删除冲突的导入
import org.apache.commons.lang3.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * AI助手服务实现类
 */
@Service
public class AiAssistantServiceImpl implements AiAssistantService {

    private static final Logger logger = LoggerFactory.getLogger(AiAssistantServiceImpl.class);
    
    // 存储会话历史的内存缓存，实际项目中建议使用Redis等缓存
    private final Map<String, List<ChatMessageResponse.ChatMessage>> sessionHistoryMap = new ConcurrentHashMap<>();
    
    // 用于异步处理的线程池
    private final ExecutorService executorService = Executors.newCachedThreadPool();

    @Value("${qwen.api.url}")
    private String qwenApiUrl;

    @Value("${qwen.api.key}")
    private String qwenApiKey;

    @Value("${qwen.api.model}")
    private String modelName;

    // 删除重复声明的RestTemplate
    // private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public ChatMessageResponse processMessage(ChatMessageRequest request) {
        String sessionId = request.getSessionId();
        if (sessionId == null || sessionId.isEmpty()) {
            sessionId = createNewSession();
        }

        // 获取会话历史
        List<ChatMessageResponse.ChatMessage> history = sessionHistoryMap.getOrDefault(
                sessionId, new ArrayList<>());

        // 创建用户消息并添加到历史
        ChatMessageResponse.ChatMessage userMessage = new ChatMessageResponse.ChatMessage();
        userMessage.setRole("user");
        userMessage.setContent(request.getMessage());
        userMessage.setTimestamp(System.currentTimeMillis());
        history.add(userMessage);

        // 调用Qwen3-30B API
        String aiResponse = callQwenApi(history, request.getParameters(), false, null);

        // 创建AI回复消息并添加到历史
        ChatMessageResponse.ChatMessage assistantMessage = new ChatMessageResponse.ChatMessage();
        assistantMessage.setRole("assistant");
        assistantMessage.setContent(aiResponse);
        assistantMessage.setTimestamp(System.currentTimeMillis());
        history.add(assistantMessage);

        // 更新会话历史
        sessionHistoryMap.put(sessionId, history);

        // 构建响应
        ChatMessageResponse response = new ChatMessageResponse();
        response.setSessionId(sessionId);
        response.setMessage(aiResponse);
        response.setHistory(new ArrayList<>(history));  // 创建新的ArrayList防止引用问题
        response.setRequestId(UUID.randomUUID().toString());
        
        // 打印完整的响应对象，用于调试
        try {
            logger.info("完整响应对象: {}", JSON.toJSONString(response));
        } catch (Exception e) {
            logger.error("响应对象序列化失败", e);
        }
        
        return response;
    }
    
    /**
     * 流式处理消息
     * @param request 聊天消息请求
     * @param emitter SSE事件发射器
     */
    @Override
    public void processMessageStream(ChatMessageRequest request, SseEmitter emitter) {
        // 在线程池中异步处理，避免阻塞主线程
        executorService.execute(() -> {
            try {
                // 获取或创建会话
                String sessionId = request.getSessionId();
                if (sessionId == null || sessionId.isEmpty()) {
                    sessionId = createNewSession();
                }
                
                // 获取会话历史
                List<ChatMessageResponse.ChatMessage> history = sessionHistoryMap.getOrDefault(
                        sessionId, new ArrayList<>());
                
                // 添加用户消息到历史
                ChatMessageResponse.ChatMessage userMessage = new ChatMessageResponse.ChatMessage();
                userMessage.setRole("user");
                userMessage.setContent(request.getMessage());
                userMessage.setTimestamp(System.currentTimeMillis());
                history.add(userMessage);
                
                // 发送初始事件，表示开始流式响应
                emitter.send(SseEmitter.event()
                        .name("message")
                        .data("开始生成回复...", MediaType.TEXT_PLAIN));
                
                // 创建AI回复消息
                ChatMessageResponse.ChatMessage assistantMessage = new ChatMessageResponse.ChatMessage();
                assistantMessage.setRole("assistant");
                assistantMessage.setContent("");
                assistantMessage.setTimestamp(System.currentTimeMillis());
                
                // 调用流式API并实时发送响应
                final StringBuilder fullResponse = new StringBuilder();
                
                callQwenApi(history, request.getParameters(), true, (chunk) -> {
                    try {
                        // 添加新的内容到完整响应
                        fullResponse.append(chunk);
                        
                        // 发送数据事件
                        emitter.send(SseEmitter.event()
                                .name("message")
                                .data(chunk, MediaType.TEXT_PLAIN));
                    } catch (IOException e) {
                        logger.error("发送SSE事件失败", e);
                    }
                });
                
                // 更新AI回复消息内容并添加到历史
                assistantMessage.setContent(fullResponse.toString());
                history.add(assistantMessage);
                
                // 更新会话历史
                sessionHistoryMap.put(sessionId, history);
                
                // 发送完成事件
                emitter.send(SseEmitter.event()
                        .name("done")
                        .data("", MediaType.TEXT_PLAIN));
                
                // 完成SSE连接
                emitter.complete();
                
            } catch (Exception e) {
                logger.error("流式处理消息失败", e);
                try {
                    // 发送错误事件
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("处理消息出错: " + e.getMessage(), MediaType.TEXT_PLAIN));
                    emitter.complete();
                } catch (IOException ex) {
                    logger.error("发送错误事件失败", ex);
                }
            }
        });
    }
    
    /**
     * 调用通义千问API获取流式响应
     * @param history 聊天历史
     * @param parameters 参数
     * @param sessionId 会话ID
     * @param emitter SSE事件发射器
     */
    // 删除不需要的方法
    // private void callQwenApiStream(List<ChatMessageResponse.ChatMessage> history, Map<String, Object> parameters, String sessionId, SseEmitter emitter) {
    // ... 删除整个方法 ...
    // }

    @Override
    public ChatMessageResponse getSessionHistory(String sessionId) {
        List<ChatMessageResponse.ChatMessage> history = sessionHistoryMap.getOrDefault(
                sessionId, new ArrayList<>());
        
        ChatMessageResponse response = new ChatMessageResponse();
        response.setSessionId(sessionId);
        response.setHistory(history);
        return response;
    }

    @Override
    public String createNewSession() {
        String sessionId = UUID.randomUUID().toString();
        sessionHistoryMap.put(sessionId, new ArrayList<>());
        return sessionId;
    }

    /**
     * 调用Qwen3-30B模型API
     * @param history 聊天历史
     * @param parameters 模型参数
     * @param stream 是否使用流式响应
     * @param streamCallback 流式响应回调函数
     * @return AI响应内容（非流式模式下返回完整响应，流式模式下返回null）
     */
    private String callQwenApi(List<ChatMessageResponse.ChatMessage> history, 
                              ChatMessageRequest.ModelParameters parameters,
                              boolean stream,
                              StreamCallback streamCallback) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(qwenApiUrl);
            
            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Authorization", "Bearer " + qwenApiKey);
            
            // 构建请求体
            JSONObject requestBody = new JSONObject();
            // 使用qwen配置的模型名称
            requestBody.put("model", modelName);
            
            // 转换消息历史格式
            List<Map<String, String>> messages = new ArrayList<>();
            for (ChatMessageResponse.ChatMessage msg : history) {
                Map<String, String> messageMap = new HashMap<>();
                messageMap.put("role", msg.getRole());
                messageMap.put("content", msg.getContent());
                messages.add(messageMap);
            }
            requestBody.put("messages", messages);
            
            // 设置生成参数
            if (parameters != null) {
                if (parameters.getTemperature() != null) {
                    requestBody.put("temperature", parameters.getTemperature());
                } else {
                    // 默认温度
                    requestBody.put("temperature", 0.7);
                }
                if (parameters.getMaxTokens() != null) {
                    requestBody.put("max_tokens", parameters.getMaxTokens());
                } else {
                    // 默认最大token数
                    requestBody.put("max_tokens", 2000);
                }
                // 添加其他可能的参数
                if (parameters.getEnableSearch() != null && parameters.getEnableSearch()) {
                    // 如果启用搜索增强，可以在这里添加相关参数
                    requestBody.put("enable_search", true);
                }
                // 添加新的参数
                if (parameters.getTopP() != null) {
                    requestBody.put("top_p", parameters.getTopP());
                }
                if (parameters.getFrequencyPenalty() != null) {
                    requestBody.put("frequency_penalty", parameters.getFrequencyPenalty());
                }
                if (parameters.getPresencePenalty() != null) {
                    requestBody.put("presence_penalty", parameters.getPresencePenalty());
                }
            } else {
                // 如果没有设置参数，添加默认值
                requestBody.put("temperature", 0.7);
                requestBody.put("max_tokens", 2000);
            }
            
            // 指定返回格式为markdown
            JSONObject responseFormat = new JSONObject();
            responseFormat.put("type", "text");
            requestBody.put("response_format", responseFormat);
            
            // 添加系统提示，要求模型以markdown格式回复
            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", "请以Markdown格式回复所有问题，适当使用以下Markdown语法来组织内容，使回复更加清晰易读：\n" +
                    "1. 使用标题: # 一级标题, ## 二级标题, ### 三级标题\n" +
                    "2. 强调: **加粗**, *斜体*\n" +
                    "3. 列表: 无序列表使用 - 或 * 开头, 有序列表使用 1. 2. 3. 格式\n" +
                    "4. 表格: 使用 | 分隔列, 使用 --- 分隔表头和内容\n" +
                    "5. 代码: 使用 ```language 和 ``` 包裹代码块, 或使用 `代码` 标记行内代码\n" +
                    "6. 引用: 使用 > 开头表示引用内容\n" +
                    "7. 链接: [链接文本](URL)\n" +
                    "8. 分隔线: 使用 --- 或 *** 创建分隔线\n\n" +
                    "对于每个回复，应该保持结构清晰，逻辑连贯，根据内容类型适当使用上述Markdown元素。对于代码，务必使用代码块并标注正确的编程语言以便语法高亮。");
            messages.add(0, systemMessage); // 在消息列表开头插入系统消息
            
            // 设置stream参数
            requestBody.put("stream", stream);
            
            // 记录请求详情
            logger.info("调用AI API, URL: {}, 流式模式: {}", qwenApiUrl, stream);
            logger.debug("请求体: {}", requestBody.toJSONString());
            
            StringEntity entity = new StringEntity(requestBody.toJSONString(), StandardCharsets.UTF_8);
            httpPost.setEntity(entity);
            
            // 发送请求
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                int statusCode = response.getStatusLine().getStatusCode();
                logger.info("API响应状态码: {}", statusCode);
                
                HttpEntity responseEntity = response.getEntity();
                
                // 流式响应处理
                if (stream && streamCallback != null) {
                    try (InputStream inputStream = responseEntity.getContent();
                         BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                        
                        String line;
                        while ((line = reader.readLine()) != null) {
                            if (line.isEmpty()) continue;
                            
                            if (line.startsWith("data:")) {
                                String data = line.substring(5).trim();
                                
                                // 跳过[DONE]标记
                                if ("[DONE]".equals(data)) continue;
                                
                                try {
                                    JSONObject jsonData = JSON.parseObject(data);
                                    String chunk = extractContentFromStreamResponse(jsonData);
                                    if (chunk != null && !chunk.isEmpty()) {
                                        // 检查是否是错误消息
                                        if (chunk.startsWith("ERROR:")) {
                                            // 发送错误事件
                                            streamCallback.onChunk("\n\n⚠️ " + chunk.substring(6) + "\n\n请联系系统管理员检查AI服务配置。");
                                            // 结束流式响应
                                            break;
                                        } else {
                                            streamCallback.onChunk(chunk);
                                        }
                                    }
                                } catch (Exception e) {
                                    logger.error("解析流式响应数据失败: {}", data, e);
                                }
                            }
                        }
                    }
                    return null; // 流式模式下不返回内容
                } 
                // 非流式响应处理
                else {
                    String responseString = EntityUtils.toString(responseEntity);
                    
                    // 记录完整的API响应以便调试
                    logger.info("API完整响应: {}", responseString);
                    
                    // 解析响应
                    JSONObject jsonResponse = JSON.parseObject(responseString);
                    if (jsonResponse.containsKey("error")) {
                        logger.error("Error from Qwen API: {}", jsonResponse.getString("error"));
                        return "抱歉，AI助手暂时无法回答，请稍后再试。错误信息: " + jsonResponse.getString("error");
                    }
                    
                    try {
                        // 尝试解析不同的响应格式
                        if (jsonResponse.containsKey("choices")) {
                            Object choices = jsonResponse.get("choices");
                            logger.debug("choices类型: {}", choices.getClass().getName());
                            
                            // 情况1: choices是数组
                            if (choices instanceof JSONArray) {
                                JSONArray choicesArray = jsonResponse.getJSONArray("choices");
                                if (choicesArray != null && !choicesArray.isEmpty()) {
                                    JSONObject firstChoice = choicesArray.getJSONObject(0);
                                    if (firstChoice.containsKey("message")) {
                                        String content = firstChoice.getJSONObject("message").getString("content");
                                        logger.info("成功解析AI回复 (数组格式)");
                                        return content;
                                    }
                                }
                            }
                            // 情况2: choices是对象
                            else if (choices instanceof JSONObject) {
                                JSONObject choicesObj = (JSONObject) choices;
                                if (choicesObj.containsKey("message")) {
                                    String content = choicesObj.getJSONObject("message").getString("content");
                                    logger.info("成功解析AI回复 (对象格式)");
                                    return content;
                                }
                            }
                        }
                        
                        // 情况3: 其他格式，尝试找到响应内容的其他路径
                        if (jsonResponse.containsKey("output")) {
                            String content = jsonResponse.getString("output");
                            logger.info("成功解析AI回复 (output格式)");
                            return content;
                        }
                        
                        if (jsonResponse.containsKey("response")) {
                            String content = jsonResponse.getString("response");
                            logger.info("成功解析AI回复 (response格式)");
                            return content;
                        }
                        
                        // 直接返回整个响应内容以便调试
                        logger.warn("未能识别的API响应格式");
                        return "AI响应格式未知，原始响应: " + responseString;
                        
                    } catch (Exception e) {
                        logger.error("Error parsing API response", e);
                        return "抱歉，解析AI回复时出错，请稍后再试。错误详情: " + e.getMessage();
                    }
                }
            }
        } catch (IOException e) {
            logger.error("Error calling Qwen API", e);
            return "抱歉，AI助手服务出现网络故障，请稍后再试。错误详情: " + e.getMessage();
        }
    }
    
    /**
     * 准备通义千问API请求体
     * @param history 聊天历史
     * @param parameters 模型参数
     * @return 请求体Map
     */
    private Map<String, Object> prepareQwenRequestBody(List<ChatMessageResponse.ChatMessage> history, Map<String, Object> parameters) {
        Map<String, Object> requestBody = new HashMap<>();
        
        // 设置模型名称
        requestBody.put("model", modelName);
        
        // 转换消息历史格式
        List<Map<String, String>> messages = new ArrayList<>();
        for (ChatMessageResponse.ChatMessage msg : history) {
            Map<String, String> messageMap = new HashMap<>();
            messageMap.put("role", msg.getRole());
            messageMap.put("content", msg.getContent());
            messages.add(messageMap);
        }
        requestBody.put("messages", messages);
        
        // 设置生成参数
        if (parameters != null) {
            if (parameters.containsKey("temperature")) {
                requestBody.put("temperature", parameters.get("temperature"));
            } else {
                // 默认温度
                requestBody.put("temperature", 0.7);
            }
            if (parameters.containsKey("maxTokens")) {
                requestBody.put("max_tokens", parameters.get("maxTokens"));
            } else {
                // 默认最大token数
                requestBody.put("max_tokens", 2000);
            }
        } else {
            // 如果没有设置参数，添加默认值
            requestBody.put("temperature", 0.7);
            requestBody.put("max_tokens", 2000);
        }
        
        return requestBody;
    }
    
    /**
     * 从流式响应中提取内容
     */
    private String extractContentFromStreamResponse(JSONObject jsonData) {
        try {
            // 记录完整的响应数据，用于调试
            logger.debug("流式响应原始数据: {}", jsonData);
            
            // 检查是否有错误响应
            if (jsonData.containsKey("error")) {
                JSONObject error = jsonData.getJSONObject("error");
                String errorMessage = error.getString("message");
                logger.error("API返回错误: {}", errorMessage);
                return "ERROR: " + errorMessage;
            }
            
            // 尝试不同的响应格式
            if (jsonData.containsKey("choices")) {
                JSONArray choices = jsonData.getJSONArray("choices");
                if (choices != null && !choices.isEmpty()) {
                    JSONObject choice = choices.getJSONObject(0);
                    
                    // 通义千问特定格式 - choices[0].delta.reasoning_content
                    if (choice.containsKey("delta")) {
                        JSONObject delta = choice.getJSONObject("delta");
                        if (delta.containsKey("reasoning_content")) {
                            String content = delta.getString("reasoning_content");
                            logger.debug("从reasoning_content提取内容: {}", content);
                            return content;
                        }
                        if (delta.containsKey("content")) {
                            String content = delta.getString("content");
                            logger.debug("从delta.content提取内容: {}", content);
                            return content;
                        }
                    }
                    
                    // OpenAI格式
                    if (choice.containsKey("delta")) {
                        JSONObject delta = choice.getJSONObject("delta");
                        if (delta.containsKey("content")) {
                            String content = delta.getString("content");
                            logger.debug("从delta.content提取内容: {}", content);
                            return content;
                        }
                    }
                    
                    // 通义千问格式
                    if (choice.containsKey("text")) {
                        String content = choice.getString("text");
                        logger.debug("从text提取内容: {}", content);
                        return content;
                    }
                    
                    // 其他可能的格式
                    if (choice.containsKey("content")) {
                        String content = choice.getString("content");
                        logger.debug("从content提取内容: {}", content);
                        return content;
                    }
                }
            }
            
            // 直接尝试获取content字段
            if (jsonData.containsKey("content")) {
                String content = jsonData.getString("content");
                logger.debug("从顶层content提取内容: {}", content);
                return content;
            }
            
            // 尝试其他可能的路径
            if (jsonData.containsKey("text")) {
                String content = jsonData.getString("text");
                logger.debug("从顶层text提取内容: {}", content);
                return content;
            }
            
            if (jsonData.containsKey("message") && jsonData.getJSONObject("message").containsKey("content")) {
                String content = jsonData.getJSONObject("message").getString("content");
                logger.debug("从message.content提取内容: {}", content);
                return content;
            }
            
            logger.warn("无法从流式响应中提取内容: {}", jsonData);
            return "";
        } catch (Exception e) {
            logger.error("提取流式响应内容出错", e);
            return "";
        }
    }
    
    /**
     * 将AI回复的原始内容解析为Markdown格式
     * @param content 原始AI回复内容
     * @return 格式化为Markdown的内容
     */
    private String parseToMarkdown(String content) {
        // 如果内容为空，直接返回
        if (content == null || content.isEmpty()) {
            return "";
        }
        
        // 由于API已经配置为返回Markdown格式，这里只做基本检查和清理
        
        // 移除可能的多余空行
        content = content.replaceAll("\n{3,}", "\n\n");
        
        // 确保代码块格式正确
        content = content.replaceAll("```\\s+([a-zA-Z0-9]+)", "```$1");
        
        logger.info("已验证AI回复的Markdown格式");
        return content;
    }
    
    /**
     * 流式回调接口
     */
    private interface StreamCallback {
        /**
         * 接收流式响应的回调方法
         * @param chunk 响应片段
         */
        void onChunk(String chunk);
    }
} 