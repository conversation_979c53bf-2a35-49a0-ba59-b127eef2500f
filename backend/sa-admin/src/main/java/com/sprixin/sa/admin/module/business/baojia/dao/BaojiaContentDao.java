package com.sprixin.sa.admin.module.business.baojia.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.admin.module.business.baojia.domain.entity.BaojiaContentEntity;
import com.sprixin.sa.admin.module.business.baojia.domain.form.BaojiaContentQueryForm;
import com.sprixin.sa.admin.module.business.baojia.domain.vo.BaojiaContentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 模板及报价数据表 Dao
 */
@Mapper
public interface BaojiaContentDao extends BaseMapper<BaojiaContentEntity> {
    List<BaojiaContentVO> queryContent(Page page, @Param("queryForm") BaojiaContentQueryForm queryForm);
} 