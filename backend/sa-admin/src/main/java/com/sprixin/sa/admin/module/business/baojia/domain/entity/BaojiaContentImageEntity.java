package com.sprixin.sa.admin.module.business.baojia.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 模板及报价图片存储表 实体类
 */
@Data
@TableName("y_baojia_content_image")
public class BaojiaContentImageEntity {

    /** ID */
    @TableId
    private String id;

    /** 内容 */
    private byte[] content;

    /** 创建时间 */
    private LocalDateTime createTime;
} 