package com.sprixin.sa.admin.module.business.baojia.domain.form;

import com.sprixin.sa.base.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * 模板及报价数据表 查询表单
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaojiaContentQueryForm extends PageParam {
    /** 关键词（模板名称/业务线/产品类型） */
    @Length(max = 255, message = "关键词最多255字符")
    private String keyword;

    /** 内容类型（1:模板;2:报价单） */
    private Integer contentType;

    /** 生成类型（1:人工;2:AI） */
    private Integer generateType;

    /** 所属人ID */
    private Long userId;

    /** 禁用状态 */
    private Boolean disabledFlag;

    /** 删除状态 */
    private Boolean deletedFlag;

    /** 列表范围（my:我的报价;all:全部报价） */
    @Length(max = 10, message = "列表范围参数最多10字符")
    private String listScope;

    /** 产品类型ID */
    private Long productTypeId;

    /** 产品类型名称 */
    @Length(max = 100, message = "产品类型名称最多100字符")
    private String productTypeName;
} 