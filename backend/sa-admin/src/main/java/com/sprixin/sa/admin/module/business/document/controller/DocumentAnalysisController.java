package com.sprixin.sa.admin.module.business.document.controller;

import com.sprixin.sa.base.common.controller.SupportBaseController;
import com.sprixin.sa.base.common.domain.ResponseDTO;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.base.common.util.SmartRequestUtil;
import com.sprixin.sa.base.constant.SwaggerTagConst;
import com.sprixin.sa.admin.module.business.document.domain.form.DocumentAnalysisResultForm;
import com.sprixin.sa.admin.module.business.document.domain.form.DocumentAnalysisQueryForm;
import com.sprixin.sa.admin.module.business.document.domain.vo.DocumentAnalysisResultVO;
import com.sprixin.sa.admin.module.business.document.service.IDocumentAnalysisService;
import com.sprixin.sa.admin.util.ExcelReader;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.*;
import lombok.extern.slf4j.Slf4j;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import org.springframework.web.bind.annotation.*;
import com.alibaba.fastjson.JSON;
import java.util.stream.Collectors;

import java.io.InputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.ArrayList;

/**
 * 文档分析控制器
 */
@Slf4j
@RestController
@Tag(name = SwaggerTagConst.Support.DOCUMENT)
public class DocumentAnalysisController extends SupportBaseController {
    
    @Resource
    private IDocumentAnalysisService documentAnalysisService;
    
    @Operation(summary = "分析文档")
    @PostMapping("/document/analyze")
    public ResponseDTO<DocumentAnalysisResultVO> analyzeDocument(@RequestBody @Valid DocumentAnalysisResultForm form) {
        return documentAnalysisService.analyzeDocument(form, SmartRequestUtil.getRequestUser());
    }
    
    @Operation(summary = "获取分析结果")
    @GetMapping("/document/analysis/{id}")
    public ResponseDTO<DocumentAnalysisResultVO> getAnalysisResult(@PathVariable Long id) {
        return documentAnalysisService.getAnalysisResultById(id);
    }
    
    @Operation(summary = "更新分析结果")
    @PostMapping(value = "/document/analysis/{id}", produces = "application/json;charset=UTF-8")
    public ResponseDTO<String> updateAnalysisResult(@PathVariable Long id, @RequestBody List<Map<String, Object>> analysisResultList) {
        return documentAnalysisService.updateAnalysisResult(id, analysisResultList);
    }

    @Operation(summary = "查询分析结果列表")
    @PostMapping("/document/analysis/query")
    public ResponseDTO<PageResult<DocumentAnalysisResultVO>> queryAnalysisResults(@RequestBody @Valid DocumentAnalysisQueryForm queryForm) {
        return documentAnalysisService.queryAnalysisResults(queryForm);
    }

    @Operation(summary = "删除分析结果")
    @DeleteMapping("/document/analysis/{id}")
    public ResponseDTO<String> deleteAnalysisResult(@PathVariable Long id) {
        return documentAnalysisService.deleteAnalysisResult(id);
    }

    @Operation(summary = "导出报价单")
    @GetMapping("/document/analysis/export/{id}")
    public void exportQuotation(@PathVariable Long id, HttpServletResponse response) {
        // 定义资源变量，以便在finally块中关闭
        InputStream templateStream = null;
        Workbook workbook = null;
        
        try {

            // 3. 查询分析结果数据
            log.info("开始查询分析结果数据, ID: {}", id);
            ResponseDTO<DocumentAnalysisResultVO> responseDTO = documentAnalysisService.getAnalysisResultById(id);
            log.info("查询结果响应码: {}, 消息: {}", responseDTO.getCode(), responseDTO.getMsg());
            
            DocumentAnalysisResultVO result = responseDTO.getData();
            if (result == null || result.getAnalysisResult() == null) {
                log.error("未找到分析结果数据，ID: {}", id);
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write("未找到分析结果数据");
                return;
            }
            log.info("成功获取分析结果数据, 文件名: {}, 分析提示词: {}", result.getFileName(), result.getAnalysisPrompt());
            
            // 4. 解析数据 数据清洗
            List<Map<String, Object>> allData;
            try {
                allData = JSON.parseObject(result.getAnalysisResult(), new com.alibaba.fastjson.TypeReference<List<Map<String, Object>>>(){});
                log.info("成功解析JSON数据，共 {} 条记录", allData.size());
                
                // 数据清洗 - 移除null或空对象
                allData = allData.stream()
                    .filter(item -> item != null && !item.isEmpty())
                    .collect(Collectors.toList());
                
                // 数据清洗 - 确保所有字段都有值（包括空字符串）
                allData.forEach(item -> {
                    // 设置必要字段的默认值，如果不存在
                    if (!item.containsKey("deviceName") || item.get("deviceName") == null) {
                        item.put("deviceName", "");
                    }
                    if (!item.containsKey("config") || item.get("config") == null) {
                        item.put("config", "");
                    }
                    if (!item.containsKey("price") || item.get("price") == null) {
                        item.put("price", "");
                    }
                    if (!item.containsKey("quantity") || item.get("quantity") == null) {
                        item.put("quantity", "1");
                    }
                    if (!item.containsKey("remark") || item.get("remark") == null) {
                        item.put("remark", "");
                    }
                });
                
                log.info("数据清洗完成，剩余 {} 条有效记录", allData.size());
            } catch (Exception e) {
                log.error("分析结果数据格式错误: {}", e.getMessage(), e);
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write("分析结果数据格式错误: " + e.getMessage());
                return;
            }
            
            // 5. 分类数据
            List<Map<String, Object>> hardwareList = allData.stream()
                .filter(m -> {
                    // 明确检查category字段是否为"硬件部分"
                    Object category = m.get("category");
                    return category != null && "硬件部分".equals(category.toString().trim());
                })
                .collect(Collectors.toList());
                
            List<Map<String, Object>> softwareList = allData.stream()
                .filter(m -> {
                    // 明确检查category字段是否为"软件部分"
                    Object category = m.get("category");
                    return category != null && "软件部分".equals(category.toString().trim());
                })
                .collect(Collectors.toList());
                
            // 增加测风塔数据分类
            List<Map<String, Object>> towerList = allData.stream()
                .filter(m -> {
                    // 明确检查category字段是否为"测风塔"或"实时测风塔"
                    Object category = m.get("category");
                    return category != null && 
                          ("测风塔".equals(category.toString().trim()) || 
                           "实时测风塔".equals(category.toString().trim()));
                })
                .collect(Collectors.toList());
                
            log.info("硬件部分: {} 条记录, 软件部分: {} 条记录, 测风塔: {} 条记录", 
                hardwareList.size(), softwareList.size(), towerList.size());

            // 检查分类后的数据总数是否与原始数据总数不一致，如果有未分类的数据，进行日志记录

            if (hardwareList.isEmpty() && softwareList.isEmpty() && towerList.isEmpty()) {
                log.warn("没有可导出的数据");
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write("没有可导出的数据");
                return;
            }
            // 6. 创建报价单数据
            ExcelReader.QuotationData quotationData = new ExcelReader.QuotationData();
            
            // 设置报价单头部信息
            ExcelReader.QuotationHeader header = new ExcelReader.QuotationHeader();
            header.setCompanyName("国能日新科技股份有限公司");
            header.setTitle("风电功率预测系统V1.0报价明细");
            header.setServiceDescription("感谢您的询价，我公司将为您提供优质的服务！24/小时服务电话：400-6688-760");
            header.setWebsite("www.spriwin.com");
            header.setQuotationDate("2025/3/26");
            header.setAddress("北京市海淀区西三环北路甲2号院中关村国防科技园2号楼");
            header.setProjectName("Myanmar Thu Pyay");
            header.setContactPerson("薛占伍");
            header.setCurrency("人民币元 RMB");
            header.setContactPhone("18061632558");
            header.setContactEmail("<EMAIL>");
            quotationData.setHeader(header);

            // 设置标题
            quotationData.setHardwareTitle("风电功率预测系统V1.0-硬件部分"); // 硬件部分标题
            quotationData.setSoftwareTitle("风电功率预测系统V1.0-软件部分"); // 软件部分标题
            quotationData.setWindTowerTitle("风电功率预测系统V1.0-实时测风塔"); // 测风塔部分标题

            // 转换并设置数据项
            AtomicInteger serialNumber = new AtomicInteger(1);
            List<ExcelReader.HardwareItem> hardwareItems = hardwareList.stream()
                .map(item -> {
                    ExcelReader.HardwareItem hardwareItem = new ExcelReader.HardwareItem();
                    hardwareItem.setSerialNumber(serialNumber.getAndIncrement());
                    hardwareItem.setName(String.valueOf(item.get("deviceName")));
                    hardwareItem.setSpecification(String.valueOf(item.get("config")));
                    hardwareItem.setUnitPrice(parsePrice(String.valueOf(item.get("price"))));
                    hardwareItem.setQuantity(parseQuantity(String.valueOf(item.get("quantity"))));
                    hardwareItem.setRemarks(String.valueOf(item.get("remark")));
                    // 设置单位，如果从匹配结果中获取不到则使用默认值"台"
                    hardwareItem.setUnit(item.get("unit") != null ? String.valueOf(item.get("unit")) : "台");
                    
                    // 设置生产厂家，从供应商名称获取，如果没有则默认为"国能日新"
                    Object manufacturerObj = item.get("manufacturer");
                    if (manufacturerObj != null && !String.valueOf(manufacturerObj).trim().isEmpty()) {
                        String manufacturerStr = String.valueOf(manufacturerObj);
                        hardwareItem.setManufacturer(manufacturerStr);
                    } else {
                        hardwareItem.setManufacturer("国能日新");
                    }
                    
                    // 设置产地，从品牌（模糊）获取，如果没有则默认为"中国"
                    Object originObj = item.get("origin");
                    if (originObj != null && !String.valueOf(originObj).trim().isEmpty()) {
                        String originStr = String.valueOf(originObj);
                        hardwareItem.setOrigin(originStr);
                    } else {
                        hardwareItem.setOrigin("中国");
                    }
                    
                    hardwareItem.setTotalPrice(hardwareItem.getUnitPrice() * hardwareItem.getQuantity());
                    return hardwareItem;
                })
                .collect(Collectors.toList());
            quotationData.setHardwareItems(hardwareItems);
            serialNumber.set(1);
            List<ExcelReader.SoftwareItem> softwareItems = softwareList.stream()
                .map(item -> {
                    ExcelReader.SoftwareItem softwareItem = new ExcelReader.SoftwareItem();
                    softwareItem.setSerialNumber(serialNumber.getAndIncrement());
                    softwareItem.setName(String.valueOf(item.get("deviceName")));
                    softwareItem.setSpecification(String.valueOf(item.get("config")));
                    softwareItem.setUnitPrice(parsePrice(String.valueOf(item.get("price"))));
                    softwareItem.setQuantity(parseQuantity(String.valueOf(item.get("quantity"))));
                    softwareItem.setRemarks(String.valueOf(item.get("remark")));
                    // 设置单位，如果从匹配结果中获取不到则使用默认值"套"
                    softwareItem.setUnit(item.get("unit") != null ? String.valueOf(item.get("unit")) : "套");
                    
                    // 设置生产厂家，从供应商名称获取，如果没有则默认为"国能日新"
                    Object manufacturerObj = item.get("manufacturer");
                    if (manufacturerObj != null && !String.valueOf(manufacturerObj).trim().isEmpty()) {
                        String manufacturerStr = String.valueOf(manufacturerObj);
                        softwareItem.setManufacturer(manufacturerStr);
                    } else {
                        softwareItem.setManufacturer("国能日新");
                    }
                    
                    // 设置产地，从品牌（模糊）获取，如果没有则默认为"中国"
                    Object originObj = item.get("origin");
                    if (originObj != null && !String.valueOf(originObj).trim().isEmpty()) {
                        String originStr = String.valueOf(originObj);
                        softwareItem.setOrigin(originStr);
                    } else {
                        softwareItem.setOrigin("中国");
                    }
                    
                    softwareItem.setTotalPrice(softwareItem.getUnitPrice() * softwareItem.getQuantity());
                    return softwareItem;
                })
                .collect(Collectors.toList());
            quotationData.setSoftwareItems(softwareItems);
            serialNumber.set(1);
            List<ExcelReader.WindTowerItem> towerItems = towerList.stream()
                .map(item -> {
                    ExcelReader.WindTowerItem towerItem = new ExcelReader.WindTowerItem();
                    towerItem.setSerialNumber(serialNumber.getAndIncrement());
                    towerItem.setName(String.valueOf(item.get("deviceName")));
                    towerItem.setSpecification(String.valueOf(item.get("config")));
                    towerItem.setUnitPrice(parsePrice(String.valueOf(item.get("price"))));
                    towerItem.setQuantity(parseQuantity(String.valueOf(item.get("quantity"))));
                    towerItem.setRemarks(String.valueOf(item.get("remark")));
                    // 设置单位，如果从匹配结果中获取不到则使用默认值"套"
                    towerItem.setUnit(item.get("unit") != null ? String.valueOf(item.get("unit")) : "套");
                    
                    // 设置生产厂家，从供应商名称获取，如果没有则默认为"国能日新"
                    Object manufacturerObj = item.get("manufacturer");
                    if (manufacturerObj != null && !String.valueOf(manufacturerObj).trim().isEmpty()) {
                        String manufacturerStr = String.valueOf(manufacturerObj);
                        towerItem.setManufacturer(manufacturerStr);
                    } else {
                        towerItem.setManufacturer("国能日新");
                    }
                    
                    // 设置产地，从品牌（模糊）获取，如果没有则默认为"中国"
                    Object originObj = item.get("origin");
                    if (originObj != null && !String.valueOf(originObj).trim().isEmpty()) {
                        String originStr = String.valueOf(originObj);
                        towerItem.setOrigin(originStr);
                    } else {
                        towerItem.setOrigin("中国");
                    }
                    
                    towerItem.setTotalPrice(towerItem.getUnitPrice() * towerItem.getQuantity());
                    return towerItem;
                })
                .collect(Collectors.toList());
            quotationData.setWindTowerItems(towerItems);

             // 添加备注信息
            List<ExcelReader.QuotationNote> notes = new ArrayList<>();

            ExcelReader.QuotationNote note1 = new ExcelReader.QuotationNote();
            note1.setNumber(1);
            note1.setContent("本报价有效期为30天；");
            notes.add(note1);
            
            ExcelReader.QuotationNote note2 = new ExcelReader.QuotationNote();
            note2.setNumber(2);
            note2.setContent("本报价设备保修期为1年；");
            notes.add(note2);
            
            ExcelReader.QuotationNote note3 = new ExcelReader.QuotationNote();
            note3.setNumber(3);
            note3.setContent("本公司保留对报价产品的最终解释权；");
            notes.add(note3);
            
            ExcelReader.QuotationNote note4 = new ExcelReader.QuotationNote();
            note4.setNumber(4);
            note4.setContent("测风塔采购的实时数据通过光纤传输至功率预测机、接入风电机组光纤传输中中控室预测服务器中，买方负责风电机组光纤通讯及均匀内通；");
            notes.add(note4);
            
            ExcelReader.QuotationNote note5 = new ExcelReader.QuotationNote();
            note5.setNumber(5);
            note5.setContent("本报价设备国内交付为主，不包含关税和国外税费；");
            notes.add(note5);
            
            ExcelReader.QuotationNote note6 = new ExcelReader.QuotationNote();
            note6.setNumber(6);
            note6.setContent("本报价服务器和工作站系统按照国内项目标准提供；");
            notes.add(note6);
            
            ExcelReader.QuotationNote note7 = new ExcelReader.QuotationNote();
            note7.setNumber(7);
            note7.setContent("本报价不包含后期服务费及现场运维费。");
            notes.add(note7);
            quotationData.setNotes(notes);
         
            workbook = ExcelReader.generateQuotationExcel(quotationData);
            // 9. 设置响应头并输出文件
            log.info("设置响应头并输出Excel文件");
            // 禁止缓存
            response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0");
            response.setHeader("Pragma", "no-cache");
            response.setDateHeader("Expires", 0);
            
            // 设置正确的MIME类型
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            // 设置文件名
            String fileName = "风电功率预测系统V1.0报价单_" + LocalDate.now() + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            // 确保Content-Disposition格式正确
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            
            // 直接写入响应流
            workbook.write(response.getOutputStream());
            log.info("导出报价单完成");
            
        } catch (Exception e) {
            log.error("导出报价单失败", e);
            try {
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write("导出报价单失败：" + e.getMessage());
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        } finally {
            // 确保资源关闭，避免内存泄漏
            try {
                if (workbook != null) {
                    workbook.close();
                }
                if (templateStream != null) {
                    templateStream.close();
                }
            } catch (IOException e) {
                log.error("关闭资源失败", e);
            }
        }
    }
    
    /**
     * 解析价格字符串为数字
     * @param priceStr 价格字符串
     * @return 价格数值
     */
    private double parsePrice(String priceStr) {
        if (priceStr == null || priceStr.trim().isEmpty()) {
            return 0.0;
        }
        try {
            // 移除所有非数字字符（保留小数点）
            String cleanPrice = priceStr.replaceAll("[^0-9.]", "");
            return Double.parseDouble(cleanPrice);
        } catch (NumberFormatException e) {
            log.warn("价格解析失败: {}", priceStr);
            return 0.0;
        }
    }

    /**
     * 解析数量字符串为整数
     * @param quantityStr 数量字符串
     * @return 数量
     */
    private int parseQuantity(String quantityStr) {
        if (quantityStr == null || quantityStr.trim().isEmpty()) {
            return 1;
        }
        try {
            // 移除所有非数字字符
            String cleanQuantity = quantityStr.replaceAll("[^0-9]", "");
            if (cleanQuantity.isEmpty()) {
                return 1;
            }
            return Integer.parseInt(cleanQuantity);
        } catch (NumberFormatException e) {
            log.warn("数量解析失败: {}", quantityStr);
            return 1;
        }
    }

} 