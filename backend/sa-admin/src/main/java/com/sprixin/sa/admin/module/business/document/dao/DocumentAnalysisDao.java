package com.sprixin.sa.admin.module.business.document.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.admin.module.business.document.domain.entity.DocumentAnalysisResult;
import com.sprixin.sa.admin.module.business.document.domain.form.DocumentAnalysisQueryForm;
import com.sprixin.sa.admin.module.business.document.domain.vo.DocumentAnalysisResultVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文档分析DAO
 */
@Mapper
public interface DocumentAnalysisDao extends BaseMapper<DocumentAnalysisResult> {
    
    /**
     * 根据id查询
     */
    DocumentAnalysisResult selectById(@Param("id") Long id);
    
    /**
     * 更新分析结果
     */
    int updateAnalysisResult(@Param("id") Long id, @Param("analysisResult") String analysisResult);

    /**
     * 查询分析结果列表
     */
    List<DocumentAnalysisResultVO> queryAnalysisResults(Page page, @Param("queryForm") DocumentAnalysisQueryForm queryForm);

    /**
     * 根据查询表单统计总数
     */
    Long selectCountByForm(@Param("queryForm") DocumentAnalysisQueryForm queryForm);

    /**
     * 根据ID删除
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据文件ID查询
     */
    DocumentAnalysisResult selectByFileId(@Param("fileId") Long fileId);
} 