package com.sprixin.sa.admin.module.business.document.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文档分析结果
 */
@Data
@TableName("y_document_analysis_result")
public class DocumentAnalysisResult {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("file_id")
    private Long fileId;

    @TableField("template_id")
    private Long templateId;
    
    @TableField("document_name")
    private String documentName;
    
    @TableField("analysis_result")
    private String analysisResult;
    
    @TableField("analysis_prompt")
    private String analysisPrompt;
    
    @TableField("create_time")
    private LocalDateTime createTime;
    
    @TableField("update_time")
    private LocalDateTime updateTime;
    
    @TableField("creator_id")
    private Long creatorId;
    
    @TableField("creator_name")
    private String creatorName;
} 