package com.sprixin.sa.admin.module.business.document.domain.form;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 文档分析结果表单
 */
@Data
public class DocumentAnalysisResultForm {

    @NotNull(message = "文件ID不能为空")
    private Long fileId;

    @NotNull(message = "模板ID不能为空")
    private Long templateId;

    @Length(max = 1000, message = "分析提示词长度不能超过1000字符")
    private String analysisPrompt;
} 