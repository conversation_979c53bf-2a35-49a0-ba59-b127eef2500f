package com.sprixin.sa.admin.module.business.document.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.base.common.code.SystemErrorCode;
import com.sprixin.sa.base.common.domain.RequestUser;
import com.sprixin.sa.base.common.domain.ResponseDTO;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.admin.module.business.document.dao.DocumentAnalysisDao;
import com.sprixin.sa.admin.module.business.document.domain.entity.DocumentAnalysisResult;
import com.sprixin.sa.admin.module.business.document.domain.form.DocumentAnalysisResultForm;
import com.sprixin.sa.admin.module.business.document.domain.form.DocumentAnalysisQueryForm;
import com.sprixin.sa.admin.module.business.document.domain.vo.DocumentAnalysisResultVO;
import com.sprixin.sa.admin.module.business.materiallist.service.MaterialListService;
import com.sprixin.sa.admin.util.qwen.DocumentAnalysisMain;
import com.sprixin.sa.base.module.support.file.domain.vo.FileVO;
import com.sprixin.sa.base.module.support.file.service.FileService;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文档分析服务实现类
 */
@Service
@Slf4j
public class DocumentAnalysisServiceImpl implements IDocumentAnalysisService {
    
    @Resource
    private DocumentAnalysisDao documentAnalysisDao;

    @Resource
    private MaterialListService materialListService;
    
    @Resource
    private FileService fileService;

    @Resource
    private DocumentAnalysisMain documentAnalysisMain;
    
    @Value("${file.storage.local.upload-path}")
    private String uploadPath;
    /**
     * 分析文档
     * @param form 分析文档表单
     * @param requestUser 请求用户
     * @return 分析文档结果
     */
    @Override
    public ResponseDTO<DocumentAnalysisResultVO> analyzeDocument(DocumentAnalysisResultForm form, RequestUser requestUser) {
        try {
            // 1. 获取文件信息
            FileVO fileVO = fileService.getByFileId(form.getFileId());
            if (fileVO == null) {
                return ResponseDTO.userErrorParam("文件不存在");     
            }
            
            String fileUrl = fileVO.getFileUrl();
            String filePath = fileUrl;
            
            if (fileUrl.startsWith("http")) {
                // 如果是相对路径，转换为绝对路径
                filePath = getAbsolutePath(fileUrl);
            }
            
            log.info("处理文件路径: {}", filePath);
            
            // 2. 调用Qwen API分析文档
            List<HashMap<String, String>> results = documentAnalysisMain.analysisDocument(filePath);
            // 对文档分析的结果进行匹配
            // 1、获取备品备件数据表中所有数据，生成一个json格式的字符串；
            // 2、文档分析的结果json格式字符串；
             String analysisResult = JSON.toJSONString(results); //文档分析结果
             //获取物料清单数据
             List<Map<String, Object>> materialList = materialListService.getMaterialListJsonMap();

             //匹配后的json字符串
             analysisResult =  documentAnalysisMain.matchDocument(analysisResult, materialList);

            // 3. 保存分析结果
            DocumentAnalysisResult result = new DocumentAnalysisResult();
            result.setFileId(form.getFileId());
            result.setTemplateId(form.getTemplateId());
            result.setDocumentName(fileVO.getFileName());
            log.info("分析结果对象初始化: ID={}, FileId={}, TemplateId={}", result.getId(), result.getFileId(), result.getTemplateId());
            result.setAnalysisResult(analysisResult);
            result.setAnalysisPrompt(form.getAnalysisPrompt());
            result.setCreatorId(requestUser.getUserId());
            result.setCreatorName(requestUser.getUserName());
            
            documentAnalysisDao.insert(result);
            log.info("插入后分析结果对象: ID={}, FileId={}", result.getId(), result.getFileId());
            // 确保插入后ID被填充，如果DAO层没有自动填充，则通过查询获取
            log.info("查询保存结果，使用 fileId: {}", form.getFileId());
            DocumentAnalysisResult savedResult = documentAnalysisDao.selectByFileId(form.getFileId());
            log.info("查询到保存结果: ID={}, FileId={}", savedResult != null ? savedResult.getId() : "null", savedResult != null ? savedResult.getFileId() : "null");
            if (savedResult != null && savedResult.getId() != null) {
                result.setId(savedResult.getId());
            } else {
                log.error("文档分析结果保存成功但ID获取失败，fileId: {}", form.getFileId());
                return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "文档分析结果保存成功但ID获取失败");
            }
            log.info("返回前分析结果对象: ID={}, FileId={}", result.getId(), result.getFileId());
            
            // 4. 返回结果
            return ResponseDTO.ok(convertToVO(result, fileVO));
        } catch (Exception e) {
            log.error("文档分析失败", e);
            return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "文档分析失败: " + e.getMessage());
        }
    }
    
    @Override
    public ResponseDTO<DocumentAnalysisResultVO> getAnalysisResultById(Long id) {
        DocumentAnalysisResult result = documentAnalysisDao.selectById(id);
        if (result == null) {
            return ResponseDTO.userErrorParam("未找到分析结果");
        }
        
        // 获取文件信息
        FileVO fileVO = fileService.getByFileId(result.getFileId());
        if (fileVO == null) {
            return ResponseDTO.userErrorParam("文件不存在");
        }
        
        return ResponseDTO.ok(convertToVO(result, fileVO));
    }
    
    @Override
    public ResponseDTO<String> updateAnalysisResult(Long id, List<Map<String, Object>> analysisResultList) {
        try {
            // 将对象列表序列化为 JSON 字符串
            String analysisResultJson = JSON.toJSONString(analysisResultList);
            int rows = documentAnalysisDao.updateAnalysisResult(id, analysisResultJson);
            if (rows > 0) {
                return ResponseDTO.ok("更新成功");
            } else {
                return ResponseDTO.userErrorParam("更新失败");
            }
        } catch (Exception e) {
            log.error("更新分析结果失败", e);
            return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "更新分析结果失败: " + e.getMessage());
        }
    }
    
    @Override
    public ResponseDTO<String> deleteAnalysisResult(Long id) {
        try {
            int rows = documentAnalysisDao.deleteById(id);
            if (rows > 0) {
                return ResponseDTO.ok("删除成功");
            } else {
                return ResponseDTO.userErrorParam("删除失败，记录不存在");
            }
        } catch (Exception e) {
            log.error("删除分析结果失败", e);
            return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "删除分析结果失败: " + e.getMessage());
        }
    }
    
    @Override
    public ResponseDTO<PageResult<DocumentAnalysisResultVO>> queryAnalysisResults(DocumentAnalysisQueryForm queryForm) {
        try {
            // 创建分页对象
            Page<DocumentAnalysisResultVO> page = new Page<>(queryForm.getPageNum(), queryForm.getPageSize());
            
            log.info("查询分析结果列表，pageNum: {}, pageSize: {}, keyword: {}", queryForm.getPageNum(), queryForm.getPageSize(), queryForm.getKeyword());

            // 查询数据
            List<DocumentAnalysisResultVO> list = documentAnalysisDao.queryAnalysisResults(page, queryForm);

            // 查询总数
            Long total = documentAnalysisDao.selectCountByForm(queryForm);
            
            // 构建分页结果
            PageResult<DocumentAnalysisResultVO> pageResult = new PageResult<>();
            pageResult.setList(list);
            pageResult.setTotal(total);
            
            return ResponseDTO.ok(pageResult);
        } catch (Exception e) {
            log.error("查询分析结果列表失败", e);
            return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, "查询分析结果列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 转换为VO
     */
    private DocumentAnalysisResultVO convertToVO(DocumentAnalysisResult result, FileVO fileVO) {
        DocumentAnalysisResultVO vo = new DocumentAnalysisResultVO();
        vo.setId(result.getId());
        vo.setFileId(result.getFileId());
        vo.setTemplateId(result.getTemplateId());
        vo.setFileName(fileVO.getFileName());
        vo.setFileUrl(fileVO.getFileUrl());
        vo.setAnalysisResult(result.getAnalysisResult());
        vo.setAnalysisPrompt(result.getAnalysisPrompt());
        vo.setCreateTime(result.getCreateTime());
        vo.setCreatorName(result.getCreatorName());
        return vo;
    }
    
    /**
     * 获取文件的绝对路径
     */
    private String getAbsolutePath(String relativePath) {
        // 标准化路径分隔符
        String normalizedPath = relativePath.replace('\\', '/');
        normalizedPath = normalizedPath.split("upload")[1];
        
        // 使用配置的上传路径
        String basePath = uploadPath.replace('\\', '/');
        if (!basePath.endsWith("/")) {
            basePath = basePath + "/";
        }
        
        // 组合路径
        return basePath + normalizedPath.substring(1);
    }
} 