package com.sprixin.sa.admin.module.business.document.service;

import com.sprixin.sa.base.common.domain.RequestUser;
import com.sprixin.sa.base.common.domain.ResponseDTO;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.admin.module.business.document.domain.form.DocumentAnalysisResultForm;
import com.sprixin.sa.admin.module.business.document.domain.form.DocumentAnalysisQueryForm;
import com.sprixin.sa.admin.module.business.document.domain.vo.DocumentAnalysisResultVO;
import java.util.List;
import java.util.Map;

/**
 * 文档分析服务接口
 */
public interface IDocumentAnalysisService {
    
    /**
     * 分析文档
     */
    ResponseDTO<DocumentAnalysisResultVO> analyzeDocument(DocumentAnalysisResultForm form, RequestUser requestUser);
    
    /**
     * 获取分析结果
     */
    ResponseDTO<DocumentAnalysisResultVO> getAnalysisResultById(Long id);
    
    /**
     * 更新分析结果
     */
    ResponseDTO<String> updateAnalysisResult(Long id, List<Map<String, Object>> analysisResultList);

    /**
     * 查询分析结果列表
     */
    ResponseDTO<PageResult<DocumentAnalysisResultVO>> queryAnalysisResults(DocumentAnalysisQueryForm queryForm);

    /**
     * 删除分析结果
     */
    ResponseDTO<String> deleteAnalysisResult(Long id);
} 