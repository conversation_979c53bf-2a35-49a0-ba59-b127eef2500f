package com.sprixin.sa.admin.module.business.equipmentprocurement.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.admin.module.business.equipmentprocurement.domain.entity.EquipmentProcurementEntity;
import com.sprixin.sa.admin.module.business.equipmentprocurement.domain.form.EquipmentProcurementQueryForm;
import com.sprixin.sa.admin.module.business.equipmentprocurement.domain.vo.EquipmentProcurementVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 设备采购 dao
 */
@Mapper
public interface EquipmentProcurementDao extends BaseMapper<EquipmentProcurementEntity> {
    
    /**
     * 查询设备采购列表
     */
    List<EquipmentProcurementVO> queryEquipmentProcurement(Page page, @Param("queryForm") EquipmentProcurementQueryForm queryForm);
    
    /**
     * 根据ID查询设备采购详情
     */
    EquipmentProcurementVO getEquipmentProcurementById(@Param("id") Integer id);

    /**
     * 根据查询表单统计总数
     */
    Long selectCountByForm(@Param("queryForm") EquipmentProcurementQueryForm queryForm);
} 