package com.sprixin.sa.admin.module.business.equipmentprocurement.domain.form;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 设备采购 添加表单
 */
@Data
public class EquipmentProcurementAddForm {

    /**
     * 设备名称
     */
    @NotBlank(message = "设备名称不能为空")
    private String equipmentName;

    /**
     * 邀标配置：邀标技术规格或配置要求
     */
    private String tenderConfig;

    /**
     * 供应商名称：供应商公司全称
     */
    private String supplierName;

    /**
     * 投标品牌：供应商投标的设备品牌
     */
    private String biddingBrand;

    /**
     * 投标型号：供应商投标的设备型号
     */
    private String biddingModel;

    /**
     * 投标配置：供应商投标的实际设备配置详情
     */
    private String biddingConfig;

    /**
     * 单价：设备单价（元）
     */
    @NotNull(message = "单价不能为空")
    private BigDecimal unitPrice;

    /**
     * 推荐采购比例：推荐的采购数量比例或说明
     */
    private String recommendedProcurementRatio;

    /**
     * ERP物料编码：企业资源计划 (ERP) 系统中的物料编码
     */
    private String erpMaterialCode;

    /**
     * 通用编码：通用物料编码或行业标准编码
     */
    private String universalCode;

    /**
     * 通用编码报价：基于通用编码的报价（元）
     */
    private BigDecimal universalCodeQuotation;

    /**
     * 说明：其他备注或说明信息
     */
    private String remarks;

    /**
     * 供应商种类：供应商的分类或级别
     */
    private String supplierType;

    /**
     * 质保周期（月）：设备质保期，单位为月
     */
    private Integer warrantyPeriodMonths;

    /**
     * 是否为新型号/品牌：标记是否为市场上新的型号或品牌 (1表示是, 0表示否)
     */
    private Boolean isNewModelBrand;

    /**
     * 是否通过测试：标记设备样品或方案是否已通过技术测试 (1表示是, 0表示否)
     */
    private Boolean passedTesting;

    /**
     * 技术联系人：供应商的技术对接人姓名
     */
    private String technicalContactPerson;

    /**
     * 技术联系电话：技术联系人的电话号码
     */
    private String technicalContactPhone;

    /**
     * 是否可放到600深机柜：标记设备是否可以放置在600mm深度的标准机柜中 (1表示是, 0表示否)
     */
    private Boolean canFit600mmDeepCabinet;
} 