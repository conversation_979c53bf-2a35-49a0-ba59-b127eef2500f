package com.sprixin.sa.admin.module.business.equipmentprocurement.service;

import com.sprixin.sa.admin.module.business.equipmentprocurement.domain.form.EquipmentProcurementAddForm;
import com.sprixin.sa.admin.module.business.equipmentprocurement.domain.form.EquipmentProcurementQueryForm;
import com.sprixin.sa.admin.module.business.equipmentprocurement.domain.form.EquipmentProcurementUpdateForm;
import com.sprixin.sa.admin.module.business.equipmentprocurement.domain.vo.EquipmentProcurementVO;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.base.common.domain.ResponseDTO;

import java.util.List;

/**
 * 设备采购 服务接口
 */
public interface EquipmentProcurementService {

    /**
     * 分页查询设备采购
     *
     * @param queryForm 查询参数
     * @return 分页结果
     */
    ResponseDTO<PageResult<EquipmentProcurementVO>> queryEquipmentProcurement(EquipmentProcurementQueryForm queryForm);

    /**
     * 添加设备采购
     *
     * @param addForm 添加参数
     * @return 操作结果
     */
    ResponseDTO<String> addEquipmentProcurement(EquipmentProcurementAddForm addForm);

    /**
     * 更新设备采购
     *
     * @param updateForm 更新参数
     * @return 操作结果
     */
    ResponseDTO<String> updateEquipmentProcurement(EquipmentProcurementUpdateForm updateForm);

    /**
     * 删除设备采购
     *
     * @param id 设备采购ID
     * @return 操作结果
     */
    ResponseDTO<String> deleteEquipmentProcurement(Integer id);

    /**
     * 批量删除设备采购
     *
     * @param idList ID列表
     * @return 操作结果
     */
    ResponseDTO<String> batchDeleteEquipmentProcurement(List<Integer> idList);

    /**
     * 获取设备采购详情
     *
     * @param id 设备采购ID
     * @return 设备采购详情
     */
    ResponseDTO<EquipmentProcurementVO> getEquipmentProcurementDetail(Integer id);

    /**
     * 获取设备采购总数
     *
     * @return 总数
     */
    ResponseDTO<Long> getEquipmentProcurementCount();
} 