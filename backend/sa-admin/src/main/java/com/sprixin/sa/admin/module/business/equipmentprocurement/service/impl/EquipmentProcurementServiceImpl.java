package com.sprixin.sa.admin.module.business.equipmentprocurement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.admin.module.business.equipmentprocurement.dao.EquipmentProcurementDao;
import com.sprixin.sa.admin.module.business.equipmentprocurement.domain.entity.EquipmentProcurementEntity;
import com.sprixin.sa.admin.module.business.equipmentprocurement.domain.form.EquipmentProcurementAddForm;
import com.sprixin.sa.admin.module.business.equipmentprocurement.domain.form.EquipmentProcurementQueryForm;
import com.sprixin.sa.admin.module.business.equipmentprocurement.domain.form.EquipmentProcurementUpdateForm;
import com.sprixin.sa.admin.module.business.equipmentprocurement.domain.vo.EquipmentProcurementVO;
import com.sprixin.sa.admin.module.business.equipmentprocurement.service.EquipmentProcurementService;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.base.common.domain.ResponseDTO;
import com.sprixin.sa.base.common.code.UserErrorCode;
import com.sprixin.sa.base.common.util.SmartPageUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备采购服务实现类
 */
@Slf4j
@Service
public class EquipmentProcurementServiceImpl implements EquipmentProcurementService {

    @Resource
    private EquipmentProcurementDao equipmentProcurementDao;

    /**
     * 查询设备采购列表
     * @param queryForm 查询条件
     * @return 设备采购列表
     */
    @Override
    public ResponseDTO<PageResult<EquipmentProcurementVO>> queryEquipmentProcurement(EquipmentProcurementQueryForm queryForm) {
        try {
            // 参数验证
            if (queryForm == null) {
                log.error("Query form is null");
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "查询参数不能为空");
            }
            if (queryForm.getPageNum() == null || queryForm.getPageSize() == null) {
                log.error("Page parameters are null: pageNum={}, pageSize={}", 
                    queryForm.getPageNum(), queryForm.getPageSize());
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "分页参数不能为空");
            }
            
            // 创建分页对象，确保类型转换正确
            int pageNum = queryForm.getPageNum().intValue();
            int pageSize = queryForm.getPageSize().intValue();
            if (pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize < 1 || pageSize > 500) {
                pageSize = 10;
            }
            
            Page<EquipmentProcurementVO> page = new Page<>(pageNum, pageSize);
            log.debug("Querying equipment procurement with page: current={}, size={}, keyword={}", 
                page.getCurrent(), page.getSize(), queryForm.getKeyword());
            
            // 执行分页查询
            List<EquipmentProcurementVO> list = equipmentProcurementDao.queryEquipmentProcurement(page, queryForm);
            if (list == null) {
                log.error("Query result is null");
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "查询结果为空");
            }
            log.debug("Query result size: {}", list.size());
            
            // 计算总记录数
            long total = equipmentProcurementDao.selectCountByForm(queryForm);
            
            // 构建分页结果
            PageResult<EquipmentProcurementVO> pageResult = new PageResult<>();
            pageResult.setList(list);
            pageResult.setTotal(total);
            pageResult.setPageNum(Long.valueOf(pageNum));
            pageResult.setPageSize(Long.valueOf(pageSize));
            
            return ResponseDTO.ok(pageResult);
        } catch (Exception e) {
            log.error("Error querying equipment procurement", e);
            return ResponseDTO.error(UserErrorCode.SYSTEM_ERROR, "查询设备采购失败");
        }
    }

    /**
     * 添加设备采购
     * @param addForm 添加条件
     * @return 添加结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> addEquipmentProcurement(EquipmentProcurementAddForm addForm) {
        try {
            // 参数验证
            if (addForm == null) {
                log.error("Add form is null");
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "添加参数不能为空");
            }
            
            // 构建实体
            EquipmentProcurementEntity entity = new EquipmentProcurementEntity();
            BeanUtil.copyProperties(addForm, entity);
            entity.setLastModifiedDate(LocalDateTime.now());
            
            // 保存数据
            int result = equipmentProcurementDao.insert(entity);
            if (result <= 0) {
                log.error("Failed to insert equipment procurement");
                return ResponseDTO.error(UserErrorCode.SYSTEM_ERROR, "添加设备采购失败");
            }
            
            return ResponseDTO.ok("添加设备采购成功");
        } catch (Exception e) {
            log.error("Error adding equipment procurement", e);
            return ResponseDTO.error(UserErrorCode.SYSTEM_ERROR, "添加设备采购失败");
        }
    }

    /**
     * 更新设备采购
     * @param updateForm 更新条件
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> updateEquipmentProcurement(EquipmentProcurementUpdateForm updateForm) {
        try {
            // 参数验证
            if (updateForm == null) {
                log.error("Update form is null");
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "更新参数不能为空");
            }
            if (updateForm.getId() == null) {
                log.error("Update form id is null");
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "设备采购ID不能为空");
            }
            
            // 检查记录是否存在
            EquipmentProcurementEntity existEntity = equipmentProcurementDao.selectById(updateForm.getId());
            if (existEntity == null) {
                log.error("Equipment procurement not found: id={}", updateForm.getId());
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "设备采购不存在");
            }
            
            // 构建实体
            EquipmentProcurementEntity entity = new EquipmentProcurementEntity();
            BeanUtil.copyProperties(updateForm, entity);
            entity.setLastModifiedDate(LocalDateTime.now());
            
            // 更新数据
            int result = equipmentProcurementDao.updateById(entity);
            if (result <= 0) {
                log.error("Failed to update equipment procurement: id={}", updateForm.getId());
                return ResponseDTO.error(UserErrorCode.SYSTEM_ERROR, "更新设备采购失败");
            }
            
            return ResponseDTO.ok("更新设备采购成功");
        } catch (Exception e) {
            log.error("Error updating equipment procurement", e);
            return ResponseDTO.error(UserErrorCode.SYSTEM_ERROR, "更新设备采购失败");
        }
    }

    /**
     * 删除设备采购
     * @param id 设备采购ID
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> deleteEquipmentProcurement(Integer id) {
        try {
            // 参数验证
            if (id == null) {
                log.error("Delete id is null");
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "设备采购ID不能为空");
            }
            
            // 检查记录是否存在
            EquipmentProcurementEntity existEntity = equipmentProcurementDao.selectById(id);
            if (existEntity == null) {
                log.error("Equipment procurement not found: id={}", id);
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "设备采购不存在");
            }
            
            // 删除数据
            int result = equipmentProcurementDao.deleteById(id);
            if (result <= 0) {
                log.error("Failed to delete equipment procurement: id={}", id);
                return ResponseDTO.error(UserErrorCode.SYSTEM_ERROR, "删除设备采购失败");
            }
            
            return ResponseDTO.ok("删除设备采购成功");
        } catch (Exception e) {
            log.error("Error deleting equipment procurement", e);
            return ResponseDTO.error(UserErrorCode.SYSTEM_ERROR, "删除设备采购失败");
        }
    }

    /**
     * 批量删除设备采购
     * @param idList 设备采购ID列表
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> batchDeleteEquipmentProcurement(List<Integer> idList) {
        try {
            // 参数验证
            if (CollUtil.isEmpty(idList)) {
                log.error("Batch delete id list is empty");
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "设备采购ID列表不能为空");
            }
            
            // 删除数据
            int result = equipmentProcurementDao.deleteBatchIds(idList);
            if (result <= 0) {
                log.error("Failed to batch delete equipment procurement: idList={}", idList);
                return ResponseDTO.error(UserErrorCode.SYSTEM_ERROR, "批量删除设备采购失败");
            }
            
            return ResponseDTO.ok("批量删除设备采购成功");
        } catch (Exception e) {
            log.error("Error batch deleting equipment procurement", e);
            return ResponseDTO.error(UserErrorCode.SYSTEM_ERROR, "批量删除设备采购失败");
        }
    }

    /**
     * 获取设备采购详情
     * @param id 设备采购ID
     * @return 设备采购详情
     */
    @Override
    public ResponseDTO<EquipmentProcurementVO> getEquipmentProcurementDetail(Integer id) {
        try {
            // 参数验证
            if (id == null) {
                log.error("Detail id is null");
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "设备采购ID不能为空");
            }
            
            // 查询详情
            EquipmentProcurementVO vo = equipmentProcurementDao.getEquipmentProcurementById(id);
            if (vo == null) {
                log.error("Equipment procurement not found: id={}", id);
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "设备采购不存在");
            }
            
            return ResponseDTO.ok(vo);
        } catch (Exception e) {
            log.error("Error getting equipment procurement detail", e);
            return ResponseDTO.error(UserErrorCode.SYSTEM_ERROR, "获取设备采购详情失败");
        }
    }

    /**
     * 获取设备采购总数
     * @return 设备采购总数
     */
    @Override
    public ResponseDTO<Long> getEquipmentProcurementCount() {
        try {
            Long count = equipmentProcurementDao.selectCount(null);
            return ResponseDTO.ok(count);
        } catch (Exception e) {
            log.error("Error getting equipment procurement count", e);
            return ResponseDTO.error(UserErrorCode.SYSTEM_ERROR, "获取设备采购总数失败");
        }
    }
} 