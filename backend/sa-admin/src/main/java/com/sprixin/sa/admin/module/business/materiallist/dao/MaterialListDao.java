package com.sprixin.sa.admin.module.business.materiallist.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.admin.module.business.materiallist.domain.entity.MaterialListEntity;
import com.sprixin.sa.admin.module.business.materiallist.domain.form.MaterialListQueryForm;
import com.sprixin.sa.admin.module.business.materiallist.domain.vo.MaterialListVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 物料清单 dao
 */
@Mapper
public interface MaterialListDao extends BaseMapper<MaterialListEntity> {
    
    /**
     * 查询物料清单列表
     */
    List<MaterialListVO> queryMaterialList(Page page, @Param("queryForm") MaterialListQueryForm queryForm);
    
    /**
     * 查询所有物料清单
     */
    List<MaterialListEntity> findAll();

    /**
     * 根据ID查询物料清单详情
     */
    MaterialListVO getMaterialListById(@Param("id") Integer id);

    /**
     * 根据查询表单统计总数
     */
    Long selectCountByForm(@Param("queryForm") MaterialListQueryForm queryForm);
} 