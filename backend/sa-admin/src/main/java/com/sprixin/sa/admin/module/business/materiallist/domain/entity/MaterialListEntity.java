package com.sprixin.sa.admin.module.business.materiallist.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 物料清单 实体表
 */
@Data
@TableName("y_material_list")
public class MaterialListEntity {

    /**
     * 主键，自增
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 品牌（模糊）
     */
    private String brandFuzzy;

    /**
     * 规格型号
     */
    private String model;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 详细配置
     */
    private String detailConfig;

    /**
     * 单价（元）
     */
    private BigDecimal unitPrice;

    /**
     * 供应商联系人
     */
    private String supplierContact;

    /**
     * 联系电话
     */
    private String supplierPhone;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}