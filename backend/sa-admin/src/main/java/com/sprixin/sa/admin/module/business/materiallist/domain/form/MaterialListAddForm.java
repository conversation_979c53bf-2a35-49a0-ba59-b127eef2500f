package com.sprixin.sa.admin.module.business.materiallist.domain.form;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 物料清单 添加表单
 */
@Data
public class MaterialListAddForm {

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    @NotBlank(message = "物料名称不能为空")
    private String materialName;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 品牌（模糊）
     */
    private String brandFuzzy;

    /**
     * 规格型号
     */
    private String model;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 详细配置
     */
    private String detailConfig;

    /**
     * 单价（元）
     */
    @NotNull(message = "单价不能为空")
    private BigDecimal unitPrice;

    /**
     * 供应商联系人
     */
    private String supplierContact;

    /**
     * 联系电话
     */
    private String supplierPhone;
}