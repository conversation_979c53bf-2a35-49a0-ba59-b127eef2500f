package com.sprixin.sa.admin.module.business.materiallist.service;

import com.sprixin.sa.admin.module.business.materiallist.domain.form.MaterialListAddForm;
import com.sprixin.sa.admin.module.business.materiallist.domain.form.MaterialListQueryForm;
import com.sprixin.sa.admin.module.business.materiallist.domain.form.MaterialListUpdateForm;
import com.sprixin.sa.admin.module.business.materiallist.domain.vo.MaterialListVO;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.base.common.domain.ResponseDTO;
import java.util.List;
import java.util.Map;

/**
 * 物料清单 Service
 */
public interface MaterialListService {

    /**
     * 查询物料清单列表
     * @param queryForm 查询条件
     * @return 物料清单列表
     */
    ResponseDTO<PageResult<MaterialListVO>> queryMaterialList(MaterialListQueryForm queryForm);

    /**
     * 添加物料清单
     * @param addForm 添加表单
     * @return 添加结果
     */
    ResponseDTO<String> addMaterialList(MaterialListAddForm addForm);

    /**
     * 更新物料清单
     * @param updateForm 更新表单
     * @return 更新结果
     */
    ResponseDTO<String> updateMaterialList(MaterialListUpdateForm updateForm);

    /**
     * 删除物料清单
     * @param id 物料清单ID
     * @return 删除结果
     */
    ResponseDTO<String> deleteMaterialList(Integer id);

    /**
     * 批量删除物料清单
     * @param idList 物料清单ID列表
     * @return 删除结果
     */
    ResponseDTO<String> batchDeleteMaterialList(List<Integer> idList);

    /**
     * 获取物料清单详情
     * @param id 物料清单ID
     * @return 物料清单详情
     */
    ResponseDTO<MaterialListVO> getMaterialListDetail(Integer id);

    /**
     * 获取物料清单总数
     * @return 总数
     */
    ResponseDTO<Long> getMaterialListCount();

    /**
     * 获取所有物料清单
     * @return 所有物料清单列表
     */
    ResponseDTO<List<MaterialListVO>> getAllMaterialList();

    /**
     * 获取所有物料清单的JSON字符串
     * @return 所有物料清单的JSON字符串
     */
    List<Map<String, Object>> getMaterialListJsonMap();
} 