package com.sprixin.sa.admin.module.business.materiallist.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.admin.module.business.materiallist.dao.MaterialListDao;
import com.sprixin.sa.admin.module.business.materiallist.domain.entity.MaterialListEntity;
import com.sprixin.sa.admin.module.business.materiallist.domain.form.MaterialListAddForm;
import com.sprixin.sa.admin.module.business.materiallist.domain.form.MaterialListQueryForm;
import com.sprixin.sa.admin.module.business.materiallist.domain.form.MaterialListUpdateForm;
import com.sprixin.sa.admin.module.business.materiallist.domain.vo.MaterialListVO;
import com.sprixin.sa.admin.module.business.materiallist.service.MaterialListService;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.base.common.domain.ResponseDTO;
import com.sprixin.sa.base.common.code.UserErrorCode;
import com.sprixin.sa.base.common.util.SmartPageUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物料清单服务实现类
 */
@Slf4j
@Service
public class MaterialListServiceImpl implements MaterialListService {

    @Resource
    private MaterialListDao materialListDao;

    /**
     * 查询物料清单列表
     * @param queryForm 查询条件
     * @return 物料清单列表
     */
    @Override
    public ResponseDTO<PageResult<MaterialListVO>> queryMaterialList(MaterialListQueryForm queryForm) {
        try {
            // 参数验证
            if (queryForm == null) {
                log.error("Query form is null");
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "查询参数不能为空");
            }
            if (queryForm.getPageNum() == null || queryForm.getPageSize() == null) {
                log.error("Page parameters are null: pageNum={}, pageSize={}", 
                    queryForm.getPageNum(), queryForm.getPageSize());
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "分页参数不能为空");
            }
            
            // 创建分页对象，确保类型转换正确
            int pageNum = queryForm.getPageNum().intValue();
            int pageSize = queryForm.getPageSize().intValue();
            if (pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize < 1 || pageSize > 500) {
                pageSize = 10;
            }
            
            Page<MaterialListVO> page = new Page<>(pageNum, pageSize);
            log.debug("Querying material list with page: current={}, size={}, keyword={}", 
                page.getCurrent(), page.getSize(), queryForm.getKeyword());
            
            // 执行分页查询
            List<MaterialListVO> list = materialListDao.queryMaterialList(page, queryForm);
            if (list == null) {
                log.error("Query result is null");
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "查询结果为空");
            }
            log.debug("Query result size: {}", list.size());
            
            // 计算总记录数
            long total;
            try {
                // 使用新的计数方法
                total = materialListDao.selectCountByForm(queryForm);
                log.debug("Total records: {}", total);
            } catch (Exception e) {
                log.error("Error counting total records: {}", e.getMessage(), e);
                // 如果计数失败，使用列表大小作为总数
                total = list.size();
                log.warn("Using list size as total: {}", total);
            }
            page.setTotal(total);
            
            // 转换分页结果
            PageResult<MaterialListVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
            log.debug("PageResult: pageNum={}, pageSize={}, total={}, listSize={}", 
                pageResult.getPageNum(), pageResult.getPageSize(), 
                pageResult.getTotal(), pageResult.getList() != null ? pageResult.getList().size() : 0);
            
            return ResponseDTO.ok(pageResult);
        } catch (Exception e) {
            log.error("Error querying material list: {}", e.getMessage(), e);
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, 
                "查询物料清单列表失败：" + (e.getMessage() != null ? e.getMessage() : "未知错误"));
        }
    }

    /**
     * 添加物料清单
     * @param addForm 添加条件
     * @return 添加结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> addMaterialList(MaterialListAddForm addForm) {
        // 创建实体并保存
        MaterialListEntity entity = new MaterialListEntity();
        BeanUtil.copyProperties(addForm, entity);
        entity.setCreatedAt(LocalDateTime.now());
        materialListDao.insert(entity);
        return ResponseDTO.ok("添加成功");
    }

    /**
     * 更新物料清单
     * @param updateForm 更新条件
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> updateMaterialList(MaterialListUpdateForm updateForm) {
        // 检查物料清单是否存在
        MaterialListEntity existEntity = materialListDao.selectById(updateForm.getId());
        if (existEntity == null) {
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "物料清单不存在");
        }
        
        // 更新实体
        MaterialListEntity entity = new MaterialListEntity();
        BeanUtil.copyProperties(updateForm, entity);
        materialListDao.updateById(entity);
        return ResponseDTO.ok("更新成功");
    }

    /**
     * 删除物料清单
     * @param id 物料清单ID
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> deleteMaterialList(Integer id) {
        // 检查物料清单是否存在
        MaterialListEntity existEntity = materialListDao.selectById(id);
        if (existEntity == null) {
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "物料清单不存在");
        }
        
        // 删除物料清单
        materialListDao.deleteById(id);
        return ResponseDTO.ok("删除成功");
    }

    /**
     * 批量删除物料清单
     * @param idList 物料清单ID列表
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> batchDeleteMaterialList(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "请选择要删除的物料清单");
        }
        
        // 批量删除
        materialListDao.deleteBatchIds(idList);
        return ResponseDTO.ok("批量删除成功");
    }

    /**
     * 获取物料清单详情
     * @param id 物料清单ID
     * @return 物料清单详情
     */ 
    @Override
    public ResponseDTO<MaterialListVO> getMaterialListDetail(Integer id) {
        MaterialListVO materialListVO = materialListDao.getMaterialListById(id);
        if (materialListVO == null) {
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "物料清单不存在");
        }
        return ResponseDTO.ok(materialListVO);
    }

    /**
     * 获取物料清单总数
     * @return 总数
    */
    @Override
    public ResponseDTO<Long> getMaterialListCount() {
        try {
            Long count = materialListDao.selectCount(null);
            return ResponseDTO.ok(count);
        } catch (Exception e) {
            log.error("Error getting material list count", e);
            throw e;
        }
    }

    /**
     * 获取所有物料清单
     * @return 所有物料清单列表
     */
    @Override
    public ResponseDTO<List<MaterialListVO>> getAllMaterialList() {
        try {
            // 调用Dao层查询所有记录
            List<MaterialListEntity> entityList = materialListDao.findAll();
            
            // 转换为VO列表
            List<MaterialListVO> voList = entityList.stream()
                .map(entity -> {
                    MaterialListVO vo = new MaterialListVO();
                    BeanUtil.copyProperties(entity, vo);
                    return vo;
                })
                .collect(Collectors.toList());
                
            return ResponseDTO.ok(voList);
        } catch (Exception e) {
            log.error("获取所有物料清单失败", e);
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "获取所有物料清单失败：" + e.getMessage());
        }
    }
    /**
     * 获取所有物料清单的格式化字符串
     * @return 所有物料清单的格式化字符串
     */
    @Override
    public List<Map<String, Object>> getMaterialListJsonMap() {
        ResponseDTO<List<MaterialListVO>> responseDTO = getAllMaterialList();
        List<MaterialListVO> materialListVOList = responseDTO.getData();
        List<Map<String, Object>> result = new ArrayList<>();
        
        if (materialListVOList == null) {
            return result;
        }         
        for (int i = 0; i < materialListVOList.size(); i++) {
            MaterialListVO vo = materialListVOList.get(i);
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("物料ID", vo.getId()); // 添加物料ID
            resultMap.put("物料名称", vo.getMaterialName());
            resultMap.put("详细配置", vo.getDetailConfig() != null ? vo.getDetailConfig() : "/");
            resultMap.put("单价", vo.getUnitPrice());
            resultMap.put("供应商名称", vo.getSupplierName() != null ? vo.getSupplierName() : "国能日新");
            resultMap.put("品牌", vo.getBrand() != null ? vo.getBrand() : "");
            resultMap.put("物料编码", vo.getMaterialCode() != null ? vo.getMaterialCode() : "");
            resultMap.put("供应商联系人", vo.getSupplierContact() != null ? vo.getSupplierContact() : "");
            resultMap.put("规格型号", vo.getModel() != null ? vo.getModel() : "/");
            resultMap.put("联系电话", vo.getSupplierPhone() != null ? vo.getSupplierPhone() : "");
            resultMap.put("品牌（模糊）", vo.getBrandFuzzy() != null ? vo.getBrandFuzzy() : "中国");
            resultMap.put("计量单位", vo.getUnit() != null ? vo.getUnit() : "台");
            result.add(resultMap);
            
        }
        
        return result;
    }
} 