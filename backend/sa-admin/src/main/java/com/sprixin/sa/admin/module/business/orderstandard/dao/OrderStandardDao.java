package com.sprixin.sa.admin.module.business.orderstandard.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.admin.module.business.orderstandard.domain.entity.OrderStandardEntity;
import com.sprixin.sa.admin.module.business.orderstandard.domain.form.OrderStandardQueryForm;
import com.sprixin.sa.admin.module.business.orderstandard.domain.vo.OrderStandardVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 设备下单标准 dao
 */
@Mapper
public interface OrderStandardDao extends BaseMapper<OrderStandardEntity> {
    
    /**
     * 查询设备下单标准列表
     */
    List<OrderStandardVO> queryOrderStandard(Page page, @Param("queryForm") OrderStandardQueryForm queryForm);
    
    /**
     * 根据ID查询设备下单标准详情
     */
    OrderStandardVO getOrderStandardById(@Param("id") Integer id);

    /**
     * 根据查询表单统计总数
     */
    Long selectCountByForm(@Param("queryForm") OrderStandardQueryForm queryForm);
} 