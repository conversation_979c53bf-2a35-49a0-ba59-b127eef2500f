package com.sprixin.sa.admin.module.business.orderstandard.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 设备下单标准实体类
 */
@Data
@TableName("y_order_standard")
public class OrderStandardEntity {

    /**
     * 序号
     */
    @TableId(value = "serial_number", type = IdType.AUTO)
    private Integer serialNumber;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 下单标准
     */
    private String orderStandard;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
} 