package com.sprixin.sa.admin.module.business.orderstandard.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 设备下单标准添加表单
 */
@Data
@Schema(description = "设备下单标准添加表单")
public class OrderStandardAddForm {

    @Schema(description = "设备名称")
    @NotBlank(message = "设备名称不能为空")
    @Size(max = 100, message = "设备名称长度不能超过100个字符")
    private String deviceName;

    @Schema(description = "下单标准")
    @NotBlank(message = "下单标准不能为空")
    @Size(max = 500, message = "下单标准长度不能超过500个字符")
    private String orderStandard;
} 