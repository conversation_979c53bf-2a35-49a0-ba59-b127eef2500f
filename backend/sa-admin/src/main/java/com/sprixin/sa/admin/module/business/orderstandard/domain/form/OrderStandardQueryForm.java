package com.sprixin.sa.admin.module.business.orderstandard.domain.form;

import com.sprixin.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备下单标准查询表单
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "设备下单标准查询表单")
public class OrderStandardQueryForm extends PageParam {

    @Schema(description = "关键字（设备名称或下单标准）")
    private String keyword;
} 