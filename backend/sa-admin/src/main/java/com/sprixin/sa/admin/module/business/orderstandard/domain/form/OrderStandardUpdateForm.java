package com.sprixin.sa.admin.module.business.orderstandard.domain.form;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 设备下单标准 更新表单
 */
@Data
public class OrderStandardUpdateForm {

    /**
     * 序号，主键
     */
    @NotNull(message = "序号不能为空")
    private Integer serialNumber;

    /**
     * 设备名称
     */
    @Size(max = 255, message = "设备名称不能超过255个字符")
    private String deviceName;

    /**
     * 下单标准
     */
    @NotBlank(message = "下单标准不能为空")
    @Size(max = 2000, message = "下单标准不能超过2000个字符")
    private String orderStandard;
} 