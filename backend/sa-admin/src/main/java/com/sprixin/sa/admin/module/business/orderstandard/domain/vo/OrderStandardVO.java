package com.sprixin.sa.admin.module.business.orderstandard.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 设备下单标准视图对象
 */
@Data
@Schema(description = "设备下单标准视图对象")
public class OrderStandardVO {

    @Schema(description = "序号")
    private Integer serialNumber;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "下单标准")
    private String orderStandard;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
} 