package com.sprixin.sa.admin.module.business.orderstandard.service;

import com.sprixin.sa.admin.module.business.orderstandard.domain.form.OrderStandardAddForm;
import com.sprixin.sa.admin.module.business.orderstandard.domain.form.OrderStandardQueryForm;
import com.sprixin.sa.admin.module.business.orderstandard.domain.form.OrderStandardUpdateForm;
import com.sprixin.sa.admin.module.business.orderstandard.domain.vo.OrderStandardVO;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.base.common.domain.ResponseDTO;

import java.util.List;

/**
 * 设备下单标准 服务接口
 */
public interface OrderStandardService {

    /**
     * 分页查询设备下单标准
     *
     * @param queryForm 查询参数
     * @return 分页结果
     */
    ResponseDTO<PageResult<OrderStandardVO>> queryOrderStandard(OrderStandardQueryForm queryForm);

    /**
     * 添加设备下单标准
     *
     * @param addForm 添加参数
     * @return 操作结果
     */
    ResponseDTO<String> addOrderStandard(OrderStandardAddForm addForm);

    /**
     * 更新设备下单标准
     *
     * @param updateForm 更新参数
     * @return 操作结果
     */
    ResponseDTO<String> updateOrderStandard(OrderStandardUpdateForm updateForm);

    /**
     * 删除设备下单标准
     *
     * @param id 设备下单标准ID
     * @return 操作结果
     */
    ResponseDTO<String> deleteOrderStandard(Integer id);

    /**
     * 批量删除设备下单标准
     *
     * @param idList ID列表
     * @return 操作结果
     */
    ResponseDTO<String> batchDeleteOrderStandard(List<Integer> idList);

    /**
     * 获取设备下单标准详情
     *
     * @param id 设备下单标准ID
     * @return 设备下单标准详情
     */
    ResponseDTO<OrderStandardVO> getOrderStandardDetail(Integer id);

    /**
     * 获取设备下单标准总数
     *
     * @return 总数
     */
    ResponseDTO<Long> getOrderStandardCount();
} 