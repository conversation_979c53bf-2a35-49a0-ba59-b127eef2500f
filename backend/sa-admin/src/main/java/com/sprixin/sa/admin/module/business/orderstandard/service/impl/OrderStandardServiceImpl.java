package com.sprixin.sa.admin.module.business.orderstandard.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.admin.module.business.orderstandard.dao.OrderStandardDao;
import com.sprixin.sa.admin.module.business.orderstandard.domain.entity.OrderStandardEntity;
import com.sprixin.sa.admin.module.business.orderstandard.domain.form.OrderStandardAddForm;
import com.sprixin.sa.admin.module.business.orderstandard.domain.form.OrderStandardQueryForm;
import com.sprixin.sa.admin.module.business.orderstandard.domain.form.OrderStandardUpdateForm;
import com.sprixin.sa.admin.module.business.orderstandard.domain.vo.OrderStandardVO;
import com.sprixin.sa.admin.module.business.orderstandard.service.OrderStandardService;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.base.common.domain.ResponseDTO;
import com.sprixin.sa.base.common.code.UserErrorCode;
import com.sprixin.sa.base.common.util.SmartPageUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备下单标准服务实现类
 */
@Slf4j
@Service
public class OrderStandardServiceImpl implements OrderStandardService {

    @Resource
    private OrderStandardDao orderStandardDao;

    /**
     * 查询设备下单标准列表
     * @param queryForm 查询条件
     * @return 设备下单标准列表
     */
    @Override
    public ResponseDTO<PageResult<OrderStandardVO>> queryOrderStandard(OrderStandardQueryForm queryForm) {
        try {
            // 参数验证
            if (queryForm == null) {
                log.error("Query form is null");
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "查询参数不能为空");
            }
            if (queryForm.getPageNum() == null || queryForm.getPageSize() == null) {
                log.error("Page parameters are null: pageNum={}, pageSize={}", 
                    queryForm.getPageNum(), queryForm.getPageSize());
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "分页参数不能为空");
            }
            
            // 创建分页对象，确保类型转换正确
            int pageNum = queryForm.getPageNum().intValue();
            int pageSize = queryForm.getPageSize().intValue();
            if (pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize < 1 || pageSize > 500) {
                pageSize = 10;
            }
            
            Page<OrderStandardVO> page = new Page<>(pageNum, pageSize);
            log.debug("Querying order standard with page: current={}, size={}, keyword={}", 
                page.getCurrent(), page.getSize(), queryForm.getKeyword());
            
            // 执行分页查询
            List<OrderStandardVO> list = orderStandardDao.queryOrderStandard(page, queryForm);
            if (list == null) {
                log.error("Query result is null");
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "查询结果为空");
            }
            log.debug("Query result size: {}", list.size());
            
            // 计算总记录数
            long total;
            try {
                // 使用新的计数方法
                total = orderStandardDao.selectCountByForm(queryForm);
                log.debug("Total records: {}", total);
            } catch (Exception e) {
                log.error("Error counting total records: {}", e.getMessage(), e);
                // 如果计数失败，使用列表大小作为总数
                total = list.size();
                log.warn("Using list size as total: {}", total);
            }
            page.setTotal(total);
            
            // 转换分页结果
            PageResult<OrderStandardVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
            log.debug("PageResult: pageNum={}, pageSize={}, total={}, listSize={}", 
                pageResult.getPageNum(), pageResult.getPageSize(), 
                pageResult.getTotal(), pageResult.getList() != null ? pageResult.getList().size() : 0);
            
            return ResponseDTO.ok(pageResult);
        } catch (Exception e) {
            log.error("Error querying order standard: {}", e.getMessage(), e);
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, 
                "查询设备下单标准列表失败：" + (e.getMessage() != null ? e.getMessage() : "未知错误"));
        }
    }

    /**
     * 添加设备下单标准
     * @param addForm 添加条件
     * @return 添加结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> addOrderStandard(OrderStandardAddForm addForm) {
        // 创建实体并保存
        OrderStandardEntity entity = new OrderStandardEntity();
        BeanUtil.copyProperties(addForm, entity);
        entity.setCreatedAt(LocalDateTime.now());
        orderStandardDao.insert(entity);
        return ResponseDTO.ok("添加成功");
    }

    /**
     * 更新设备下单标准
     * @param updateForm 更新条件
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> updateOrderStandard(OrderStandardUpdateForm updateForm) {
        // 检查设备下单标准是否存在
        OrderStandardEntity existEntity = orderStandardDao.selectById(updateForm.getSerialNumber());
        if (existEntity == null) {
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "设备下单标准不存在");
        }
        
        // 更新实体
        OrderStandardEntity entity = new OrderStandardEntity();
        BeanUtil.copyProperties(updateForm, entity);
        // 保留创建时间不变
        entity.setCreatedAt(existEntity.getCreatedAt());
        orderStandardDao.updateById(entity);
        return ResponseDTO.ok("更新成功");
    }

    /**
     * 删除设备下单标准
     * @param id 设备下单标准ID
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> deleteOrderStandard(Integer id) {
        // 检查设备下单标准是否存在
        OrderStandardEntity existEntity = orderStandardDao.selectById(id);
        if (existEntity == null) {
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "设备下单标准不存在");
        }
        
        // 删除设备下单标准
        orderStandardDao.deleteById(id);
        return ResponseDTO.ok("删除成功");
    }

    /**
     * 批量删除设备下单标准
     * @param idList 设备下单标准ID列表
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> batchDeleteOrderStandard(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "请选择要删除的设备下单标准");
        }
        
        // 批量删除
        orderStandardDao.deleteBatchIds(idList);
        return ResponseDTO.ok("批量删除成功");
    }

    /**
     * 获取设备下单标准详情
     * @param id 设备下单标准ID
     * @return 设备下单标准详情
     */ 
    @Override
    public ResponseDTO<OrderStandardVO> getOrderStandardDetail(Integer id) {
        OrderStandardVO orderStandardVO = orderStandardDao.getOrderStandardById(id);
        if (orderStandardVO == null) {
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "设备下单标准不存在");
        }
        return ResponseDTO.ok(orderStandardVO);
    }

    /**
     * 获取设备下单标准总数
     * @return 总数
    */
    @Override
    public ResponseDTO<Long> getOrderStandardCount() {
        try {
            Long count = orderStandardDao.selectCount(null);
            return ResponseDTO.ok(count);
        } catch (Exception e) {
            log.error("Error getting order standard count", e);
            throw e;
        }
    }
} 