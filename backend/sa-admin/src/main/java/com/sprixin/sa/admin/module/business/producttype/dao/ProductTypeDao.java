package com.sprixin.sa.admin.module.business.producttype.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sprixin.sa.admin.module.business.producttype.domain.entity.ProductTypeEntity;
import com.sprixin.sa.admin.module.business.producttype.domain.vo.ProductTypeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 产品类型 DAO
 */
@Mapper
public interface ProductTypeDao extends BaseMapper<ProductTypeEntity> {

    /**
     * 根据父级id，查询此父级直接子级的数量
     */
    Integer countSubProductType(@Param("parentId") Integer parentId);

    /**
     * 获取全部产品类型列表
     */
    List<ProductTypeVO> listAll();

    /**
     * 根据id查询产品类型
     */
    ProductTypeVO selectProductTypeVO(@Param("id") Integer id);
} 