package com.sprixin.sa.admin.module.business.producttype.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 产品类型实体类
 */
@Data
@TableName("y_product_type")
public class ProductTypeEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 产品名称
     */
    private String name;

    /**
     * 类型：产品线或产品类型
     */
    private String type;

    /**
     * 上级层级ID
     */
    private Integer parentId;

    /**
     * 软件著作权名称
     */
    private String softwareCopyrightName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    private Boolean isDeleted;
} 