package com.sprixin.sa.admin.module.business.producttype.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 产品类型 更新表单
 */
@Data
public class ProductTypeUpdateForm {

    @Schema(description = "主键id")
    @NotNull(message = "id不能为空")
    private Integer id;

    @Schema(description = "产品名称")
    @NotBlank(message = "产品名称不能为空")
    @Length(min = 1, max = 100, message = "产品名称长度必须在1-100个字符之间")
    private String name;

    @Schema(description = "类型：产品线或产品类型")
    @NotBlank(message = "类型不能为空")
    private String type;

    @Schema(description = "上级层级ID")
    private Integer parentId;

    @Schema(description = "软件著作权名称")
    @Length(max = 200, message = "软件著作权名称不能超过200个字符")
    private String softwareCopyrightName;
} 