package com.sprixin.sa.admin.module.business.producttype.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 产品类型树形VO
 */
@Data
public class ProductTypeTreeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "产品名称")
    private String name;

    @Schema(description = "类型：产品线或产品类型")
    private String type;

    @Schema(description = "上级层级ID")
    private Integer parentId;

    @Schema(description = "软件著作权名称")
    private String softwareCopyrightName;

    @Schema(description = "子节点")
    private List<ProductTypeTreeVO> children;
} 