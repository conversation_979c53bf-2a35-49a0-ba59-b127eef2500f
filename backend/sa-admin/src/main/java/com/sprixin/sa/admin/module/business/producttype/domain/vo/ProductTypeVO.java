package com.sprixin.sa.admin.module.business.producttype.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 产品类型VO
 */
@Data
public class ProductTypeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "产品名称")
    private String name;

    @Schema(description = "类型：产品线或产品类型")
    private String type;

    @Schema(description = "上级层级ID")
    private Integer parentId;

    @Schema(description = "上级层级名称")
    private String parentName;

    @Schema(description = "软件著作权名称")
    private String softwareCopyrightName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
} 