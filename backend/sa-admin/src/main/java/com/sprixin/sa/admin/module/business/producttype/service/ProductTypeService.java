package com.sprixin.sa.admin.module.business.producttype.service;

import com.sprixin.sa.admin.module.business.producttype.dao.ProductTypeDao;
import com.sprixin.sa.admin.module.business.producttype.domain.entity.ProductTypeEntity;
import com.sprixin.sa.admin.module.business.producttype.domain.form.ProductTypeAddForm;
import com.sprixin.sa.admin.module.business.producttype.domain.form.ProductTypeUpdateForm;
import com.sprixin.sa.admin.module.business.producttype.domain.vo.ProductTypeTreeVO;
import com.sprixin.sa.admin.module.business.producttype.domain.vo.ProductTypeVO;
import com.sprixin.sa.base.common.code.UserErrorCode;
import com.sprixin.sa.base.common.domain.ResponseDTO;
import com.sprixin.sa.base.common.util.SmartBeanUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品类型 service
 */
@Service
public class ProductTypeService {

    @Resource
    private ProductTypeDao productTypeDao;

    // ---------------------------- 增加、修改、删除 ----------------------------

    /**
     * 新增添加产品类型
     */
    public ResponseDTO<String> addProductType(ProductTypeAddForm productTypeAddForm) {
        // 新增查重逻辑
        String name = productTypeAddForm.getName();
        // 查询是否已存在同名且未删除的产品类型
        List<ProductTypeEntity> existList = productTypeDao.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<ProductTypeEntity>()
                .eq("name", name)
                .eq("is_deleted", false)
        );
        if (existList != null && !existList.isEmpty()) {
            return ResponseDTO.userErrorParam("产品名称已存在，请更换名称");
        }
        ProductTypeEntity productTypeEntity = SmartBeanUtil.copy(productTypeAddForm, ProductTypeEntity.class);
        productTypeEntity.setIsDeleted(false);
        productTypeDao.insert(productTypeEntity);
        return ResponseDTO.ok();
    }

    /**
     * 更新产品类型信息
     */
    public ResponseDTO<String> updateProductType(ProductTypeUpdateForm updateDTO) {
        ProductTypeEntity entity = productTypeDao.selectById(updateDTO.getId());
        if (entity == null) {
            return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST);
        }
        ProductTypeEntity productTypeEntity = SmartBeanUtil.copy(updateDTO, ProductTypeEntity.class);
        productTypeDao.updateById(productTypeEntity);
        return ResponseDTO.ok();
    }

    /**
     * 根据id删除产品类型
     * 1、需要判断当前产品类型是否有子类型,有子类型则不允许删除
     */
    public ResponseDTO<String> deleteProductType(Integer id) {
        ProductTypeEntity productTypeEntity = productTypeDao.selectById(id);
        if (null == productTypeEntity) {
            return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST);
        }
        // 是否有子级产品类型
        int subProductTypeNum = productTypeDao.countSubProductType(id);
        if (subProductTypeNum > 0) {
            return ResponseDTO.userErrorParam("请先删除子级产品类型");
        }
        // 物理删除
        productTypeDao.deleteById(id);
        return ResponseDTO.ok();
    }

    // ---------------------------- 查询 ----------------------------

    /**
     * 获取产品类型树形结构
     */
    public ResponseDTO<List<ProductTypeTreeVO>> productTypeTree() {
        List<ProductTypeVO> productTypeList = productTypeDao.listAll();
        List<ProductTypeTreeVO> treeVOList = buildProductTypeTree(productTypeList);
        return ResponseDTO.ok(treeVOList);
    }

    /**
     * 获取所有产品类型
     */
    public List<ProductTypeVO> listAll() {
        return productTypeDao.listAll();
    }

    /**
     * 获取所有启用的产品类型（用于下拉选择）
     */
    public ResponseDTO<List<ProductTypeVO>> getAllEnabled() {
        List<ProductTypeVO> allProductTypes = productTypeDao.listAll();
        // 过滤出启用的产品类型（假设没有disabled字段，则返回所有非删除的）
        // 如果有启用/禁用字段，可以在这里添加过滤逻辑
        return ResponseDTO.ok(allProductTypes);
    }

    /**
     * 获取产品类型
     */
    public ProductTypeVO getProductTypeById(Integer id) {
        return productTypeDao.selectProductTypeVO(id);
    }

    /**
     * 构建产品类型树
     */
    private List<ProductTypeTreeVO> buildProductTypeTree(List<ProductTypeVO> productTypeList) {
        if (productTypeList == null || productTypeList.isEmpty()) {
            return new ArrayList<>();
        }
        // 转换为树形VO
        List<ProductTypeTreeVO> treeVOList = productTypeList.stream()
                .map(e -> SmartBeanUtil.copy(e, ProductTypeTreeVO.class))
                .collect(Collectors.toList());
        // 构建父子关系
        Map<Integer, List<ProductTypeTreeVO>> parentMap = treeVOList.stream()
                .collect(Collectors.groupingBy(ProductTypeTreeVO::getParentId));
        // 设置子节点
        treeVOList.forEach(e -> e.setChildren(parentMap.get(e.getId())));
        // 返回顶级节点
        return treeVOList.stream()
                .filter(e -> e.getParentId() == null)
                .collect(Collectors.toList());
    }
} 