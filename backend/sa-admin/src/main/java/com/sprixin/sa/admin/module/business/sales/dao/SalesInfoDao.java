package com.sprixin.sa.admin.module.business.sales.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.admin.module.business.sales.domain.entity.SalesInfoEntity;
import com.sprixin.sa.admin.module.business.sales.domain.form.SalesInfoQueryForm;
import com.sprixin.sa.admin.module.business.sales.domain.vo.SalesInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 销售信息 dao
 */
@Mapper
public interface SalesInfoDao extends BaseMapper<SalesInfoEntity> {
    
    /**
     * 查询销售信息列表
     */
    List<SalesInfoVO> querySalesInfo(Page page, @Param("queryForm") SalesInfoQueryForm queryForm);
    
    /**
     * 根据ID查询销售信息详情
     */
    SalesInfoVO getSalesInfoById(@Param("id") Long id);

    /**
     * 根据查询表单统计总数
     */
    Long selectCountByForm(@Param("queryForm") SalesInfoQueryForm queryForm);

    /**
     * 查询所有销售人员列表（用于下拉选择）
     */
    List<SalesInfoVO> queryAllSales();
}