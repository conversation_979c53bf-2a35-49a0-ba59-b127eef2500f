package com.sprixin.sa.admin.module.business.sales.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 销售信息 实体表
 */
@Data
@TableName("y_sales_info")
public class SalesInfoEntity {

    /**
     * 主键ID，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 销售姓名
     */
    private String salesName;

    /**
     * 手机号码
     */
    private String mobilePhone;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 职务
     */
    private String position;

    /**
     * 负责区域
     */
    private String region;

    /**
     * 在职情况
     */
    private String employmentStatus;

    /**
     * 记录日期
     */
    private LocalDate recordDate;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 