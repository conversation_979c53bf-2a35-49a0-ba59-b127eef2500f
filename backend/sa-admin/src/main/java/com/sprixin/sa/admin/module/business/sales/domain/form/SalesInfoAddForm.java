package com.sprixin.sa.admin.module.business.sales.domain.form;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import java.time.LocalDate;

/**
 * 销售信息 添加表单
 */
@Data
public class SalesInfoAddForm {

    /**
     * 销售姓名
     */
    @NotBlank(message = "销售姓名不能为空")
    private String salesName;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String mobilePhone;

    /**
     * 邮箱地址
     */
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 职务
     */
    private String position;

    /**
     * 负责区域
     */
    private String region;

    /**
     * 在职情况
     */
    @NotNull(message = "在职情况不能为空")
    private String employmentStatus;

    /**
     * 记录日期
     * 由后端自动生成，前端无需传递
     */
    private LocalDate recordDate;
} 