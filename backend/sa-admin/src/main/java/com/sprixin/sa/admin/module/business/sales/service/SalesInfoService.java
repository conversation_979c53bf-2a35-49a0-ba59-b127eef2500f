package com.sprixin.sa.admin.module.business.sales.service;

import com.sprixin.sa.admin.module.business.sales.domain.form.SalesInfoAddForm;
import com.sprixin.sa.admin.module.business.sales.domain.form.SalesInfoQueryForm;
import com.sprixin.sa.admin.module.business.sales.domain.form.SalesInfoUpdateForm;
import com.sprixin.sa.admin.module.business.sales.domain.vo.SalesInfoVO;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.base.common.domain.ResponseDTO;

import jakarta.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * 销售信息 服务接口
 */
public interface SalesInfoService {

    /**
     * 分页查询销售信息
     *
     * @param queryForm 查询参数
     * @return 分页结果
     */
    ResponseDTO<PageResult<SalesInfoVO>> querySalesInfo(SalesInfoQueryForm queryForm);

    /**
     * 添加销售信息
     *
     * @param addForm 添加参数
     * @return 操作结果
     */
    ResponseDTO<String> addSalesInfo(SalesInfoAddForm addForm);

    /**
     * 更新销售信息
     *
     * @param updateForm 更新参数
     * @return 操作结果
     */
    ResponseDTO<String> updateSalesInfo(SalesInfoUpdateForm updateForm);

    /**
     * 删除销售信息
     *
     * @param id 销售信息ID
     * @return 操作结果
     */
    ResponseDTO<String> deleteSalesInfo(Long id);

    /**
     * 批量删除销售信息
     *
     * @param idList ID列表
     * @return 操作结果
     */
    ResponseDTO<String> batchDeleteSalesInfo(List<Long> idList);

    /**
     * 获取销售信息详情
     *
     * @param id 销售信息ID
     * @return 销售信息详情
     */
    ResponseDTO<SalesInfoVO> getSalesInfoDetail(Long id);

    /**
     * 获取销售信息详情
     */
    ResponseDTO<SalesInfoVO> getDetail(Long id);

    /**
     * 导出销售信息
     */
    void exportSalesInfo(SalesInfoQueryForm queryForm, HttpServletResponse response) throws UnsupportedEncodingException;

    /**
     * 获取所有销售人员列表（用于下拉选择）
     *
     * @return 销售人员列表
     */
    ResponseDTO<List<SalesInfoVO>> queryAllSales();
}