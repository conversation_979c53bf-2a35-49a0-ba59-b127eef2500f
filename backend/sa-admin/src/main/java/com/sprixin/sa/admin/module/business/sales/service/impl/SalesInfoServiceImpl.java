package com.sprixin.sa.admin.module.business.sales.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.admin.module.business.sales.dao.SalesInfoDao;
import com.sprixin.sa.admin.module.business.sales.domain.entity.SalesInfoEntity;
import com.sprixin.sa.admin.module.business.sales.domain.form.SalesInfoAddForm;
import com.sprixin.sa.admin.module.business.sales.domain.form.SalesInfoQueryForm;
import com.sprixin.sa.admin.module.business.sales.domain.form.SalesInfoUpdateForm;
import com.sprixin.sa.admin.module.business.sales.domain.vo.SalesInfoVO;
import com.sprixin.sa.admin.module.business.sales.service.SalesInfoService;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.base.common.domain.ResponseDTO;
import com.sprixin.sa.base.common.code.UserErrorCode;
import com.sprixin.sa.base.common.util.SmartPageUtil;
import com.sprixin.sa.base.common.util.SmartStringUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.io.UnsupportedEncodingException;

/**
 * 销售信息服务实现类
 */
@Slf4j
@Service
public class SalesInfoServiceImpl implements SalesInfoService {

    @Resource
    private SalesInfoDao salesInfoDao;

    @Override
    public ResponseDTO<PageResult<SalesInfoVO>> querySalesInfo(SalesInfoQueryForm queryForm) {
        Page<SalesInfoVO> page = new Page<>(queryForm.getPageNum(), queryForm.getPageSize());
        List<SalesInfoVO> list = salesInfoDao.querySalesInfo(page, queryForm);
        Long total = salesInfoDao.selectCountByForm(queryForm);
        return ResponseDTO.ok(SmartPageUtil.convert2PageResult(page, list));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> addSalesInfo(SalesInfoAddForm addForm) {
        SalesInfoEntity entity = new SalesInfoEntity();
        BeanUtil.copyProperties(addForm, entity);
        entity.setRecordDate(LocalDate.now()); // 自动设置记录日期为当前日期
        entity.setCreatedAt(LocalDateTime.now());
        entity.setUpdatedAt(LocalDateTime.now());
        salesInfoDao.insert(entity);
        return ResponseDTO.ok("添加成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> updateSalesInfo(SalesInfoUpdateForm updateForm) {
        SalesInfoEntity existEntity = salesInfoDao.selectById(updateForm.getId());
        if (existEntity == null) {
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "销售信息不存在");
        }
        
        SalesInfoEntity entity = new SalesInfoEntity();
        BeanUtil.copyProperties(updateForm, entity);
        entity.setRecordDate(LocalDate.now()); // 自动设置记录日期为当前日期
        entity.setUpdatedAt(LocalDateTime.now());
        salesInfoDao.updateById(entity);
        return ResponseDTO.ok("更新成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> deleteSalesInfo(Long id) {
        SalesInfoEntity existEntity = salesInfoDao.selectById(id);
        if (existEntity == null) {
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "销售信息不存在");
        }
        
        salesInfoDao.deleteById(id);
        return ResponseDTO.ok("删除成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> batchDeleteSalesInfo(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "请选择要删除的数据");
        }
        
        salesInfoDao.deleteBatchIds(idList);
        return ResponseDTO.ok("批量删除成功");
    }

    @Override
    public ResponseDTO<SalesInfoVO> getSalesInfoDetail(Long id) {
        SalesInfoVO vo = salesInfoDao.getSalesInfoById(id);
        if (vo == null) {
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "销售信息不存在");
        }
        return ResponseDTO.ok(vo);
    }

    @Override
    public ResponseDTO<SalesInfoVO> getDetail(Long id) {
        return getSalesInfoDetail(id);
    }

    @Override
    public void exportSalesInfo(SalesInfoQueryForm queryForm, HttpServletResponse response) throws UnsupportedEncodingException {
        log.info("开始导出销售信息，查询条件: {}", queryForm);
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/csv");
        response.setHeader("Content-Disposition", "attachment;filename=" + 
            URLEncoder.encode("销售信息列表.csv", "UTF-8").replaceAll("\\+", "%20"));
        
        try (PrintWriter writer = response.getWriter()) {
            // 写入CSV标题
            String[] headers = {"销售姓名", "手机号码", "邮箱地址", "职务", "负责区域", "在职情况", "记录日期"};
            writer.println(String.join(",", headers));
            
            // 查询所有数据
            Page<SalesInfoVO> page = new Page<>();
            page.setCurrent(1);
            page.setSize(Long.MAX_VALUE);
            List<SalesInfoVO> list = salesInfoDao.querySalesInfo(page, queryForm);
            log.info("查询到销售信息记录数: {}", list.size());
            
            // 写入数据行
            for (SalesInfoVO vo : list) {
                String[] data = {
                    escapeCsv(vo.getSalesName()),
                    escapeCsv(vo.getMobilePhone()),
                    escapeCsv(vo.getEmail()),
                    escapeCsv(vo.getPosition()),
                    escapeCsv(vo.getRegion()),
                    escapeCsv(vo.getEmploymentStatus()),
                    vo.getRecordDate() != null ? vo.getRecordDate().toString() : ""
                };
                writer.println(String.join(",", data));
            }
            log.info("CSV数据写入成功");
        } catch (Exception e) {
            log.error("导出CSV异常:", e);
            try {
                response.reset();
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"message\":\"导出CSV失败\"}");
            } catch (IOException ex) {
                log.error("写入错误响应失败:", ex);
            }
        }
    }
    
    /**
     * 转义CSV字段中的特殊字符
     * @param field 字段值
     * @return 转义后的字段值
     */
    private String escapeCsv(String field) {
        if (field == null) {
            return "";
        }
        
        // 如果字段包含逗号、引号或换行符，需要用引号包围并转义内部引号
        if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
            return "\"" + field.replace("\"", "\"\"") + "\"";
        }
        return field;
    }

    @Override
    public ResponseDTO<List<SalesInfoVO>> queryAllSales() {
        List<SalesInfoVO> list = salesInfoDao.queryAllSales();
        return ResponseDTO.ok(list);
    }
}