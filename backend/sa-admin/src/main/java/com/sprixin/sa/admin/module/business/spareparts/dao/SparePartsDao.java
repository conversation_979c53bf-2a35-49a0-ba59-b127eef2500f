package com.sprixin.sa.admin.module.business.spareparts.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.admin.module.business.spareparts.domain.entity.SparePartsEntity;
import com.sprixin.sa.admin.module.business.spareparts.domain.form.SparePartsQueryForm;
import com.sprixin.sa.admin.module.business.spareparts.domain.vo.SparePartsVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 备品备件 dao
 */
@Mapper
public interface SparePartsDao extends BaseMapper<SparePartsEntity> {
    
    /**
     * 查询备品备件列表
     */
    List<SparePartsVO> querySpareParts(Page page, @Param("queryForm") SparePartsQueryForm queryForm);
    
    /**
     * 查询所有备品备件
     */
    List<SparePartsEntity> findAll();

    /**
     * 根据ID查询备品备件详情
     */
    SparePartsVO getSparePartsById(@Param("id") Integer id);

    /**
     * 根据查询表单统计总数
     */
    Long selectCountByForm(@Param("queryForm") SparePartsQueryForm queryForm);
} 