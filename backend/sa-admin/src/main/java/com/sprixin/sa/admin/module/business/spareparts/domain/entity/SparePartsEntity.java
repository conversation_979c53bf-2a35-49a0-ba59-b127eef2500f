package com.sprixin.sa.admin.module.business.spareparts.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 备品备件 实体表
 */
@Data
@TableName("y_spare_parts")
public class SparePartsEntity {

    /**
     * 序号，主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 投标品牌
     */
    private String brand;

    /**
     * 投标型号
     */
    private String model;

    /**
     * 投标配置
     */
    private String configuration;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;
} 