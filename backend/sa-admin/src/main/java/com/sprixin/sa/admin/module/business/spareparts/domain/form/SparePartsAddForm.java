package com.sprixin.sa.admin.module.business.spareparts.domain.form;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 备品备件 添加表单
 */
@Data
public class SparePartsAddForm {

    /**
     * 设备名称
     */
    @NotBlank(message = "设备名称不能为空")
    private String deviceName;

    /**
     * 投标品牌
     */
    private String brand;

    /**
     * 投标型号
     */
    private String model;

    /**
     * 投标配置
     */
    private String configuration;

    /**
     * 单价
     */
    @NotNull(message = "单价不能为空")
    private BigDecimal unitPrice;
} 