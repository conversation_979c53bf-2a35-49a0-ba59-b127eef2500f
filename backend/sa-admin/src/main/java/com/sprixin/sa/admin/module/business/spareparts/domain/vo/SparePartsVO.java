package com.sprixin.sa.admin.module.business.spareparts.domain.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 备品备件 视图对象
 */
@Data
public class SparePartsVO {

    /**
     * 序号，主键
     */
    private Integer id;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 投标品牌
     */
    private String brand;

    /**
     * 投标型号
     */
    private String model;

    /**
     * 投标配置
     */
    private String configuration;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;
} 