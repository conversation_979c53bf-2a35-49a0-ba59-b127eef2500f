package com.sprixin.sa.admin.module.business.spareparts.service;

import com.sprixin.sa.admin.module.business.spareparts.domain.form.SparePartsAddForm;
import com.sprixin.sa.admin.module.business.spareparts.domain.form.SparePartsQueryForm;
import com.sprixin.sa.admin.module.business.spareparts.domain.form.SparePartsUpdateForm;
import com.sprixin.sa.admin.module.business.spareparts.domain.vo.SparePartsVO;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.base.common.domain.ResponseDTO;

import java.util.List;

/**
 * 备品备件 服务接口
 */
public interface SparePartsService {

    /**
     * 分页查询备品备件
     *
     * @param queryForm 查询参数
     * @return 分页结果
     */
    ResponseDTO<PageResult<SparePartsVO>> querySpareParts(SparePartsQueryForm queryForm);

    /**
     * 添加备品备件
     *
     * @param addForm 添加参数
     * @return 操作结果
     */
    ResponseDTO<String> addSpareParts(SparePartsAddForm addForm);

    /**
     * 更新备品备件
     *
     * @param updateForm 更新参数
     * @return 操作结果
     */
    ResponseDTO<String> updateSpareParts(SparePartsUpdateForm updateForm);

    /**
     * 删除备品备件
     *
     * @param id 备品备件ID
     * @return 操作结果
     */
    ResponseDTO<String> deleteSpareParts(Integer id);

    /**
     * 批量删除备品备件
     *
     * @param idList ID列表
     * @return 操作结果
     */
    ResponseDTO<String> batchDeleteSpareParts(List<Integer> idList);

    /**
     * 获取备品备件详情
     *
     * @param id 备品备件ID
     * @return 备品备件详情
     */
    ResponseDTO<SparePartsVO> getSparePartsDetail(Integer id);

    /**
     * 获取所有备品备件并返回JSON数组字符串
     *
     * @return 所有备品备件的JSON数组字符串
     */
    ResponseDTO<String> getAllSparePartsAsJson();

    /**
     * 获取备品备件总数
     *
     * @return 总数
     */
    ResponseDTO<Long> getSparePartsCount();
} 