package com.sprixin.sa.admin.module.business.spareparts.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.admin.module.business.spareparts.dao.SparePartsDao;
import com.sprixin.sa.admin.module.business.spareparts.domain.entity.SparePartsEntity;
import com.sprixin.sa.admin.module.business.spareparts.domain.form.SparePartsAddForm;
import com.sprixin.sa.admin.module.business.spareparts.domain.form.SparePartsQueryForm;
import com.sprixin.sa.admin.module.business.spareparts.domain.form.SparePartsUpdateForm;
import com.sprixin.sa.admin.module.business.spareparts.domain.vo.SparePartsVO;
import com.sprixin.sa.admin.module.business.spareparts.service.SparePartsService;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.base.common.domain.ResponseDTO;
import com.sprixin.sa.base.common.code.UserErrorCode;
import com.sprixin.sa.base.common.util.SmartPageUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 备品备件服务实现类
 */
@Slf4j
@Service
public class SparePartsServiceImpl implements SparePartsService {

    @Resource
    private SparePartsDao sparePartsDao;
    @Resource
    private ObjectMapper objectMapper;

    /**
     * 查询备品备件列表
     * @param queryForm 查询条件
     * @return 备品备件列表
     */
    @Override
    public ResponseDTO<PageResult<SparePartsVO>> querySpareParts(SparePartsQueryForm queryForm) {
        try {
            // 参数验证
            if (queryForm == null) {
                log.error("Query form is null");
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "查询参数不能为空");
            }
            if (queryForm.getPageNum() == null || queryForm.getPageSize() == null) {
                log.error("Page parameters are null: pageNum={}, pageSize={}", 
                    queryForm.getPageNum(), queryForm.getPageSize());
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "分页参数不能为空");
            }
            
            // 创建分页对象，确保类型转换正确
            int pageNum = queryForm.getPageNum().intValue();
            int pageSize = queryForm.getPageSize().intValue();
            if (pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize < 1 || pageSize > 500) {
                pageSize = 10;
            }
            
            Page<SparePartsVO> page = new Page<>(pageNum, pageSize);
            log.debug("Querying spare parts with page: current={}, size={}, keyword={}", 
                page.getCurrent(), page.getSize(), queryForm.getKeyword());
            
            // 执行分页查询
            List<SparePartsVO> list = sparePartsDao.querySpareParts(page, queryForm);
            if (list == null) {
                log.error("Query result is null");
                return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "查询结果为空");
            }
            log.debug("Query result size: {}", list.size());
            
            // 计算总记录数
            long total;
            try {
                // 使用新的计数方法
                total = sparePartsDao.selectCountByForm(queryForm);
                log.debug("Total records: {}", total);
            } catch (Exception e) {
                log.error("Error counting total records: {}", e.getMessage(), e);
                // 如果计数失败，使用列表大小作为总数
                total = list.size();
                log.warn("Using list size as total: {}", total);
            }
            page.setTotal(total);
            
            // 转换分页结果
            PageResult<SparePartsVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
            log.debug("PageResult: pageNum={}, pageSize={}, total={}, listSize={}", 
                pageResult.getPageNum(), pageResult.getPageSize(), 
                pageResult.getTotal(), pageResult.getList() != null ? pageResult.getList().size() : 0);
            
            return ResponseDTO.ok(pageResult);
        } catch (Exception e) {
            log.error("Error querying spare parts: {}", e.getMessage(), e);
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, 
                "查询备品备件列表失败：" + (e.getMessage() != null ? e.getMessage() : "未知错误"));
        }
    }

    /**
     * 添加备品备件
     * @param addForm 添加条件
     * @return 添加结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> addSpareParts(SparePartsAddForm addForm) {
        // 创建实体并保存
        SparePartsEntity entity = new SparePartsEntity();
        BeanUtil.copyProperties(addForm, entity);
        entity.setUpdatedAt(LocalDateTime.now());
        sparePartsDao.insert(entity);
        return ResponseDTO.ok("添加成功");
    }

    /**
     * 更新备品备件
     * @param updateForm 更新条件
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> updateSpareParts(SparePartsUpdateForm updateForm) {
        // 检查备品备件是否存在
        SparePartsEntity existEntity = sparePartsDao.selectById(updateForm.getId());
        if (existEntity == null) {
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "备品备件不存在");
        }
        
        // 更新实体
        SparePartsEntity entity = new SparePartsEntity();
        BeanUtil.copyProperties(updateForm, entity);
        entity.setUpdatedAt(LocalDateTime.now());
        sparePartsDao.updateById(entity);
        return ResponseDTO.ok("更新成功");
    }

    /**
     * 删除备品备件
     * @param id 备品备件ID
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> deleteSpareParts(Integer id) {
        // 检查备品备件是否存在
        SparePartsEntity existEntity = sparePartsDao.selectById(id);
        if (existEntity == null) {
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "备品备件不存在");
        }
        
        // 删除备品备件
        sparePartsDao.deleteById(id);
        return ResponseDTO.ok("删除成功");
    }

    /**
     * 批量删除备品备件
     * @param idList 备品备件ID列表
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> batchDeleteSpareParts(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "请选择要删除的备品备件");
        }
        
        // 批量删除
        sparePartsDao.deleteBatchIds(idList);
        return ResponseDTO.ok("批量删除成功");
    }

    /**
     * 获取备品备件详情
     * @param id 备品备件ID
     * @return 备品备件详情
     */ 
    @Override
    public ResponseDTO<SparePartsVO> getSparePartsDetail(Integer id) {
        SparePartsVO sparePartsVO = sparePartsDao.getSparePartsById(id);
        if (sparePartsVO == null) {
            return ResponseDTO.error(UserErrorCode.PARAM_ERROR, "备品备件不存在");
        }
        return ResponseDTO.ok(sparePartsVO);
    }

    /**
     * 获取备品备件总数
     * @return 总数
    */
    @Override
    public ResponseDTO<Long> getSparePartsCount() {
        try {
            Long count = sparePartsDao.selectCount(null);
            return ResponseDTO.ok(count);
        } catch (Exception e) {
            log.error("Error getting spare parts count", e);
            throw e;
        }
    }

    /**
     * 获取所有备品备件并返回JSON数组字符串
     *
     * @return 所有备品备件的JSON数组字符串
     */
    @Override
    public ResponseDTO<String> getAllSparePartsAsJson() {
        try {
            List<SparePartsEntity> entities = sparePartsDao.findAll();
            List<SparePartsVO> vos = entities.stream()
                .map(entity -> BeanUtil.copyProperties(entity, SparePartsVO.class))
                .collect(Collectors.toList());
            String jsonArray = objectMapper.writeValueAsString(vos);
            return ResponseDTO.ok(jsonArray);
        } catch (Exception e) {
            log.error("Error getting all spare parts as JSON", e);
            return ResponseDTO.error(UserErrorCode.SYSTEM_ERROR, "获取所有备品备件失败");
        }
    }
} 