package com.sprixin.sa.admin.module.business.techsolution.domain;

import lombok.Data;

import java.util.Date;

/**
 * 分页参数
 *
 * <AUTHOR>
 */
@Data
public class PageParam {
    
    /**
     * 当前页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页记录数
     */
    private Integer pageSize = 10;
    
    /**
     * 搜索参数
     */
    private String searchParam;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 开始时间
     */
    private Date startTime;
    
    /**
     * 结束时间
     */
    private Date endTime;
    
    /**
     * 排序字段
     */
    private String sortField;
    
    /**
     * 排序方式，asc或desc
     */
    private String sortOrder;
}