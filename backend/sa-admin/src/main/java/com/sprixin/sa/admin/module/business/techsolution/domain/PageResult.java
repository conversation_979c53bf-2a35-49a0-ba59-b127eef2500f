package com.sprixin.sa.admin.module.business.techsolution.domain;

import lombok.Data;

import java.util.List;

/**
 * 分页结果（临时实现）
 *
 * <AUTHOR>
 */
@Data
public class PageResult<T> {
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 当前页码
     */
    private Integer pageNum;
    
    /**
     * 每页记录数
     */
    private Integer pageSize;
    
    /**
     * 数据列表
     */
    private List<T> records;
    
    /**
     * 总页数
     */
    private Integer pages;
} 