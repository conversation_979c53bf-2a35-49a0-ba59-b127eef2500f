package com.sprixin.sa.admin.module.business.techsolution.domain.dto;

import lombok.Data;

import java.io.Serializable;

import jakarta.validation.constraints.NotBlank;

/**
 * 技术方案生成DTO
 *
 * <AUTHOR>
 */
@Data
public class TechSolutionGenerateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    private String title;

    /**
     * 提示词
     */
    @NotBlank(message = "提示词不能为空")
    private String prompt;

    /**
     * API Key
     */
    private String apiKey;

    /**
     * 文件ID列表，逗号分隔
     */
    private String fileIds;
} 