package com.sprixin.sa.admin.module.business.techsolution.domain.dto;

import lombok.Data;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 技术方案内容更新DTO
 *
 * <AUTHOR>
 */
@Data
public class TechSolutionUpdateContentDTO {
    
    /**
     * 技术方案ID
     */
    @NotNull(message = "技术方案ID不能为空")
    private Long id;
    
    /**
     * 更新后的内容
     */
    @Size(max = 65535, message = "内容长度不能超过65535")
    private String content;
} 