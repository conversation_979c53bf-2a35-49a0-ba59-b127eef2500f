package com.sprixin.sa.admin.module.business.techsolution.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 技术方案实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("tech_solution")
public class TechSolutionEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 提示词
     */
    private String prompt;

    /**
     * API Key (加密存储)
     */
    private String apiKey;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 状态：PROCESSING-处理中, COMPLETED-已完成, FAILED-失败
     */
    private String status;
} 