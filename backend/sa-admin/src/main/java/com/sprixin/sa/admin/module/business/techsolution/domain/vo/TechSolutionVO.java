package com.sprixin.sa.admin.module.business.techsolution.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 技术方案VO
 *
 * <AUTHOR>
 */
@Data
public class TechSolutionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 提示词
     */
    private String prompt;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 状态：PROCESSING-处理中, COMPLETED-已完成, FAILED-失败
     */
    private String status;
} 