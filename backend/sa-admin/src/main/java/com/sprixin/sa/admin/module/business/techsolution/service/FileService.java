package com.sprixin.sa.admin.module.business.techsolution.service;

import com.sprixin.sa.admin.module.business.techsolution.domain.entity.FileEntity;

/**
 * 文件Service接口（临时实现）
 *
 * <AUTHOR>
 */
public interface FileService {
    
    /**
     * 根据ID获取文件
     *
     * @param fileId 文件ID
     * @return 文件实体
     */
    FileEntity getById(Long fileId);
    
    /**
     * 获取文件内容
     *
     * @param fileId 文件ID
     * @return 文件内容
     */
    String getFileContent(Long fileId);
} 