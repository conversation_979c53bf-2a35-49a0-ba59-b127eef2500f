package com.sprixin.sa.admin.module.business.techsolution.service;

import com.sprixin.sa.admin.module.business.techsolution.domain.PageParam;
import com.sprixin.sa.admin.module.business.techsolution.domain.PageResult;
import com.sprixin.sa.admin.module.business.techsolution.domain.dto.TechSolutionGenerateDTO;
import com.sprixin.sa.admin.module.business.techsolution.domain.dto.TechSolutionUpdateContentDTO;
import com.sprixin.sa.admin.module.business.techsolution.domain.entity.TechSolutionEntity;
import com.sprixin.sa.admin.module.business.techsolution.domain.vo.TechSolutionVO;

import java.util.List;

/**
 * 技术方案Service接口
 *
 * <AUTHOR>
 */
public interface TechSolutionService {

    /**
     * 生成技术方案
     *
     * @param generateDTO 生成参数
     * @return 技术方案VO
     */
    TechSolutionVO generateTechSolution(TechSolutionGenerateDTO generateDTO);

    /**
     * 分页查询
     *
     * @param pageParam 分页参数
     * @return 分页结果
     */
    PageResult<TechSolutionEntity> queryPage(PageParam pageParam);

    /**
     * 根据ID获取
     *
     * @param id 技术方案ID
     * @return 技术方案实体
     */
    TechSolutionEntity getById(Long id);

    /**
     * 根据ID删除
     *
     * @param id 技术方案ID
     */
    void deleteById(Long id);

    /**
     * 批量删除
     *
     * @param idList ID列表
     */
    void batchDelete(List<Long> idList);
    
    /**
     * 更新技术方案内容
     *
     * @param updateDTO 更新内容参数
     * @return 更新后的技术方案VO
     */
    TechSolutionVO updateContent(TechSolutionUpdateContentDTO updateDTO);
} 