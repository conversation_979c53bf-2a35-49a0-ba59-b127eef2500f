package com.sprixin.sa.admin.module.business.techsolution.service.impl;

import com.sprixin.sa.admin.module.business.techsolution.domain.vo.EmployeeVO;
import com.sprixin.sa.admin.module.business.techsolution.service.EmployeeService;
import org.springframework.stereotype.Service;

/**
 * 员工Service实现类（临时实现）
 *
 * <AUTHOR>
 */
@Service
public class EmployeeServiceImpl implements EmployeeService {
    
    @Override
    public EmployeeVO getCurrentLoginEmployee() {
        // 临时实现，返回模拟数据
        EmployeeVO employeeVO = new EmployeeVO();
        employeeVO.setEmployeeId(1L);
        employeeVO.setEmployeeName("管理员");
        return employeeVO;
    }
} 