package com.sprixin.sa.admin.module.business.techsolution.service.impl;

import com.sprixin.sa.admin.module.business.techsolution.domain.entity.FileEntity;
import com.sprixin.sa.admin.module.business.techsolution.service.FileService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件Service实现类（临时实现）
 *
 * <AUTHOR>
 */
@Service
public class FileServiceImpl implements FileService {
    
    // 模拟文件存储
    private static final Map<Long, FileEntity> FILE_MAP = new HashMap<>();
    
    static {
        // 添加一些模拟数据
        FileEntity file1 = new FileEntity();
        file1.setFileId(1L);
        file1.setFileName("示例文档1.docx");
        file1.setFileType("docx");
        file1.setFileSize(1024L);
        file1.setFileUrl("/upload/public/common/file1.docx");
        FILE_MAP.put(1L, file1);
        
        FileEntity file2 = new FileEntity();
        file2.setFileId(2L);
        file2.setFileName("示例文档2.pdf");
        file2.setFileType("pdf");
        file2.setFileSize(2048L);
        file2.setFileUrl("/upload/public/common/file2.pdf");
        FILE_MAP.put(2L, file2);
    }
    
    @Override
    public FileEntity getById(Long fileId) {
        return FILE_MAP.get(fileId);
    }
    
    @Override
    public String getFileContent(Long fileId) {
        // 模拟返回文件内容
        if (fileId != null && FILE_MAP.containsKey(fileId)) {
            return "这是文件ID为" + fileId + "的文件内容，实际项目中应该读取真实文件内容。";
        }
        return null;
    }
} 