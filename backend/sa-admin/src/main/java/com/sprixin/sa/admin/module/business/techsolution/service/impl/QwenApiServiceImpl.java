package com.sprixin.sa.admin.module.business.techsolution.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sprixin.sa.admin.module.business.techsolution.service.QwenApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * Qwen API服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class QwenApiServiceImpl implements QwenApiService {
    
    @Value("${qwen.api.url}")
    private String apiUrl;

    @Value("${qwen.api.key}")
    private String defaultApiKey;

    @Value("${qwen.api.model}")
    private String model;
    
    @Override
    public String generateSolution(String prompt, String apiKey, String[] fileContents) {
        try {
            // 使用传入的apiKey，如果为空则使用默认的
            String actualApiKey = StringUtils.isNotBlank(apiKey) ? apiKey : defaultApiKey;
            
            // 日志输出，帮助调试
            log.info("使用API密钥: {}, API URL: {}, 模型: {}", 
                     actualApiKey.length() > 5 ? actualApiKey.substring(0, 5) + "..." : "无效密钥", 
                     apiUrl, model);
            
            // 准备请求主体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            
            // 构建消息
            List<Map<String, Object>> messages = new ArrayList<>();
            
            // 系统消息
            Map<String, Object> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", "你是一个技术方案生成专家，可以根据用户的需求和参考文档生成详细的技术方案。");
            messages.add(systemMessage);
            
            // 构建用户消息内容，包含提示词和文档内容
            StringBuilder userContent = new StringBuilder();
            userContent.append(prompt).append("\n\n");
            
            if (fileContents != null && fileContents.length > 0) {
                userContent.append("参考文档：\n\n");
                for (int i = 0; i < fileContents.length; i++) {
                    if (StringUtils.isNotBlank(fileContents[i])) {
                        userContent.append("文档 ").append(i + 1).append(":\n");
                        userContent.append(fileContents[i]).append("\n\n");
                    }
                }
            }
            
            // 用户消息
            Map<String, Object> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", userContent.toString());
            messages.add(userMessage);
            
            requestBody.put("messages", messages);
            requestBody.put("max_tokens", 4000);
            requestBody.put("temperature", 0.7);
            
            // 准备HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + actualApiKey);
            
            // 发送请求
            RestTemplate restTemplate = new RestTemplate();
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            log.info("发送请求到Qwen API: {}", apiUrl);
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                apiUrl,
                HttpMethod.POST,
                requestEntity,
                String.class
            );
            
            // 处理响应
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                String responseBody = responseEntity.getBody();
                log.info("Qwen API响应成功");
                
                // 解析响应
                JSONObject jsonResponse = JSON.parseObject(responseBody);
                JSONObject choices = jsonResponse.getJSONArray("choices").getJSONObject(0);
                JSONObject message = choices.getJSONObject("message");
                String content = message.getString("content");
                
                return content;
            } else {
                log.error("Qwen API调用失败，状态码: {}", responseEntity.getStatusCodeValue());
                return "调用Qwen API失败，状态码: " + responseEntity.getStatusCodeValue();
            }
            
        } catch (Exception e) {
            log.error("调用Qwen API异常", e);
            return "生成技术方案失败: " + e.getMessage();
        }
    }
} 