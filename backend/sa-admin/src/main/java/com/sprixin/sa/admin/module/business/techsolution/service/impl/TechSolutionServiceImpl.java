package com.sprixin.sa.admin.module.business.techsolution.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.admin.module.business.techsolution.dao.TechSolutionFileDao;
import com.sprixin.sa.admin.module.business.techsolution.dao.TechSolutionDao;
import com.sprixin.sa.admin.module.business.techsolution.domain.PageParam;
import com.sprixin.sa.admin.module.business.techsolution.domain.PageResult;
import com.sprixin.sa.admin.module.business.techsolution.domain.dto.TechSolutionGenerateDTO;
import com.sprixin.sa.admin.module.business.techsolution.domain.dto.TechSolutionUpdateContentDTO;
import com.sprixin.sa.admin.module.business.techsolution.domain.entity.FileEntity;
import com.sprixin.sa.admin.module.business.techsolution.domain.entity.TechSolutionEntity;
import com.sprixin.sa.admin.module.business.techsolution.domain.entity.TechSolutionFileEntity;
import com.sprixin.sa.admin.module.business.techsolution.domain.vo.EmployeeVO;
import com.sprixin.sa.admin.module.business.techsolution.domain.vo.TechSolutionVO;
import com.sprixin.sa.admin.module.business.techsolution.service.QwenApiService;
import com.sprixin.sa.admin.module.business.techsolution.service.EmployeeService;
import com.sprixin.sa.admin.module.business.techsolution.service.FileService;
import com.sprixin.sa.admin.module.business.techsolution.service.TechSolutionService;
import com.sprixin.sa.admin.module.business.techsolution.util.SmartBeanUtil;
import com.sprixin.sa.admin.module.business.techsolution.util.SmartPageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 技术方案Service实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TechSolutionServiceImpl implements TechSolutionService {

    @Autowired
    private TechSolutionDao techSolutionDao;

    @Autowired
    private TechSolutionFileDao techSolutionFileDao;

    @Autowired
    private QwenApiService qwenApiService;

    @Autowired
    private FileService fileService;

    @Autowired
    private EmployeeService employeeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TechSolutionVO generateTechSolution(TechSolutionGenerateDTO generateDTO) {
        // 获取当前登录用户
        EmployeeVO currentEmployee = employeeService.getCurrentLoginEmployee();

        // 创建技术方案记录
        TechSolutionEntity techSolutionEntity = new TechSolutionEntity();
        techSolutionEntity.setTitle(StringUtils.isBlank(generateDTO.getTitle()) ? "技术方案" : generateDTO.getTitle());
        techSolutionEntity.setPrompt(generateDTO.getPrompt());
        // 加密存储API Key（实际项目中应该使用加密算法）
        techSolutionEntity.setApiKey(generateDTO.getApiKey());
        techSolutionEntity.setCreatorId(currentEmployee.getEmployeeId());
        techSolutionEntity.setCreatorName(currentEmployee.getEmployeeName());
        techSolutionEntity.setStatus("PROCESSING"); // 初始状态设为处理中
        techSolutionEntity.setCreateTime(new Date());
        techSolutionEntity.setUpdateTime(new Date());
        
        // 保存技术方案
        techSolutionDao.insert(techSolutionEntity);
        
        // 处理文件关联
        if (StringUtils.isNotBlank(generateDTO.getFileIds())) {
            String[] fileIdArr = generateDTO.getFileIds().split(",");
            for (String fileIdStr : fileIdArr) {
                try {
                    Long fileId = Long.parseLong(fileIdStr);
                    FileEntity fileEntity = fileService.getById(fileId);
                    if (fileEntity != null) {
                        TechSolutionFileEntity fileRelation = new TechSolutionFileEntity();
                        fileRelation.setSolutionId(techSolutionEntity.getId());
                        fileRelation.setFileId(fileId);
                        fileRelation.setFileName(fileEntity.getFileName());
                        fileRelation.setFileType(fileEntity.getFileType());
                        fileRelation.setFileSize(fileEntity.getFileSize());
                        fileRelation.setFileUrl(fileEntity.getFileUrl());
                        fileRelation.setCreateTime(new Date());
                        techSolutionFileDao.insert(fileRelation);
                    }
                } catch (NumberFormatException e) {
                    log.error("Invalid file ID: {}", fileIdStr, e);
                }
            }
        }
        
        try {
            // 获取关联的文件内容
            String[] fileContents = getFileContents(techSolutionEntity.getId());
            
            // 同步调用Qwen API生成方案
            String content = qwenApiService.generateSolution(
                generateDTO.getPrompt(), 
                generateDTO.getApiKey(), 
                fileContents
            );
            
            // 更新方案内容和状态
            techSolutionEntity.setContent(content);
            techSolutionEntity.setStatus("COMPLETED");
            techSolutionEntity.setUpdateTime(new Date());
            techSolutionDao.updateById(techSolutionEntity);
            
        } catch (Exception e) {
            log.error("生成技术方案失败，ID: {}", techSolutionEntity.getId(), e);
            // 更新状态为失败
            techSolutionEntity.setStatus("FAILED");
            techSolutionEntity.setUpdateTime(new Date());
            techSolutionDao.updateById(techSolutionEntity);
        }
        
        // 返回包含最新状态和内容的VO
        TechSolutionEntity updatedEntity = techSolutionDao.selectById(techSolutionEntity.getId());
        return SmartBeanUtil.copy(updatedEntity, TechSolutionVO.class);
    }

    /**
     * 获取技术方案关联的文件内容
     */
    private String[] getFileContents(Long solutionId) {
        List<TechSolutionFileEntity> fileEntities = techSolutionFileDao.selectList(
            new LambdaQueryWrapper<TechSolutionFileEntity>()
                .eq(TechSolutionFileEntity::getSolutionId, solutionId)
        );
        
        if (fileEntities == null || fileEntities.isEmpty()) {
            return new String[0];
        }
        
        List<String> contents = new ArrayList<>();
        for (TechSolutionFileEntity fileEntity : fileEntities) {
            try {
                // 这里应该实现获取文件内容的逻辑，可能需要调用文件服务
                // 为了简化，这里假设我们可以通过文件URL获取内容
                String content = fileService.getFileContent(fileEntity.getFileId());
                if (StringUtils.isNotBlank(content)) {
                    contents.add(content);
                }
            } catch (Exception e) {
                log.error("获取文件内容失败，文件ID: {}", fileEntity.getFileId(), e);
            }
        }
        
        return contents.toArray(new String[0]);
    }

    @Override
    public PageResult<TechSolutionEntity> queryPage(PageParam pageParam) {
        Page<TechSolutionEntity> page = SmartPageUtil.convert2QueryPage(pageParam);
        
        LambdaQueryWrapper<TechSolutionEntity> queryWrapper = new LambdaQueryWrapper<>();
        
        // 关键字搜索
        if (StringUtils.isNotBlank(pageParam.getSearchParam())) {
            String keyword = pageParam.getSearchParam();
            queryWrapper.like(TechSolutionEntity::getTitle, keyword)
                .or()
                .like(TechSolutionEntity::getPrompt, keyword);
        }
        
        // 状态筛选
        if (StringUtils.isNotBlank(pageParam.getStatus())) {
            queryWrapper.eq(TechSolutionEntity::getStatus, pageParam.getStatus());
        }
        
        // 时间范围筛选
        if (pageParam.getStartTime() != null) {
            queryWrapper.ge(TechSolutionEntity::getCreateTime, pageParam.getStartTime());
        }
        
        if (pageParam.getEndTime() != null) {
            queryWrapper.le(TechSolutionEntity::getCreateTime, pageParam.getEndTime());
        }
        
        // 排序
        if (StringUtils.isNotBlank(pageParam.getSortField())) {
            boolean isAsc = "asc".equalsIgnoreCase(pageParam.getSortOrder());
            
            switch (pageParam.getSortField()) {
                case "title":
                    queryWrapper.orderBy(true, isAsc, TechSolutionEntity::getTitle);
                    break;
                case "createTime":
                    queryWrapper.orderBy(true, isAsc, TechSolutionEntity::getCreateTime);
                    break;
                case "updateTime":
                    queryWrapper.orderBy(true, isAsc, TechSolutionEntity::getUpdateTime);
                    break;
                default:
                    queryWrapper.orderByDesc(TechSolutionEntity::getCreateTime);
            }
        } else {
            // 默认按创建时间倒序排序
            queryWrapper.orderByDesc(TechSolutionEntity::getCreateTime);
        }
        
        Page<TechSolutionEntity> resultPage = techSolutionDao.selectPage(page, queryWrapper);
        return SmartPageUtil.convert2PageResult(resultPage);
    }

    @Override
    public TechSolutionEntity getById(Long id) {
        return techSolutionDao.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        // 删除关联的文件记录
        techSolutionFileDao.delete(
            new LambdaQueryWrapper<TechSolutionFileEntity>()
                .eq(TechSolutionFileEntity::getSolutionId, id)
        );
        
        // 删除技术方案
        techSolutionDao.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Long> idList) {
        // 删除关联的文件记录
        techSolutionFileDao.delete(
            new LambdaQueryWrapper<TechSolutionFileEntity>()
                .in(TechSolutionFileEntity::getSolutionId, idList)
        );
        
        // 批量删除技术方案
        techSolutionDao.deleteBatchIds(idList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TechSolutionVO updateContent(TechSolutionUpdateContentDTO updateDTO) {
        if (updateDTO == null || updateDTO.getId() == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        // 获取技术方案实体
        TechSolutionEntity entity = techSolutionDao.selectById(updateDTO.getId());
        if (entity == null) {
            throw new IllegalArgumentException("技术方案不存在");
        }
        
        // 更新内容
        entity.setContent(updateDTO.getContent());
        entity.setUpdateTime(new Date());
        
        // 保存更新
        techSolutionDao.updateById(entity);
        
        // 返回更新后的结果
        TechSolutionEntity updatedEntity = techSolutionDao.selectById(entity.getId());
        return SmartBeanUtil.copy(updatedEntity, TechSolutionVO.class);
    }
} 