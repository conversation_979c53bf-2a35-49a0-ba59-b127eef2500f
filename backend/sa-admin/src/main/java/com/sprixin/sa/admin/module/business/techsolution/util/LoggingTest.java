package com.sprixin.sa.admin.module.business.techsolution.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 测试日志配置
 */
@Component
public class LoggingTest implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(LoggingTest.class);
    
    @Override
    public void run(String... args) throws Exception {
        // logger.debug("这是一条DEBUG日志");
        // logger.info("这是一条INFO日志");
        // logger.warn("这是一条WARN日志");
        // logger.error("这是一条ERROR日志");
    }
} 