package com.sprixin.sa.admin.module.business.techsolution.util;

import org.springframework.beans.BeanUtils;

/**
 * Bean工具类（临时实现）
 *
 * <AUTHOR>
 */
public class SmartBeanUtil {
    
    /**
     * 对象属性拷贝
     *
     * @param source 源对象
     * @param targetClass 目标类
     * @param <T> 目标类型
     * @return 目标对象
     */
    public static <T> T copy(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            throw new RuntimeException("Bean copy error", e);
        }
    }
} 