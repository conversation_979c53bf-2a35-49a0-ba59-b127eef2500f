package com.sprixin.sa.admin.module.business.techsolution.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.admin.module.business.techsolution.domain.PageParam;
import com.sprixin.sa.admin.module.business.techsolution.domain.PageResult;

import java.util.List;

/**
 * 分页工具类（临时实现）
 *
 * <AUTHOR>
 */
public class SmartPageUtil {
    
    /**
     * 将PageParam转换为MyBatis-Plus的Page对象
     *
     * @param pageParam 分页参数
     * @param <T> 实体类型
     * @return Page对象
     */
    public static <T> Page<T> convert2QueryPage(PageParam pageParam) {
        if (pageParam == null) {
            return new Page<>(1, 10);
        }
        return new Page<>(pageParam.getPageNum(), pageParam.getPageSize());
    }
    
    /**
     * 将MyBatis-Plus的Page对象转换为PageResult
     *
     * @param page MyBatis-Plus的Page对象
     * @param <T> 实体类型
     * @return PageResult对象
     */
    public static <T> PageResult<T> convert2PageResult(Page<T> page) {
        PageResult<T> result = new PageResult<>();
        result.setTotal(page.getTotal());
        result.setPageNum((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        result.setRecords(page.getRecords());
        result.setPages((int) page.getPages());
        return result;
    }
} 