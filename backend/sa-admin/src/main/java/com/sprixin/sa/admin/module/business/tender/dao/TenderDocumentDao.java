package com.sprixin.sa.admin.module.business.tender.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.admin.module.business.tender.domain.entity.TenderDocumentEntity;
import com.sprixin.sa.admin.module.business.tender.domain.dto.TenderDocumentQueryDTO;
import com.sprixin.sa.admin.module.business.tender.domain.vo.TenderDocumentListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 标书文档数据访问接口
 *
 * <AUTHOR>
 */
@Mapper
public interface TenderDocumentDao extends BaseMapper<TenderDocumentEntity> {

    /**
     * 分页查询标书列表
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 标书列表
     */
    List<TenderDocumentListVO> queryPage(Page<TenderDocumentListVO> page, @Param("queryDTO") TenderDocumentQueryDTO queryDTO);

    /**
     * 根据ID查询标书详情
     *
     * @param id 标书ID
     * @return 标书详情
     */
    TenderDocumentEntity selectById(@Param("id") Long id);

    /**
     * 批量删除标书
     *
     * @param idList ID列表
     * @return 删除数量
     */
    int batchDelete(@Param("idList") List<Long> idList);
}
