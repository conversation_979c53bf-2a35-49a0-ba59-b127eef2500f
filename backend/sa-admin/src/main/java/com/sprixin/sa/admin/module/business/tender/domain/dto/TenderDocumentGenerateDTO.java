package com.sprixin.sa.admin.module.business.tender.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import jakarta.validation.constraints.NotBlank;

/**
 * 标书生成DTO
 *
 * <AUTHOR>
 */
@Data
public class TenderDocumentGenerateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标书标题
     */
    private String title;

    /**
     * 标书类型：TECHNICAL-技术标, COMMERCIAL-商务标, COMPREHENSIVE-综合标
     */
    @NotBlank(message = "标书类型不能为空")
    private String tenderType;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 招标编号
     */
    private String tenderNo;

    /**
     * 生成提示词
     */
    @NotBlank(message = "提示词不能为空")
    private String prompt;

    /**
     * API Key
     */
    private String apiKey;

    /**
     * 投标截止时间
     */
    private Date deadline;

    /**
     * 预估金额
     */
    private BigDecimal estimatedAmount;

    /**
     * 文件ID列表，逗号分隔
     */
    private String fileIds;
}
