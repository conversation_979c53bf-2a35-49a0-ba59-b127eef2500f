package com.sprixin.sa.admin.module.business.tender.domain.dto;

import com.sprixin.sa.base.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 标书查询DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TenderDocumentQueryDTO extends PageParam {

    /**
     * 标书标题
     */
    private String title;

    /**
     * 标书类型
     */
    private String tenderType;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 招标编号
     */
    private String tenderNo;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建人姓名
     */
    private String creatorName;
}
