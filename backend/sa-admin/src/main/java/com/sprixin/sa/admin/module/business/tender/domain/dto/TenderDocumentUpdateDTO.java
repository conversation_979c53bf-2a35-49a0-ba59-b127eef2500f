package com.sprixin.sa.admin.module.business.tender.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import jakarta.validation.constraints.NotNull;

/**
 * 标书更新DTO
 *
 * <AUTHOR>
 */
@Data
public class TenderDocumentUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @NotNull(message = "标书ID不能为空")
    private Long id;

    /**
     * 标书标题
     */
    private String title;

    /**
     * 标书类型
     */
    private String tenderType;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 招标编号
     */
    private String tenderNo;

    /**
     * 标书内容
     */
    private String content;

    /**
     * 状态
     */
    private String status;

    /**
     * 投标截止时间
     */
    private Date deadline;

    /**
     * 预估金额
     */
    private BigDecimal estimatedAmount;
}
