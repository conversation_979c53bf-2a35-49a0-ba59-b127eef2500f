package com.sprixin.sa.admin.module.business.tender.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 标书文档实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("tender_document")
public class TenderDocumentEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 标书标题
     */
    private String title;

    /**
     * 标书类型：TECHNICAL-技术标, COMMERCIAL-商务标, COMPREHENSIVE-综合标
     */
    private String tenderType;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 招标编号
     */
    private String tenderNo;

    /**
     * 标书内容
     */
    private String content;

    /**
     * 生成提示词
     */
    private String prompt;

    /**
     * API Key (加密存储)
     */
    private String apiKey;

    /**
     * 状态：PROCESSING-处理中, COMPLETED-已完成, FAILED-失败, SUBMITTED-已提交
     */
    private String status;

    /**
     * 投标截止时间
     */
    private Date deadline;

    /**
     * 预估金额
     */
    private BigDecimal estimatedAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;
}
