package com.sprixin.sa.admin.module.business.tender.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 标书列表VO
 *
 * <AUTHOR>
 */
@Data
public class TenderDocumentListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 标书标题
     */
    private String title;

    /**
     * 标书类型
     */
    private String tenderType;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 招标编号
     */
    private String tenderNo;

    /**
     * 状态
     */
    private String status;

    /**
     * 投标截止时间
     */
    private Date deadline;

    /**
     * 预估金额
     */
    private BigDecimal estimatedAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人姓名
     */
    private String creatorName;
}
