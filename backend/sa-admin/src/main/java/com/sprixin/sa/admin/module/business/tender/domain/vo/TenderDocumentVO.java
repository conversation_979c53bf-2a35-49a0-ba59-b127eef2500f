package com.sprixin.sa.admin.module.business.tender.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 标书文档VO
 *
 * <AUTHOR>
 */
@Data
public class TenderDocumentVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 标书标题
     */
    private String title;

    /**
     * 标书类型
     */
    private String tenderType;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 招标编号
     */
    private String tenderNo;

    /**
     * 标书内容
     */
    private String content;

    /**
     * 生成提示词
     */
    private String prompt;

    /**
     * 状态
     */
    private String status;

    /**
     * 投标截止时间
     */
    private Date deadline;

    /**
     * 预估金额
     */
    private BigDecimal estimatedAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;
}
