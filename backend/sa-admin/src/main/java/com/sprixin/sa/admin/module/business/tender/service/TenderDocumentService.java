package com.sprixin.sa.admin.module.business.tender.service;

import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.admin.module.business.tender.domain.dto.TenderDocumentGenerateDTO;
import com.sprixin.sa.admin.module.business.tender.domain.dto.TenderDocumentQueryDTO;
import com.sprixin.sa.admin.module.business.tender.domain.dto.TenderDocumentUpdateDTO;
import com.sprixin.sa.admin.module.business.tender.domain.entity.TenderDocumentEntity;
import com.sprixin.sa.admin.module.business.tender.domain.vo.TenderDocumentVO;
import com.sprixin.sa.admin.module.business.tender.domain.vo.TenderDocumentListVO;

import java.util.List;

/**
 * 标书文档Service接口
 *
 * <AUTHOR>
 */
public interface TenderDocumentService {

    /**
     * 生成标书
     *
     * @param generateDTO 生成参数
     * @return 标书VO
     */
    TenderDocumentVO generateTender(TenderDocumentGenerateDTO generateDTO);

    /**
     * 分页查询
     *
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    PageResult<TenderDocumentListVO> queryPage(TenderDocumentQueryDTO queryDTO);

    /**
     * 根据ID获取
     *
     * @param id 标书ID
     * @return 标书VO
     */
    TenderDocumentVO getById(Long id);

    /**
     * 更新标书
     *
     * @param updateDTO 更新参数
     * @return 标书VO
     */
    TenderDocumentVO updateTender(TenderDocumentUpdateDTO updateDTO);

    /**
     * 根据ID删除
     *
     * @param id 标书ID
     */
    void deleteById(Long id);

    /**
     * 批量删除
     *
     * @param idList ID列表
     */
    void batchDelete(List<Long> idList);

    /**
     * 获取标书生成状态
     *
     * @param id 标书ID
     * @return 状态信息
     */
    String getTenderStatus(Long id);

    /**
     * 更新标书内容
     *
     * @param id 标书ID
     * @param content 内容
     * @return 标书VO
     */
    TenderDocumentVO updateContent(Long id, String content);
}
