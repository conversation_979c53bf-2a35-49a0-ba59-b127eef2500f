package com.sprixin.sa.admin.module.business.tender.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sprixin.sa.admin.module.business.tender.service.TenderApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 标书AI生成服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TenderApiServiceImpl implements TenderApiService {
    
    @Value("${qwen.api.url}")
    private String apiUrl;

    @Value("${qwen.api.key}")
    private String defaultApiKey;

    @Value("${qwen.api.model}")
    private String model;
    
    @Override
    public String generateTender(String prompt, String apiKey, String[] fileContents, String tenderType) {
        try {
            // 使用传入的apiKey，如果为空则使用默认的
            String actualApiKey = StringUtils.isNotBlank(apiKey) ? apiKey : defaultApiKey;
            
            // 日志输出，帮助调试
            log.info("使用API密钥: {}, API URL: {}, 模型: {}, 标书类型: {}", 
                     actualApiKey.length() > 5 ? actualApiKey.substring(0, 5) + "..." : "无效密钥", 
                     apiUrl, model, tenderType);
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            
            // 构建消息列表
            List<Map<String, Object>> messages = new ArrayList<>();
            
            // 系统消息 - 根据标书类型定制
            Map<String, Object> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", buildSystemPrompt(tenderType));
            messages.add(systemMessage);
            
            // 用户消息
            StringBuilder userContent = new StringBuilder();
            userContent.append("请根据以下要求生成标书：\n\n");
            userContent.append(prompt);
            
            // 如果有文件内容，添加到提示词中
            if (fileContents != null && fileContents.length > 0) {
                userContent.append("\n\n参考文件内容：\n");
                for (int i = 0; i < fileContents.length; i++) {
                    if (StringUtils.isNotBlank(fileContents[i])) {
                        userContent.append("文件").append(i + 1).append("：\n");
                        userContent.append(fileContents[i]).append("\n\n");
                    }
                }
            }
            
            Map<String, Object> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", userContent.toString());
            messages.add(userMessage);
            
            requestBody.put("messages", messages);
            requestBody.put("max_tokens", 4000);
            requestBody.put("temperature", 0.7);
            
            // 准备HTTP头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + actualApiKey);
            
            // 发送请求
            RestTemplate restTemplate = new RestTemplate();
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            log.info("发送请求到Qwen API: {}", apiUrl);
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                apiUrl,
                HttpMethod.POST,
                requestEntity,
                String.class
            );
            
            // 处理响应
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                String responseBody = responseEntity.getBody();
                log.info("Qwen API响应成功");
                
                // 解析响应
                JSONObject jsonResponse = JSON.parseObject(responseBody);
                JSONObject choices = jsonResponse.getJSONArray("choices").getJSONObject(0);
                JSONObject message = choices.getJSONObject("message");
                String content = message.getString("content");
                
                return content;
            } else {
                log.error("Qwen API调用失败，状态码: {}", responseEntity.getStatusCodeValue());
                return "调用Qwen API失败，状态码: " + responseEntity.getStatusCodeValue();
            }
            
        } catch (Exception e) {
            log.error("调用Qwen API异常", e);
            return "生成标书失败: " + e.getMessage();
        }
    }
    
    /**
     * 根据标书类型构建系统提示词
     */
    private String buildSystemPrompt(String tenderType) {
        StringBuilder systemPrompt = new StringBuilder();
        systemPrompt.append("你是一个专业的标书编写专家，具有丰富的投标经验。");
        
        switch (tenderType) {
            case "TECHNICAL":
                systemPrompt.append("请专注于技术标书的编写，重点突出技术方案、实施方案、技术优势等内容。");
                break;
            case "COMMERCIAL":
                systemPrompt.append("请专注于商务标书的编写，重点突出报价方案、商务条款、服务承诺等内容。");
                break;
            case "COMPREHENSIVE":
                systemPrompt.append("请编写综合标书，需要包含技术方案和商务方案两个部分。");
                break;
            default:
                systemPrompt.append("请根据招标要求编写专业的标书文档。");
        }
        
        systemPrompt.append("标书内容应该结构清晰、逻辑严密、符合招标要求，并突出投标方的优势和竞争力。");
        
        return systemPrompt.toString();
    }
}
