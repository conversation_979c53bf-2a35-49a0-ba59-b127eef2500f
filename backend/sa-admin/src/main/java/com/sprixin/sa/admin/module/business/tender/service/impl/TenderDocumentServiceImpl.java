package com.sprixin.sa.admin.module.business.tender.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.admin.module.business.tender.dao.TenderDocumentDao;
import com.sprixin.sa.admin.module.business.tender.domain.dto.TenderDocumentGenerateDTO;
import com.sprixin.sa.admin.module.business.tender.domain.dto.TenderDocumentQueryDTO;
import com.sprixin.sa.admin.module.business.tender.domain.dto.TenderDocumentUpdateDTO;
import com.sprixin.sa.admin.module.business.tender.domain.entity.TenderDocumentEntity;
import com.sprixin.sa.admin.module.business.tender.domain.vo.TenderDocumentVO;
import com.sprixin.sa.admin.module.business.tender.domain.vo.TenderDocumentListVO;
import com.sprixin.sa.admin.module.business.tender.service.TenderDocumentService;
import com.sprixin.sa.admin.module.business.tender.service.TenderApiService;
import com.sprixin.sa.admin.module.system.employee.domain.vo.EmployeeVO;
import com.sprixin.sa.admin.module.system.login.domain.RequestEmployee;
import com.sprixin.sa.admin.util.AdminRequestUtil;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.base.common.util.SmartBeanUtil;
import com.sprixin.sa.base.common.util.SmartPageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 标书文档Service实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TenderDocumentServiceImpl implements TenderDocumentService {

    @Autowired
    private TenderDocumentDao tenderDocumentDao;

    @Autowired
    private TenderApiService tenderApiService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TenderDocumentVO generateTender(TenderDocumentGenerateDTO generateDTO) {
        // 获取当前登录用户
        RequestEmployee requestEmployee = AdminRequestUtil.getRequestUser();
        if (requestEmployee == null) {
            throw new RuntimeException("用户未登录");
        }
        
        // 创建标书记录
        TenderDocumentEntity tenderEntity = new TenderDocumentEntity();
        tenderEntity.setTitle(StringUtils.isBlank(generateDTO.getTitle()) ? "标书文档" : generateDTO.getTitle());
        tenderEntity.setTenderType(generateDTO.getTenderType());
        tenderEntity.setProjectName(generateDTO.getProjectName());
        tenderEntity.setTenderNo(generateDTO.getTenderNo());
        tenderEntity.setPrompt(generateDTO.getPrompt());
        tenderEntity.setApiKey(generateDTO.getApiKey());
        tenderEntity.setDeadline(generateDTO.getDeadline());
        tenderEntity.setEstimatedAmount(generateDTO.getEstimatedAmount());
        tenderEntity.setCreatorId(requestEmployee.getEmployeeId());
        tenderEntity.setCreatorName(requestEmployee.getActualName());
        tenderEntity.setStatus("PROCESSING"); // 初始状态设为处理中
        tenderEntity.setCreateTime(new Date());
        tenderEntity.setUpdateTime(new Date());
        
        // 保存标书记录
        tenderDocumentDao.insert(tenderEntity);
        
        // TODO: 处理文件关联（如果有fileIds）
        // 这里可以添加文件关联的逻辑
        
        try {
            // 获取关联的文件内容（暂时为空数组）
            String[] fileContents = new String[0];
            // TODO: 根据fileIds获取文件内容
            
            // 调用AI API生成标书
            String content = tenderApiService.generateTender(
                generateDTO.getPrompt(), 
                generateDTO.getApiKey(), 
                fileContents,
                generateDTO.getTenderType()
            );
            
            // 更新标书内容和状态
            tenderEntity.setContent(content);
            tenderEntity.setStatus("COMPLETED");
            tenderEntity.setUpdateTime(new Date());
            tenderDocumentDao.updateById(tenderEntity);
            
        } catch (Exception e) {
            log.error("生成标书失败，ID: {}", tenderEntity.getId(), e);
            // 更新状态为失败
            tenderEntity.setStatus("FAILED");
            tenderEntity.setUpdateTime(new Date());
            tenderDocumentDao.updateById(tenderEntity);
        }
        
        // 转换为VO返回
        return SmartBeanUtil.copy(tenderEntity, TenderDocumentVO.class);
    }

    @Override
    public PageResult<TenderDocumentListVO> queryPage(TenderDocumentQueryDTO queryDTO) {
        Page<TenderDocumentListVO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<TenderDocumentListVO> list = tenderDocumentDao.queryPage(page, queryDTO);
        return SmartPageUtil.convert2PageResult(page, list);
    }

    @Override
    public TenderDocumentVO getById(Long id) {
        TenderDocumentEntity entity = tenderDocumentDao.selectById(id);
        if (entity == null) {
            return null;
        }
        return SmartBeanUtil.copy(entity, TenderDocumentVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TenderDocumentVO updateTender(TenderDocumentUpdateDTO updateDTO) {
        TenderDocumentEntity entity = tenderDocumentDao.selectById(updateDTO.getId());
        if (entity == null) {
            throw new RuntimeException("标书不存在");
        }
        
        // 更新字段
        if (StringUtils.isNotBlank(updateDTO.getTitle())) {
            entity.setTitle(updateDTO.getTitle());
        }
        if (StringUtils.isNotBlank(updateDTO.getTenderType())) {
            entity.setTenderType(updateDTO.getTenderType());
        }
        if (StringUtils.isNotBlank(updateDTO.getProjectName())) {
            entity.setProjectName(updateDTO.getProjectName());
        }
        if (StringUtils.isNotBlank(updateDTO.getTenderNo())) {
            entity.setTenderNo(updateDTO.getTenderNo());
        }
        if (StringUtils.isNotBlank(updateDTO.getContent())) {
            entity.setContent(updateDTO.getContent());
        }
        if (StringUtils.isNotBlank(updateDTO.getStatus())) {
            entity.setStatus(updateDTO.getStatus());
        }
        if (updateDTO.getDeadline() != null) {
            entity.setDeadline(updateDTO.getDeadline());
        }
        if (updateDTO.getEstimatedAmount() != null) {
            entity.setEstimatedAmount(updateDTO.getEstimatedAmount());
        }
        
        entity.setUpdateTime(new Date());
        tenderDocumentDao.updateById(entity);
        
        return SmartBeanUtil.copy(entity, TenderDocumentVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        tenderDocumentDao.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Long> idList) {
        if (idList != null && !idList.isEmpty()) {
            tenderDocumentDao.batchDelete(idList);
        }
    }

    @Override
    public String getTenderStatus(Long id) {
        TenderDocumentEntity entity = tenderDocumentDao.selectById(id);
        if (entity == null) {
            throw new RuntimeException("标书不存在");
        }
        return entity.getStatus();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TenderDocumentVO updateContent(Long id, String content) {
        TenderDocumentEntity entity = tenderDocumentDao.selectById(id);
        if (entity == null) {
            throw new RuntimeException("标书不存在");
        }

        entity.setContent(content);
        entity.setUpdateTime(new Date());
        tenderDocumentDao.updateById(entity);

        return SmartBeanUtil.copy(entity, TenderDocumentVO.class);
    }
}
