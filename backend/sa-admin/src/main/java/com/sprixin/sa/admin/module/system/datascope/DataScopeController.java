package com.sprixin.sa.admin.module.system.datascope;

import com.sprixin.sa.admin.constant.AdminSwaggerTagConst;
import com.sprixin.sa.admin.module.system.datascope.domain.DataScopeAndViewTypeVO;
import com.sprixin.sa.admin.module.system.datascope.service.DataScopeService;
import com.sprixin.sa.base.common.domain.ResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 查询支持的数据范围类型
 */
@RestController
@Tag(name = AdminSwaggerTagConst.System.SYSTEM_DATA_SCOPE)
public class DataScopeController {

    @Resource
    private DataScopeService dataScopeService;

    @Operation(summary = "获取当前系统所配置的所有数据范围")
    @GetMapping("/dataScope/list")
    public ResponseDTO<List<DataScopeAndViewTypeVO>> dataScopeList() {
        return dataScopeService.dataScopeList();
    }

}
