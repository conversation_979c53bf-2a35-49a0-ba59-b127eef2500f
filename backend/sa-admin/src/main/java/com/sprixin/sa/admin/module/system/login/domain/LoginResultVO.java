package com.sprixin.sa.admin.module.system.login.domain;

import com.sprixin.sa.admin.module.system.menu.domain.vo.MenuVO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 登录结果信息
 */
@Data
public class LoginResultVO extends RequestEmployee {

    @Schema(description = "token")
    private String token;

    @Schema(description = "菜单列表")
    private List<MenuVO> menuList;

    @Schema(description = "是否需要修改密码")
    private Boolean needUpdatePwdFlag;

    @Schema(description = "上次登录ip")
    private String lastLoginIp;

    @Schema(description = "上次登录ip地区")
    private String lastLoginIpRegion;

    @Schema(description = "上次登录user-agent")
    private String lastLoginUserAgent;

    @Schema(description = "上次登录时间")
    private LocalDateTime lastLoginTime;

}
