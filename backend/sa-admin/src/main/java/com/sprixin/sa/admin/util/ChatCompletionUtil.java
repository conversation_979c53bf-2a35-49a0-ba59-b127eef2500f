package com.sprixin.sa.admin.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.sprixin.sa.admin.util.qwen.ConstantsPrompt;
import com.sprixin.sa.admin.util.qwen.DocumentProcessor;

import java.util.List;
import java.util.ArrayList;

public class ChatCompletionUtil {
    public static void main(String[] args) {
        try {
            // 指定要读取的文件路径，优先使用命令行参数
            String  filePath = "D://test/W01-徐闻项目-光伏电站预测系统竞价协议.docx";
            boolean useStream = false; // 默认使用非流式API（根据测试结果更稳定）
            int chunkSize = 8000; // 默认分块大小（根据测试结果更合适）
            boolean mergeResults = true; // 默认合并结果
           

            // 读取文档内容
            System.out.println("开始处理文件: " + filePath);
            System.out.println("使用" + (useStream ? "流式" : "非流式") + "API");
            System.out.println("分块大小: " + chunkSize + " 字符");
            System.out.println(mergeResults ? "将合并分析结果" : "不合并分析结果");
            
            // 读取文档内容
            String documentContent = DocumentProcessor.extractTextFromFile(filePath);
            System.out.println("成功读取文档内容，长度: " + documentContent.length() + " 字符");
            
            // 分析要求
            String fenxiyaoqiuStr = "【分析要求】请仔细分析以下电力新能源系统技术协议文档，严格按照指定格式提取硬件和软件配置信息：" + 
                "【分析目标】" + 
                "提取电力新能源系统中所有硬件设备和软件系统的详细配置参数、数量及供应商信息。" + 
                "【输出格式要求】" + 
                "严格按照以下表格格式输出，不得更改表格结构。仅输出表格内容，不输出任何解释、分析过程或总结：" + 
    
                "## 硬件部分" + 
                "| 设备名称 | 配置 | 数量 | 备注 |" + 
                "|---------|------|------|------|" + 
                "|功率预测服务器| CPU配置,内存(GB),硬盘(TB),网口数量,电源配置 等|2|国能日新科技股份有限公司|" + 
                "|气象服务器| CPU配置,内存(GB),硬盘(TB),网口数量,电源配置 等|1|具体供应商名称|" + 
                "|数据采集服务器| CPU配置,内存(GB),硬盘(TB),网口数量,串口数量 等|1|具体供应商名称|" + 
                "|工程师站/工作站| CPU配置,内存(GB),硬盘(TB),网口数量,显示器尺寸 等|1|具体供应商名称|" + 
                "|防火墙| 接口数量,吞吐量,并发连接数,电源配置 等|2|具体供应商名称|" + 
                "|交换机| 端口数量,速率,电源配置 等|1|具体供应商名称|" + 
                "|隔离装置| 网口配置,传输方向,安全等级 等|1|具体供应商名称|" + 
                "|KVM及显示器| 显示器尺寸,VGA口数量,键鼠配套 等|1|具体供应商名称|" + 
                "|机柜| 尺寸规格,颜色,电源路数 等|1|具体供应商名称|" + 
                "|测风塔| 高度,传感器类型,测量参数 等|1|具体供应商名称|" + 
                "|气象站设备| 高度,传感器类型,测量参数 等|1|具体供应商名称|" + 
    
                "## 软件部分" + 
                "| 名称 | 配置 | 数量 | 备注 |" + 
                "|---------|------|------|------|" + 
                "|功率预测软件| 预测时长范围,预测精度要求,功能模块 等|1|国能日新科技股份有限公司|" + 
                "|操作系统| 系统类型,版本要求,安全等级 等|多套|具体供应商名称|" + 
                "|数据库软件| 数据库类型,版本,许可证数量 等|1|具体供应商名称|" + 
                "|短期预测模型| 预测时长,建模服务,算法类型 等|1|具体供应商名称|" + 
                "|超短期预测模型| 预测时长,建模服务,算法类型 等|1|具体供应商名称|" + 
                "|数值天气预报服务| 服务年限,数据源,更新频率 等|1|具体供应商名称|" + 
                "|技术维护服务| 服务年限,服务内容,维护范围 等|1|具体供应商名称|" + 
                "|网安探针软件| 兼容系统,功能要求,版本信息 等|多套|具体供应商名称|" + 
    
                "【关键提取要求】" + 
                "1. **硬件配置细节**：" + 
                "   - CPU：核心数、主频" + 
                "   - 内存：容量(GB)、类型(如ECC DDR3)" + 
                "   - 硬盘：容量(TB)、类型(如SAS、热插拔)" + 
                "   - 网口：数量、速率(如千兆)" + 
                "   - 电源：单/双电源、热插拔特性" + 
    
                "2. **软件功能要求**：" + 
                "   - 预测时长：超短期(0-4h)、短期(0-72h)等" + 
                "   - 系统要求：国产化、安全等级" + 
                "   - 服务年限：维护期限、技术支持" + 
    
                "3. **数量统计**：" + 
                "   - 准确提取每种设备的数量" + 
                "   - 区分单台、多台、成套设备" + 
    
                "4. **供应商信息**：" + 
                "   - 优先提取明确的供应商名称" + 
                "   - 如未指定供应商，标注\"未指定\"或相关要求" + 
    
                "5. **特殊要求标注**：" + 
                "   - 项目装机容量(如100MW、500MW)" + 
                "   - 环境要求(如抗覆冰、防护等级)" + 
                "   - 电网认证要求" + 
                "   - 颜色、尺寸等定制要求" + 
    
                "【注意事项】" + 
                "- 保持原文档中的技术参数准确性" + 
                "- 对于\"不少于\"、\"不低于\"等表述原样记录" + 
                "- 区分风电和光伏项目的配置差异" + 
                "- 将相同功能但不同规格的设备合并列出" + 
                "- 如某项信息不明确，在备注中说明" + 
                "- 严禁在表格前后输出任何介绍、分析过程、总结或其他文字内容" + 
                "【重要说明】仅输出两个表格（硬件部分和软件部分），不要输出任何表格外的内容，包括开头的分析说明、中间的思考过程和结尾的总结。";
                fenxiyaoqiuStr = ConstantsPrompt.DOCUMNT_ANALYSIS_PROMPT;
            // 分析文档内容
            String finalResult = doAnalyzeDocument(documentContent, useStream, chunkSize, fenxiyaoqiuStr, mergeResults);
            
            // 输出结果
            System.out.println("文档分析结果: " + finalResult);
            
            // 保存结果到文件
            String outputFilePath = filePath + ".analysis.txt";
            try {
                java.nio.file.Files.write(
                    java.nio.file.Paths.get(outputFilePath),
                    finalResult.getBytes(StandardCharsets.UTF_8)
                );
                System.out.println("分析结果已保存到文件: " + outputFilePath);
            } catch (IOException e) {
                System.err.println("保存结果到文件时出错: " + e.getMessage());
            }
        } catch (IOException e) {
            System.err.println("处理文档时出错: " + e.getMessage());
            e.printStackTrace();
        }
    } 
    /**
     * 流式聊天完成
     * @param messages 消息内容JSON字符串
     * @return 只返回content标签的内容
     */
    public static String postChatCompletion(String messages) {
        return postChatCompletion(messages, true);
    }

    /**
     * 聊天完成
     * @param messages 消息内容JSON字符串
     * @param useStream 是否使用流式API
     * @return 只返回content标签的内容
     */
    public static String postChatCompletion(String messages, boolean useStream) {
         String url = "http://10.10.10.245:8000/v1/chat/completions";
         String apiKey = "sk-1234567890";
         String model = "/data/models/Qwen/Qwen3-30B-A3B";

        // String url = "https://api.deepseek.com/chat/completions";
        // String apiKey = "sk-2b0a17cc9c93451d98ea35e9bae9b218";
        // String model = "deepseek-chat";
        
        System.out.println("开始调用AI API进行文档分析...");
        if (useStream) {
            System.out.println("使用流式API...");
            return streamingChatCompletion(url, apiKey, model, messages);
        } else {
            System.out.println("使用非流式API...");
            return chatCompletion(url, apiKey, model, messages);
        }
    }

    /**
     * 流式聊天完成
     * @param url API请求地址
     * @param apiKey API密钥
     * @param model 模型名称
     * @param messages 消息内容JSON字符串
     * @return 只返回content标签的内容
     */
    public static String streamingChatCompletion(String url, String apiKey, String model, String messages) {
        HttpURLConnection connection = null;
        StringBuilder resultBuilder = new StringBuilder();
        
        try {
            // 创建URL对象
            URL urlObj = new URL(url);
            
            // 打开HTTP连接
            connection = (HttpURLConnection) urlObj.openConnection(); 
            
            // 设置请求方法和请求头
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Authorization", "Bearer " + apiKey);
            connection.setDoOutput(true);
            connection.setConnectTimeout(30000); // 30秒连接超时
            connection.setReadTimeout(60000);    // 60秒读取超时
            
            // 构建请求体JSON - 使用简化格式，根据测试结果确认有效
            String requestBody = "{"
                + "\"model\": \"" + model + "\","
                + "\"messages\": " + messages + ","
                + "\"stream\": true,"
                + "\"max_tokens\": 2000"
                + "}";
            
            System.out.println("请求体大小: " + requestBody.getBytes(StandardCharsets.UTF_8).length + " 字节");
            
            // 发送请求体
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            
            // 获取响应状态码
            int responseCode = connection.getResponseCode();
            System.out.println("HTTP响应状态: " + responseCode);
            
            // 读取流式响应
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    String line;
                    
                    // 循环读取每一行数据
                    while ((line = br.readLine()) != null) {
                        if (!line.isEmpty() && line.startsWith("data: ")) {
                            // 提取数据部分（去掉"data: "前缀）
                            String data = line.substring(6);
                            
                            // 检查是否是流结束标记
                            if (!"[DONE]".equals(data)) {
                                try {
                                    // 尝试解析JSON
                                    JSONObject jsonObject = new JSONObject(data);
                                    if (jsonObject.has("choices")) {
                                        JSONArray choices = jsonObject.getJSONArray("choices");
                                        if (choices.length() > 0) {
                                            JSONObject choice = choices.getJSONObject(0);
                                            if (choice.has("delta") && choice.getJSONObject("delta").has("content")) {
                                                // 从delta中提取content并添加到结果
                                                String content = choice.getJSONObject("delta").getString("content");
                                                resultBuilder.append(content);
                                            }
                                        }
                                    }
                                } catch (JSONException e) {
                                    System.err.println("JSON解析错误: " + e.getMessage());
                                }
                            } else {
                                System.out.println("流响应结束");
                            }
                        }
                    }
                }
            } else {
                // 读取错误响应
                try (BufferedReader errorReader = new BufferedReader(
                        new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
                    String errorLine;
                    StringBuilder errorResponse = new StringBuilder();
                    while ((errorLine = errorReader.readLine()) != null) {
                        errorResponse.append(errorLine);
                    }
                    System.err.println("服务器错误响应: " + errorResponse.toString());
                    return "error: " + responseCode + " - " + errorResponse.toString();
                }
            }
        } catch (IOException e) {
            System.err.println("发生IO异常: " + e.getMessage());
            e.printStackTrace();
            return "error: " + e.getMessage();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
        
        return resultBuilder.toString();
    }
    
    /**
     * 非流式聊天完成
     * @param urlString API请求地址
     * @param apiKey API密钥
     * @param model 模型名称
     * @param messages 消息内容JSON字符串
     * @return 只返回content标签的内容
     */
    public static String chatCompletion(String urlString, String apiKey, String model, String messages) {
        try {
            URL url = new URL(urlString);
            System.out.println("请求URL: " + url);
            
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            // 设置请求头
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Authorization", "Bearer " + apiKey);
            connection.setDoOutput(true);
            connection.setConnectTimeout(30000); // 30秒连接超时
            connection.setReadTimeout(60000);    // 60秒读取超时
            
            // 请求体 - 使用简化格式，根据测试结果确认有效
            String requestBody = "{"
                + "\"model\": \"" + model + "\","
                + "\"messages\": " + messages + ","
                + "\"stream\": false,"
                + "\"max_tokens\": 2000"
                + "}";
                
            System.out.println("请求体大小: " + requestBody.getBytes(StandardCharsets.UTF_8).length + " 字节");
            
            // 发送请求
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            
            // 获取响应状态码
            int statusCode = connection.getResponseCode();
            System.out.println("HTTP状态码: " + statusCode);
            
            // 读取响应
            if (statusCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    StringBuilder responseStr = new StringBuilder();
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        responseStr.append(responseLine.trim());
                    }
                    
                    try {
                        // 解析JSON响应，提取content内容
                        JSONObject jsonResponse = new JSONObject(responseStr.toString());
                        if (jsonResponse.has("choices")) {
                            JSONArray choices = jsonResponse.getJSONArray("choices");
                            if (choices.length() > 0) {
                                JSONObject choice = choices.getJSONObject(0);
                                if (choice.has("message") && choice.getJSONObject("message").has("content")) {
                                    // 只返回content内容
                                    String content = choice.getJSONObject("message").getString("content");
                                    if (content != null) {
                                        return content;
                                    } else {
                                        // 尝试从reasoning_content获取内容
                                        if (choice.getJSONObject("message").has("reasoning_content")) {
                                            String reasoning = choice.getJSONObject("message").getString("reasoning_content");
                                            if (reasoning != null && !reasoning.isEmpty()) {
                                                return reasoning;
                                            }
                                        }
                                        return "API返回的content为null，请检查API配置";
                                    }
                                }
                            }
                        }
                        return "未找到content内容: " + responseStr.toString();
                    } catch (JSONException e) {
                        System.err.println("JSON解析错误: " + e.getMessage());
                        return "JSON解析错误: " + e.getMessage();
                    }
                }
            } else {
                // 读取错误响应
                try (BufferedReader errorReader = new BufferedReader(
                        new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
                    String errorLine;
                    StringBuilder errorResponse = new StringBuilder();
                    while ((errorLine = errorReader.readLine()) != null) {
                        errorResponse.append(errorLine);
                    }
                    System.err.println("服务器错误响应: " + errorResponse.toString());
                    return "错误响应: " + statusCode + " - " + errorResponse.toString();
                } catch (Exception e) {
                    // 如果在读取错误流时发生异常
                    System.err.println("读取错误响应时发生异常: " + e.getMessage());
                    return "错误响应: " + statusCode + " - 无法读取详细错误信息";
                }
            }
        } catch (IOException e) {
            System.err.println("发生IO异常: " + e.getMessage());
            e.printStackTrace();
            return "error: " + e.getMessage();
        }
    }

    /**
     * 分析文档内容
     * @param documentText 文档文本内容
     * @param useStream 是否使用流式API
     * @param chunkSize 分块大小
     * @param customPrompt 自定义分析要求
     * @param mergeResults 是否合并结果
     * @return 分析结果
     */
    public static String doAnalyzeDocument(String documentText, boolean useStream, int chunkSize, String customPrompt, boolean mergeResults) {
        System.out.println("文档文本总长度: " + documentText.length() + " 字符");
        System.out.println("分块大小: " + chunkSize + " 字符");
        System.out.println("是否使用流式API: " + useStream);
        System.out.println("是否合并结果: " + mergeResults);
        
        // 如果内容过长，分块处理
        if (documentText.length() > chunkSize) {
            System.out.println("文档内容过长，将分为多个块处理...");
            List<String> chunks = splitTextIntoChunks(documentText, chunkSize);
            System.out.println("分块数量: " + chunks.size());
            
            StringBuilder resultBuilder = new StringBuilder();
            int chunkIndex = 1;
            
            for (String chunk : chunks) {
                System.out.println("\n处理第 " + chunkIndex + "/" + chunks.size() + " 块 (长度: " + chunk.length() + " 字符)");
                
                String prompt;
                if (chunkIndex == 1) {
                    // 第一块使用完整指令
                    prompt = customPrompt  + chunkIndex + "/" + chunks.size() + " 部分：\n\n";
                } else {
                    // 后续块使用简化指令
                    prompt = "这是文档的第 " + chunkIndex + "/" + chunks.size() + " 部分，请继续分析。仅输出表格内容，不要输出任何分析过程或总结：\n\n";
                }
                
                String messages = "[{\"role\": \"system\", \"content\": \"你是一个专业的报价文档分析助手。你的回复必须只包含硬件部分和软件部分的表格内容，不要包含任何其他文字。\"}, {\"role\": \"user\", \"content\": \"" + escapeJsonString(prompt + chunk) + "\"}]";
                String result = postChatCompletion(messages, useStream);
                
                if (result.startsWith("error:")) {
                    System.err.println("处理第 " + chunkIndex + " 块时出错: " + result);
                    // 尝试缩小分块，重试这一块
                    if (chunk.length() > chunkSize / 2) {
                        System.out.println("尝试将该块进一步分割后重试...");
                        List<String> smallerChunks = splitTextIntoChunks(chunk, chunkSize / 2);
                        StringBuilder retryResult = new StringBuilder();
                        
                        for (int i = 0; i < smallerChunks.size(); i++) {
                            String smallerChunk = smallerChunks.get(i);
                            String retryPrompt = "这是文档第 " + chunkIndex + " 块的第 " + (i+1) + "/" + smallerChunks.size() + " 小块，请分析。仅输出表格内容，不要输出任何分析过程或总结：\n\n";
                            String retryMessages = "[{\"role\": \"system\", \"content\": \"你是一个专业的报价文档分析助手。你的回复必须只包含硬件部分和软件部分的表格内容，不要包含任何其他文字。\"}, {\"role\": \"user\", \"content\": \"" + escapeJsonString(retryPrompt + smallerChunk) + "\"}]";
                            String retryChunkResult = postChatCompletion(retryMessages, false); // 使用非流式API重试
                            
                            if (!retryChunkResult.startsWith("error:")) {
                                retryResult.append(retryChunkResult).append("\n\n");
                            } else {
                                System.err.println("重试小块失败: " + retryChunkResult);
                            }
                        }
                        
                        if (retryResult.length() > 0) {
                            result = retryResult.toString();
                        }
                    }
                }
                
                if (mergeResults) {
                    resultBuilder.append(result).append("\n\n");
                } else {
                    System.out.println("\n=== 第 " + chunkIndex + " 块结果 ===\n");
                    System.out.println(result);
                    System.out.println("\n=== 第 " + chunkIndex + " 块结果结束 ===\n");
                }
                
                chunkIndex++;
            }
            
            if (mergeResults) {
                                    // 合并所有结果后，可以再进行一次整合总结
                    if (chunks.size() > 1) {
                        System.out.println("\n所有块处理完毕，正在生成最终整合...");
                        String summaryPrompt = "以下是对一份招标文档各部分的分析结果，请将这些信息整合成一个完整的表格，去除重复内容，确保格式统一。仅输出硬件部分和软件部分两个表格，不要输出任何表格外的文字内容：\n\n";
                        String summaryMessages = "[{\"role\": \"system\", \"content\": \"你是一个专业的招标文档分析助手。你的回复必须只包含硬件部分和软件部分两个表格，不要包含任何其他文字内容。\"}, {\"role\": \"user\", \"content\": \"" + escapeJsonString(summaryPrompt + resultBuilder.toString()) + "\"}]";
                    
                    try {
                        String finalSummary = postChatCompletion(summaryMessages, false); // 使用非流式API处理总结
                        return finalSummary;
                    } catch (Exception e) {
                        System.err.println("生成最终总结时出错: " + e.getMessage());
                        return resultBuilder.toString(); // 返回未合并的结果
                    }
                } else {
                    return resultBuilder.toString();
                }
            } else {
                return "已完成所有块的处理，但未合并结果。请查看上方输出。";
            }
        } else {
            // 内容不长，直接处理
            System.out.println("文档内容长度适中，直接处理...");
            String prompt = customPrompt != null ? customPrompt : "你是一个专业的文档分析专家。请仔细分析以下招标文档内容，提取关键信息，包括项目名称、产品类型、技术规格要求等。要求简洁明了地整理成表格形式。仅输出表格内容，不要输出任何表格外的文字内容：\n\n";
            String messages = "[{\"role\": \"system\", \"content\": \"你是一个专业的招标文档分析助手。你的回复必须只包含硬件部分和软件部分两个表格，不要包含任何其他文字内容。\"}, {\"role\": \"user\", \"content\": \"" + escapeJsonString(prompt + documentText) + "\"}]";
            return postChatCompletion(messages, useStream);
        }
    }
    
    /**
     * 将文本分割成多个块
     * @param text 要分割的文本
     * @param chunkSize 每块的大小
     * @return 分割后的块列表
     */
    private static List<String> splitTextIntoChunks(String text, int chunkSize) {
        List<String> chunks = new ArrayList<>();
        
        // 按段落分割，避免在句子中间断开
        String[] paragraphs = text.split("\n");
        StringBuilder currentChunk = new StringBuilder();
        
        for (String paragraph : paragraphs) {
            // 如果当前段落本身就超过块大小，需要进一步分割
            if (paragraph.length() > chunkSize) {
                // 先处理当前块中已有的内容
                if (currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString());
                    currentChunk = new StringBuilder();
                }
                
                // 按句子分割长段落
                String[] sentences = paragraph.split("(?<=[。.!?！？]\\s*)");
                StringBuilder sentenceChunk = new StringBuilder();
                
                for (String sentence : sentences) {
                    if (sentenceChunk.length() + sentence.length() > chunkSize) {
                        if (sentenceChunk.length() > 0) {
                            chunks.add(sentenceChunk.toString());
                            sentenceChunk = new StringBuilder();
                        }
                        
                        // 如果单个句子超过块大小，则按字符强制分割
                        if (sentence.length() > chunkSize) {
                            int start = 0;
                            while (start < sentence.length()) {
                                int end = Math.min(start + chunkSize, sentence.length());
                                chunks.add(sentence.substring(start, end));
                                start = end;
                            }
                        } else {
                            sentenceChunk.append(sentence);
                        }
                    } else {
                        sentenceChunk.append(sentence);
                    }
                }
                
                if (sentenceChunk.length() > 0) {
                    chunks.add(sentenceChunk.toString());
                }
            } 
            // 如果添加当前段落会超出块大小，先保存当前块
            else if (currentChunk.length() + paragraph.length() + 1 > chunkSize) {
                chunks.add(currentChunk.toString());
                currentChunk = new StringBuilder(paragraph);
            } 
            // 否则将段落添加到当前块
            else {
                if (currentChunk.length() > 0) {
                    currentChunk.append("\n");
                }
                currentChunk.append(paragraph);
            }
        }
        
        // 添加最后一个块
        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString());
        }
        
        return chunks;
    }

    /**
     * 将字符串转换为JSON安全的格式
     * @param str 要转换的字符串
     * @return JSON安全的格式
     */
    private static String escapeJsonString(String str) {
        return str.replace("\\", "\\\\").replace("\"", "\\\"").replace("\n", "\\n").replace("\r", "\\r").replace("\t", "\\t");
    }
}
