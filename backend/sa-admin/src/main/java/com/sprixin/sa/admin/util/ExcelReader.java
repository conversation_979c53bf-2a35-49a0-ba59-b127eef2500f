package com.sprixin.sa.admin.util;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class ExcelReader {


    
    /**
     * 将报价数据生成Excel文件
     * @param data 报价单数据
     * @return Excel工作簿
     * @throws Exception 文件写入异常
     */
    public static XSSFWorkbook generateQuotationExcel(QuotationData data) throws Exception {
        XSSFWorkbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("报价单");
            
            // 设置列宽
            sheet.setColumnWidth(0, 8 * 256);  // 序号
            sheet.setColumnWidth(1, 25 * 256); // 项目
            sheet.setColumnWidth(2, 25 * 256); // 规格
            sheet.setColumnWidth(3, 8 * 256);  // 单位
            sheet.setColumnWidth(4, 8 * 256);  // 数量
            sheet.setColumnWidth(5, 12 * 256); // 原产地/许可类型/高度
            sheet.setColumnWidth(6, 12 * 256); // 制造商/开发商/材料
            sheet.setColumnWidth(7, 12 * 256); // 单价
            sheet.setColumnWidth(8, 12 * 256); // 总价
            sheet.setColumnWidth(9, 20 * 256); // 备注
            
            // 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle titleStyle = createTitleStyle(workbook);
            CellStyle normalStyle = createNormalStyle(workbook);
            CellStyle sectionStyle = createSectionStyle(workbook);
            CellStyle noteStyle = createNoteStyle(workbook);
        CellStyle subtotalStyle = createSubtotalStyle(workbook); // 创建小计样式
            
            // 写入表头信息
            writeQuotationHeader(sheet, data.getHeader(), headerStyle, titleStyle);
            
            // 当前行索引
        int rowIndex = 9;
            
            // 写入硬件部分标题
        rowIndex = writeSectionTitle(sheet, rowIndex, data.getHardwareTitle(), sectionStyle);
            
            // 写入硬件部分表头
            rowIndex = writeTableHeader(sheet, rowIndex, normalStyle);
        
        // 记录硬件部分开始行
        int hardwareStartRow = rowIndex;
            
            // 写入硬件部分数据
            if (data.getHardwareItems() != null && !data.getHardwareItems().isEmpty()) {
                for (HardwareItem item : data.getHardwareItems()) {
                    Row row = sheet.createRow(rowIndex++);
                    writeItemRow(row, item, normalStyle);
                }
            }
        
        // 添加硬件部分小计行
        double hardwareTotalPrice = 0;
        if (data.getHardwareItems() != null) {
            for (HardwareItem item : data.getHardwareItems()) {
                hardwareTotalPrice += item.getTotalPrice();
            }
        }
        
        Row hardwareSubtotalRow = sheet.createRow(rowIndex++);
        Cell hardwareSubtotalLabelCell = hardwareSubtotalRow.createCell(0);
        hardwareSubtotalLabelCell.setCellValue("硬件部分小计");
        hardwareSubtotalLabelCell.setCellStyle(subtotalStyle);
        
        // 合并小计标签单元格
        sheet.addMergedRegion(new CellRangeAddress(hardwareSubtotalRow.getRowNum(), hardwareSubtotalRow.getRowNum(), 0, 7));
        
        // 设置合并后的其他单元格样式
        for (int i = 1; i <= 7; i++) {
            Cell cell = hardwareSubtotalRow.createCell(i);
            cell.setCellStyle(subtotalStyle);
        }
        
        // 设置小计金额
        Cell hardwareSubtotalValueCell = hardwareSubtotalRow.createCell(8);
        hardwareSubtotalValueCell.setCellValue(hardwareTotalPrice);
        hardwareSubtotalValueCell.setCellStyle(subtotalStyle);
        
        // 备注单元格
        Cell hardwareSubtotalRemarkCell = hardwareSubtotalRow.createCell(9);
        hardwareSubtotalRemarkCell.setCellStyle(subtotalStyle);
        
        // 添加空行
        rowIndex++;
            
            // 写入软件部分标题
        rowIndex = writeSectionTitle(sheet, rowIndex,data.getSoftwareTitle(), sectionStyle);
            
            // 写入软件部分表头
            rowIndex = writeTableHeader(sheet, rowIndex, normalStyle);
        
        // 记录软件部分开始行
        int softwareStartRow = rowIndex;
            
            // 写入软件部分数据
            if (data.getSoftwareItems() != null && !data.getSoftwareItems().isEmpty()) {
                for (SoftwareItem item : data.getSoftwareItems()) {
                    Row row = sheet.createRow(rowIndex++);
                    writeItemRow(row, item, normalStyle);
                }
            }
        
        // 添加软件部分小计行
        double softwareTotalPrice = 0;
        if (data.getSoftwareItems() != null) {
            for (SoftwareItem item : data.getSoftwareItems()) {
                softwareTotalPrice += item.getTotalPrice();
            }
        }
        
        Row softwareSubtotalRow = sheet.createRow(rowIndex++);
        Cell softwareSubtotalLabelCell = softwareSubtotalRow.createCell(0);
        softwareSubtotalLabelCell.setCellValue("软件部分小计");
        softwareSubtotalLabelCell.setCellStyle(subtotalStyle);
        
        // 合并小计标签单元格
        sheet.addMergedRegion(new CellRangeAddress(softwareSubtotalRow.getRowNum(), softwareSubtotalRow.getRowNum(), 0, 7));
        
        // 设置合并后的其他单元格样式
        for (int i = 1; i <= 7; i++) {
            Cell cell = softwareSubtotalRow.createCell(i);
            cell.setCellStyle(subtotalStyle);
        }
        
        // 设置小计金额
        Cell softwareSubtotalValueCell = softwareSubtotalRow.createCell(8);
        softwareSubtotalValueCell.setCellValue(softwareTotalPrice);
        softwareSubtotalValueCell.setCellStyle(subtotalStyle);
        
        // 备注单元格
        Cell softwareSubtotalRemarkCell = softwareSubtotalRow.createCell(9);
        softwareSubtotalRemarkCell.setCellStyle(subtotalStyle);
        
        // 添加空行
        rowIndex++;
            
            // 写入测风塔部分标题
        rowIndex = writeSectionTitle(sheet, rowIndex, data.getWindTowerTitle(), sectionStyle);
            
            // 写入测风塔部分表头
            rowIndex = writeTableHeader(sheet, rowIndex, normalStyle);
            
            // 写入测风塔部分数据
            if (data.getWindTowerItems() != null && !data.getWindTowerItems().isEmpty()) {
                for (WindTowerItem item : data.getWindTowerItems()) {
                    Row row = sheet.createRow(rowIndex++);
                    writeItemRow(row, item, normalStyle);
                }
            }
        
        // 添加空行
        rowIndex++;
        
        // 添加总价行
        Row totalRow = sheet.createRow(rowIndex++);
        Cell totalLabelCell = totalRow.createCell(0);
        totalLabelCell.setCellValue("总价");
        totalLabelCell.setCellStyle(subtotalStyle);
    
        
        // 合并总价标签单元格
        sheet.addMergedRegion(new CellRangeAddress(totalRow.getRowNum(), totalRow.getRowNum(), 0, 7));
        
        // 设置合并后的其他单元格样式
        for (int i = 1; i <= 7; i++) {
            Cell cell = totalRow.createCell(i);
            cell.setCellStyle(subtotalStyle);
        }
        
        // 设置总价金额
        Cell totalValueCell = totalRow.createCell(8);
        totalValueCell.setCellValue(data.calculateTotalPrice());
        totalValueCell.setCellStyle(subtotalStyle);
        
        // 备注单元格
        Cell totalRemarkCell = totalRow.createCell(9);
        totalRemarkCell.setCellStyle(subtotalStyle);
            
            // 空行
            rowIndex++;
        
        // 创建备注标题
        Row noteTitleRow = sheet.createRow(rowIndex++);
        Cell noteTitleCell = noteTitleRow.createCell(0);
        noteTitleCell.setCellValue("备注:");
        noteTitleCell.setCellStyle(noteStyle);
        
        // 记录备注开始行
        int noteStartRow = rowIndex - 1;
        int noteContentStartRow = rowIndex;
            
            // 写入备注信息
            if (data.getNotes() != null && !data.getNotes().isEmpty()) {
                // 按备注编号排序
                List<QuotationNote> sortedNotes = new ArrayList<>(data.getNotes());
                sortedNotes.sort((n1, n2) -> Integer.compare(n1.getNumber(), n2.getNumber()));
                
                for (QuotationNote note : sortedNotes) {
                    Row row = sheet.createRow(rowIndex++);
                
                // 为每个单元格设置样式，确保整个行都有相同的样式
                for (int i = 0; i <= 9; i++) {
                    Cell cell = row.createCell(i);
                    if (i == 0) {
                    cell.setCellValue(note.getNumber() + "、" + note.getContent());
                    }
                    cell.setCellStyle(noteStyle);
                }
                
                // 合并当前行的单元格
                    sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(), row.getRowNum(), 0, 9));
                }
        } else {
            // 如果没有备注，至少添加一行空白备注行
            Row emptyRow = sheet.createRow(rowIndex++);
            for (int i = 0; i <= 9; i++) {
                Cell cell = emptyRow.createCell(i);
                cell.setCellStyle(noteStyle);
            }
            sheet.addMergedRegion(new CellRangeAddress(emptyRow.getRowNum(), emptyRow.getRowNum(), 0, 9));
        }
        
        // 合并备注标题行
        sheet.addMergedRegion(new CellRangeAddress(noteStartRow, noteStartRow, 0, 9));
        
        // 移除表格边框设置
        
        return workbook;
    }
    
    /**
     * 创建小计样式
     */
    private static CellStyle createSubtotalStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 11);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.RIGHT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        // 添加边框
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        
        return style;
    }
    
    /**
     * 创建表头样式
     */
    private static CellStyle createHeaderStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        //font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER); // 居中对齐
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);
        
        return style;
    }
    
    /**
     * 创建标题样式
     */
    private static CellStyle createTitleStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER); // 居中对齐
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);
        
        return style;
    }
    
    /**
     * 创建普通单元格样式
     */
    private static CellStyle createNormalStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);
        
        // 添加边框
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        
        // 设置字体，确保不加粗
        XSSFFont font = workbook.createFont();
        font.setBold(false);
        font.setFontHeightInPoints((short) 11);
        style.setFont(font);
        
        return style;
    }
    
    /**
     * 创建分区标题样式
     */
    private static CellStyle createSectionStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER); // 居中对齐
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        return style;
    }
    
    /**
     * 创建备注样式
     */
    private static CellStyle createNoteStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);
    
        
        return style;
    }
    
    /**
     * 写入报价单表头信息
     */
    private static void writeQuotationHeader(Sheet sheet, QuotationHeader header, CellStyle headerStyle, CellStyle titleStyle) {
        // 创建表头专用样式（居中对齐）
        CellStyle centerHeaderStyle = sheet.getWorkbook().createCellStyle();
        centerHeaderStyle.cloneStyleFrom(headerStyle);
        centerHeaderStyle.setAlignment(HorizontalAlignment.CENTER);
        
        // 创建标题专用样式（居中对齐）
        CellStyle centerTitleStyle = sheet.getWorkbook().createCellStyle();
        centerTitleStyle.cloneStyleFrom(titleStyle);
        centerTitleStyle.setAlignment(HorizontalAlignment.CENTER);
        
        // 创建左对齐样式（用于网址、地址等信息）
        CellStyle leftAlignStyle = sheet.getWorkbook().createCellStyle();
        leftAlignStyle.cloneStyleFrom(headerStyle);
        leftAlignStyle.setAlignment(HorizontalAlignment.LEFT);
        
        // 创建右对齐样式（用于报价日期等右侧信息）
        CellStyle rightAlignStyle = sheet.getWorkbook().createCellStyle();
        rightAlignStyle.cloneStyleFrom(headerStyle);
        rightAlignStyle.setAlignment(HorizontalAlignment.RIGHT);
        
        // 公司名称
        Row companyRow = sheet.createRow(0);
        companyRow.setHeight((short) 600);
        Cell companyCell = companyRow.createCell(0);
        companyCell.setCellValue(header.getCompanyName());
        companyCell.setCellStyle(centerTitleStyle); // 使用居中样式
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 9));
        
        // 报价单标题
        Row titleRow = sheet.createRow(1);
        titleRow.setHeight((short) 600);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(header.getTitle());
        titleCell.setCellStyle(centerTitleStyle); // 使用居中样式
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 9));
        
        // 服务描述
        Row serviceRow = sheet.createRow(2);
        Cell serviceCell = serviceRow.createCell(0);
        serviceCell.setCellValue(header.getServiceDescription());
        serviceCell.setCellStyle(centerHeaderStyle); // 使用居中样式
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 9));
        
        // 网址和报价日期
        Row websiteRow = sheet.createRow(3);
        
        // 左侧：网址
        Cell websiteCell = websiteRow.createCell(0);
        websiteCell.setCellValue("网址：");
        websiteCell.setCellStyle(leftAlignStyle); // 使用左对齐样式
        
        Cell websiteValueCell = websiteRow.createCell(1);
        websiteValueCell.setCellValue(header.getWebsite());
        websiteValueCell.setCellStyle(leftAlignStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 1, 4));
        
        // 右侧标签：报价日期
        Cell dateLabelCell = websiteRow.createCell(7);
        dateLabelCell.setCellValue("报价日期：");
        dateLabelCell.setCellStyle(rightAlignStyle); // 使用右对齐样式
        
        // 右侧值：报价日期
        Cell dateValueCell = websiteRow.createCell(8);
        dateValueCell.setCellValue(header.getQuotationDate());
        dateValueCell.setCellStyle(leftAlignStyle); // 使用左对齐样式
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 8, 9));
        
        // 地址和项目名称
        Row addressRow = sheet.createRow(4);
        
        // 左侧：地址
        Cell addressCell = addressRow.createCell(0);
        addressCell.setCellValue("地址：");
        addressCell.setCellStyle(leftAlignStyle); // 使用左对齐样式
        
        Cell addressValueCell = addressRow.createCell(1);
        addressValueCell.setCellValue(header.getAddress());
        addressValueCell.setCellStyle(leftAlignStyle);
        sheet.addMergedRegion(new CellRangeAddress(4, 4, 1, 4));
        
        // 右侧标签：项目名称
        Cell projectLabelCell = addressRow.createCell(7);
        projectLabelCell.setCellValue("项目名称：");
        projectLabelCell.setCellStyle(rightAlignStyle); // 使用右对齐样式
        
        // 右侧值：项目名称
        Cell projectValueCell = addressRow.createCell(8);
        projectValueCell.setCellValue(header.getProjectName());
        projectValueCell.setCellStyle(leftAlignStyle); // 使用左对齐样式
        sheet.addMergedRegion(new CellRangeAddress(4, 4, 8, 9));
        
        // 联系人和报价单位
        Row contactRow = sheet.createRow(5);
        
        // 左侧：联系人
        Cell contactCell = contactRow.createCell(0);
        contactCell.setCellValue("联系人：");
        contactCell.setCellStyle(leftAlignStyle); // 使用左对齐样式
        
        Cell contactValueCell = contactRow.createCell(1);
        contactValueCell.setCellValue(header.getContactPerson());
        contactValueCell.setCellStyle(leftAlignStyle);
        sheet.addMergedRegion(new CellRangeAddress(5, 5, 1, 4));
        
        // 右侧标签：报价单位
        Cell currencyLabelCell = contactRow.createCell(7);
        currencyLabelCell.setCellValue("报价单位：");
        currencyLabelCell.setCellStyle(rightAlignStyle); // 使用右对齐样式
        
        // 右侧值：报价单位
        Cell currencyValueCell = contactRow.createCell(8);
        currencyValueCell.setCellValue(header.getCurrency());
        currencyValueCell.setCellStyle(leftAlignStyle); // 使用左对齐样式
        sheet.addMergedRegion(new CellRangeAddress(5, 5, 8, 9));
        
        // 联系方式
        Row phoneRow = sheet.createRow(6);
        Cell phoneLabelCell = phoneRow.createCell(0);
        phoneLabelCell.setCellValue("联系方式：");
        phoneLabelCell.setCellStyle(leftAlignStyle); // 使用左对齐样式
        
        Cell phoneValueCell = phoneRow.createCell(1);
        phoneValueCell.setCellValue(header.getContactPhone());
        phoneValueCell.setCellStyle(leftAlignStyle);
        sheet.addMergedRegion(new CellRangeAddress(6, 6, 1, 9));
        
        // 邮箱
        Row emailRow = sheet.createRow(7);
        Cell emailLabelCell = emailRow.createCell(0);
        emailLabelCell.setCellValue("邮箱：");
        emailLabelCell.setCellStyle(leftAlignStyle); // 使用左对齐样式
        
        Cell emailValueCell = emailRow.createCell(1);
        emailValueCell.setCellValue(header.getContactEmail());
        emailValueCell.setCellStyle(leftAlignStyle);
        sheet.addMergedRegion(new CellRangeAddress(7, 7, 1, 9));
        
        // 空行
        sheet.createRow(8);
        sheet.createRow(9);
    }
    
    /**
     * 写入分区标题
     * @return 下一行索引
     */
    private static int writeSectionTitle(Sheet sheet, int rowIndex, String title, CellStyle style) {
        Row row = sheet.createRow(rowIndex++);
        Cell cell = row.createCell(0);
        cell.setCellValue(title);
        
        // 创建居中样式
        CellStyle centerStyle = sheet.getWorkbook().createCellStyle();
        centerStyle.cloneStyleFrom(style);
        centerStyle.setAlignment(HorizontalAlignment.CENTER);
        
        cell.setCellStyle(centerStyle);
        sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(), row.getRowNum(), 0, 9));
        return rowIndex;
    }
    
    /**
     * 写入表格表头
     * @return 下一行索引
     */
    private static int writeTableHeader(Sheet sheet, int rowIndex, CellStyle style) {
        Row row = sheet.createRow(rowIndex++);
        
        String[] headers = {"序号", "项目", "规格", "单位", "数量", "产地", "生产厂家", "单价", "总价", "备注"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(headers[i]);
            
            // 创建表头样式（左对齐，不加粗）
            CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
            headerStyle.cloneStyleFrom(style);
            headerStyle.setAlignment(HorizontalAlignment.LEFT);
            
            // 设置字体不加粗
            XSSFFont font = (XSSFFont) sheet.getWorkbook().createFont();
            font.setBold(false);
            font.setFontHeightInPoints((short) 11);
            headerStyle.setFont(font);
            
            cell.setCellStyle(headerStyle);
        }
        
        return rowIndex;
    }
    
    /**
     * 写入项目行数据
     */
    private static void writeItemRow(Row row, BaseItem item, CellStyle style) {
        // 序号
        Cell serialCell = row.createCell(0);
        serialCell.setCellValue(item.getSerialNumber());
        serialCell.setCellStyle(style);
        
        // 项目
        Cell nameCell = row.createCell(1);
        nameCell.setCellValue(item.getName());
        nameCell.setCellStyle(style);
        
        // 规格
        Cell specCell = row.createCell(2);
        specCell.setCellValue(item.getSpecification());
        specCell.setCellStyle(style);
        
        // 单位
        Cell unitCell = row.createCell(3);
        unitCell.setCellValue(item.getUnit());
        unitCell.setCellStyle(style);
        
        // 数量
        Cell quantityCell = row.createCell(4);
        quantityCell.setCellValue(item.getQuantity());
        quantityCell.setCellStyle(style);
        
        // 原产地/许可类型/高度
        Cell field5Cell = row.createCell(5);
        if (item instanceof HardwareItem) {
            field5Cell.setCellValue(((HardwareItem) item).getOrigin());
        } else if (item instanceof SoftwareItem) {
            field5Cell.setCellValue(((SoftwareItem) item).getOrigin());
        } else if (item instanceof WindTowerItem) {
            field5Cell.setCellValue(((WindTowerItem) item).getOrigin());
        }
        field5Cell.setCellStyle(style);
        
        // 制造商/开发商/材料
        Cell field6Cell = row.createCell(6);
        if (item instanceof HardwareItem) {
            field6Cell.setCellValue(((HardwareItem) item).getManufacturer());
        } else if (item instanceof SoftwareItem) {
            field6Cell.setCellValue(((SoftwareItem) item).getManufacturer());
        } else if (item instanceof WindTowerItem) {
            field6Cell.setCellValue(((WindTowerItem) item).getManufacturer());
        }
        field6Cell.setCellStyle(style);
        
        // 单价
        Cell unitPriceCell = row.createCell(7);
        unitPriceCell.setCellValue(item.getUnitPrice());
        unitPriceCell.setCellStyle(style);
        
        // 总价
        Cell totalPriceCell = row.createCell(8);
        totalPriceCell.setCellValue(item.getTotalPrice());
        totalPriceCell.setCellStyle(style);
        
        // 备注
        Cell remarksCell = row.createCell(9);
        remarksCell.setCellValue(item.getRemarks());
        remarksCell.setCellStyle(style);
    }
    
    /**
     * 报价单数据类，包含不同部分的数据
     */
    public static class QuotationData {
        private QuotationHeader header; // 报价单表头信息
        private String hardwareTitle; // 硬件部分标题
        private List<HardwareItem> hardwareItems; // 硬件部分项目
        private String softwareTitle; // 软件部分标题
        private List<SoftwareItem> softwareItems; // 软件部分项目
        private String windTowerTitle; // 测风塔部分标题
        private List<WindTowerItem> windTowerItems; // 测风塔部分项目
        private List<QuotationNote> notes; // 备注信息
        
        // Getters and Setters
        public QuotationHeader getHeader() { return header; }
        public void setHeader(QuotationHeader header) { this.header = header; }
        
        public String getHardwareTitle() { return hardwareTitle; }
        public void setHardwareTitle(String hardwareTitle) { this.hardwareTitle = hardwareTitle; }
        
        public List<HardwareItem> getHardwareItems() { return hardwareItems; }
        public void setHardwareItems(List<HardwareItem> hardwareItems) { this.hardwareItems = hardwareItems; }
        
        public String getSoftwareTitle() { return softwareTitle; }
        public void setSoftwareTitle(String softwareTitle) { this.softwareTitle = softwareTitle; }
        
        public List<SoftwareItem> getSoftwareItems() { return softwareItems; }
        public void setSoftwareItems(List<SoftwareItem> softwareItems) { this.softwareItems = softwareItems; }
        
        public String getWindTowerTitle() { return windTowerTitle; }
        public void setWindTowerTitle(String windTowerTitle) { this.windTowerTitle = windTowerTitle; }
        
        public List<WindTowerItem> getWindTowerItems() { return windTowerItems; }
        public void setWindTowerItems(List<WindTowerItem> windTowerItems) { this.windTowerItems = windTowerItems; }
        
        public List<QuotationNote> getNotes() { return notes; }
        public void setNotes(List<QuotationNote> notes) { this.notes = notes; }
        
        /**
         * 计算总价
         */
        public double calculateTotalPrice() {
            double total = 0;
            
            if (hardwareItems != null) {
                for (HardwareItem item : hardwareItems) {
                    total += item.getTotalPrice();
                }
            }
            
            if (softwareItems != null) {
                for (SoftwareItem item : softwareItems) {
                    total += item.getTotalPrice();
                }
            }
            
            if (windTowerItems != null) {
                for (WindTowerItem item : windTowerItems) {
                    total += item.getTotalPrice();
                }
            }
            
            return total;
        }
    }
    
    /**
     * 报价单表头信息类
     */
    public static class QuotationHeader {
        private String companyName; // 公司名称
        private String title; // 报价单标题
        private String serviceDescription; // 服务描述
        private String website; // 网址
        private String quotationDate; // 报价日期
        private String address; // 地址
        private String projectName; // 项目名称
        private String contactPerson; // 联系人
        private String currency; // 报价单位
        private String contactPhone; // 联系方式
        private String contactEmail; // 邮箱
        
        // Getters and Setters
        public String getCompanyName() { return companyName; }
        public void setCompanyName(String companyName) { this.companyName = companyName; }
        
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        
        public String getServiceDescription() { return serviceDescription; }
        public void setServiceDescription(String serviceDescription) { this.serviceDescription = serviceDescription; }
        
        public String getWebsite() { return website; }
        public void setWebsite(String website) { this.website = website; }
        
        public String getQuotationDate() { return quotationDate; }
        public void setQuotationDate(String quotationDate) { this.quotationDate = quotationDate; }
        
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
        
        public String getProjectName() { return projectName; }
        public void setProjectName(String projectName) { this.projectName = projectName; }
        
        public String getContactPerson() { return contactPerson; }
        public void setContactPerson(String contactPerson) { this.contactPerson = contactPerson; }
        
        public String getCurrency() { return currency; }
        public void setCurrency(String currency) { this.currency = currency; }
        
        public String getContactPhone() { return contactPhone; }
        public void setContactPhone(String contactPhone) { this.contactPhone = contactPhone; }
        
        public String getContactEmail() { return contactEmail; }
        public void setContactEmail(String contactEmail) { this.contactEmail = contactEmail; }
        
        @Override
        public String toString() {
            return "QuotationHeader{" +
                    "companyName='" + companyName + '\'' +
                    ", title='" + title + '\'' +
                    ", serviceDescription='" + serviceDescription + '\'' +
                    ", website='" + website + '\'' +
                    ", quotationDate='" + quotationDate + '\'' +
                    ", address='" + address + '\'' +
                    ", projectName='" + projectName + '\'' +
                    ", contactPerson='" + contactPerson + '\'' +
                    ", currency='" + currency + '\'' +
                    ", contactPhone='" + contactPhone + '\'' +
                    ", contactEmail='" + contactEmail + '\'' +
                    '}';
        }
    }
    
    /**
     * 报价单备注信息类
     */
    public static class QuotationNote {
        private int number; // 备注编号
        private String content; // 备注内容
        
        // Getters and Setters
        public int getNumber() { return number; }
        public void setNumber(int number) { this.number = number; }
        
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        
        @Override
        public String toString() {
            return number + "、" + content;
        }
    }
    
    /**
     * 基础报价项目类
     */
    public static abstract class BaseItem {
        private int serialNumber;
        private String name;
        private String specification;
        private String unit;
        private int quantity;
        private double unitPrice;
        private double totalPrice;
        private String remarks;
        private String manufacturer;
        private String origin;
        
        // Getters and Setters
        public int getSerialNumber() { return serialNumber; }
        public void setSerialNumber(int serialNumber) { this.serialNumber = serialNumber; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getSpecification() { return specification; }
        public void setSpecification(String specification) { this.specification = specification; }
        
        public String getUnit() { return unit; }
        public void setUnit(String unit) { this.unit = unit; }
        
        public int getQuantity() { return quantity; }
        public void setQuantity(int quantity) { this.quantity = quantity; }
        
        public double getUnitPrice() { return unitPrice; }
        public void setUnitPrice(double unitPrice) { this.unitPrice = unitPrice; }
        
        public double getTotalPrice() { return totalPrice; }
        public void setTotalPrice(double totalPrice) { this.totalPrice = totalPrice; }
        
        public String getRemarks() { return remarks; }
        public void setRemarks(String remarks) { this.remarks = remarks; }

        public String getManufacturer() { return manufacturer; }
        public void setManufacturer(String manufacturer) { this.manufacturer = manufacturer; }

        public String getOrigin() { return origin; }
        public void setOrigin(String origin) { this.origin = origin; }
    }
    
    /**
     * 硬件项目类
     */
    public static class HardwareItem extends BaseItem {
        private String origin; // 原产地
        private String manufacturer; // 制造商
        
        // Getters and Setters
        public String getOrigin() { return origin; }
        public void setOrigin(String origin) { this.origin = origin; }
        
        public String getManufacturer() { return manufacturer; }
        public void setManufacturer(String manufacturer) { this.manufacturer = manufacturer; }
        
        @Override
        public String toString() {
            return "HardwareItem{" +
                    "serialNumber=" + getSerialNumber() +
                    ", name='" + getName() + '\'' +
                    ", specification='" + getSpecification() + '\'' +
                    ", unit='" + getUnit() + '\'' +
                    ", quantity=" + getQuantity() +
                    ", origin='" + origin + '\'' +
                    ", manufacturer='" + manufacturer + '\'' +
                    ", unitPrice=" + getUnitPrice() +
                    ", totalPrice=" + getTotalPrice() +
                    ", remarks='" + getRemarks() + '\'' +
                    '}';
        }
    }
    
    /**
     * 软件项目类
     */
    public static class SoftwareItem extends BaseItem {
        private String licenseType; // 许可证类型
        private String developer; // 开发商
        
        // Getters and Setters
        public String getLicenseType() { return licenseType; }
        public void setLicenseType(String licenseType) { this.licenseType = licenseType; }
        
        public String getDeveloper() { return developer; }
        public void setDeveloper(String developer) { this.developer = developer; }
        
        @Override
        public String toString() {
            return "SoftwareItem{" +
                    "serialNumber=" + getSerialNumber() +
                    ", name='" + getName() + '\'' +
                    ", specification='" + getSpecification() + '\'' +
                    ", unit='" + getUnit() + '\'' +
                    ", quantity=" + getQuantity() +
                    ", licenseType='" + licenseType + '\'' +
                    ", developer='" + developer + '\'' +
                    ", unitPrice=" + getUnitPrice() +
                    ", totalPrice=" + getTotalPrice() +
                    ", remarks='" + getRemarks() + '\'' +
                    '}';
        }
    }
    
    /**
     * 测风塔项目类
     */
    public static class WindTowerItem extends BaseItem {
        private String height; // 高度
        private String material; // 材料
        
        // Getters and Setters
        public String getHeight() { return height; }
        public void setHeight(String height) { this.height = height; }
        
        public String getMaterial() { return material; }
        public void setMaterial(String material) { this.material = material; }
        
        @Override
        public String toString() {
            return "WindTowerItem{" +
                    "serialNumber=" + getSerialNumber() +
                    ", name='" + getName() + '\'' +
                    ", specification='" + getSpecification() + '\'' +
                    ", unit='" + getUnit() + '\'' +
                    ", quantity=" + getQuantity() +
                    ", height='" + height + '\'' +
                    ", material='" + material + '\'' +
                    ", unitPrice=" + getUnitPrice() +
                    ", totalPrice=" + getTotalPrice() +
                    ", remarks='" + getRemarks() + '\'' +
                    '}';
        }
    }

   
}