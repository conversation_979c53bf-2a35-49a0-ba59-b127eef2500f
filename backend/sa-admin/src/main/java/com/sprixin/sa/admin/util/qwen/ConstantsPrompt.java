package com.sprixin.sa.admin.util.qwen;

public class ConstantsPrompt {

    //文档分析提示词
    public static String DOCUMNT_ANALYSIS_PROMPT = "请仔细分析以下电力新能源系统技术协议文档，严格按照指定格式提取硬件和软件配置信息：" + 
    "【分析目标】" + 
    "提取电力新能源系统中所有硬件设备和软件系统的详细配置参数、数量及供应商信息。" + 
    "【输出格式要求】" + 
    "严格按照以下表格格式输出，不得更改表格结构：" + 

    "## 硬件部分\n" + 
    "| 设备名称 | 配置 | 数量 | 备注 |\n" + 
    "|---------|------|------|------|\n" + 
    "|功率预测服务器| CPU配置,内存(GB),硬盘(TB),网口数量,电源配置 等|2|国能日新科技股份有限公司|\n" + 
    "|气象服务器| CPU配置,内存(GB),硬盘(TB),网口数量,电源配置 等|1|具体供应商名称|\n" + 
    "|数据采集服务器| CPU配置,内存(GB),硬盘(TB),网口数量,串口数量 等|1|具体供应商名称|\n" + 
    "|工程师站/工作站| CPU配置,内存(GB),硬盘(TB),网口数量,显示器尺寸 等|1|具体供应商名称|\n" + 
    "|防火墙| 接口数量,吞吐量,并发连接数,电源配置 等|2|具体供应商名称|\n" + 
    "|交换机| 端口数量,速率,电源配置 等|1|具体供应商名称|\n" + 
    "|隔离装置| 网口配置,传输方向,安全等级 等|1|具体供应商名称|\n" + 
    "|KVM及显示器| 显示器尺寸,VGA口数量,键鼠配套 等|1|具体供应商名称|\n" + 
    "|机柜| 尺寸规格,颜色,电源路数 等|1|具体供应商名称|\n" + 
    "|测风塔| 高度,传感器类型,测量参数 等|1|具体供应商名称|\n" + 
    "|气象站设备| 高度,传感器类型,测量参数 等|1|具体供应商名称|\n" + 

    "## 软件部分\n" + 
    "| 名称 | 配置 | 数量 | 备注 |\n" + 
    "|---------|------|------|------|\n" + 
    "|功率预测软件| 预测时长范围,预测精度要求,功能模块 等|1|国能日新科技股份有限公司|\n" + 
    "|操作系统| 系统类型,版本要求,安全等级 等|多套|具体供应商名称|\n" + 
    "|数据库软件| 数据库类型,版本,许可证数量 等|1|具体供应商名称|\n" + 
    "|短期预测模型| 预测时长,建模服务,算法类型 等|1|具体供应商名称|\n" + 
    "|超短期预测模型| 预测时长,建模服务,算法类型 等|1|具体供应商名称|\n" + 
    "|数值天气预报服务| 服务年限,数据源,更新频率 等|1|具体供应商名称|\n" + 
    "|技术维护服务| 服务年限,服务内容,维护范围 等|1|具体供应商名称|\n" + 
    "|网安探针软件| 兼容系统,功能要求,版本信息 等|多套|具体供应商名称|\n" + 

    "【关键提取要求】" + 
    "1. **硬件配置细节**：" + 
    "   - CPU：核心数、主频" + 
    "   - 内存：容量(GB)、类型(如ECC DDR3)" + 
    "   - 硬盘：容量(TB)、类型(如SAS、热插拔)" + 
    "   - 网口：数量、速率(如千兆)" + 
    "   - 电源：单/双电源、热插拔特性" + 

    "2. **软件功能要求**：" + 
    "   - 预测时长：超短期(0-4h)、短期(0-72h)等" + 
    "   - 系统要求：国产化、安全等级" + 
    "   - 服务年限：维护期限、技术支持" + 

    "3. **数量统计**：" + 
    "   - 准确提取每种设备的数量" + 
    "   - 区分单台、多台、成套设备" + 

    "4. **供应商信息**：" + 
    "   - 优先提取明确的供应商名称" + 
    "   - 如未指定供应商，标注\"未指定\"或相关要求" + 

    "5. **特殊要求标注**：" + 
    "   - 项目装机容量(如100MW、500MW)" + 
    "   - 环境要求(如抗覆冰、防护等级)" + 
    "   - 电网认证要求" + 
    "   - 颜色、尺寸等定制要求" + 

    "【注意事项】" + 
    "- 保持原文档中的技术参数准确性" + 
    "- 对于\"不少于\"、\"不低于\"等表述原样记录" + 
    "- 区分风电和光伏项目的配置差异" + 
    "- 将相同功能但不同规格的设备合并列出" + 
    "- 如某项信息不明确，在备注中说明" + 
    "请开始分析文档并严格按照上述表格格式输出结果。";
    
    //文档匹配提示词
    public static String DOCUMNT_MATCH_PROMPT = "\n【匹配规则】\n" +
                "1. 名称匹配：\n" +
                "   - 优先进行名称的精确匹配\n" +
                "   - 如无精确匹配，则进行语义相似度匹配\n" +
                "   - 考虑名称的同义词和常见缩写\n\n" +
                "2. 配置参数匹配：\n" +
                "   - 提取并标准化关键配置参数（如CPU、内存、硬盘等）\n" +
                "   - 对数值型参数进行范围匹配（如内存容量、硬盘大小）\n" +
                "   - 对文本型参数进行语义相似度匹配\n\n" +
                "3. 匹配优先级：\n" +
                "   - 名称匹配权重：40%\n" +
                "   - 配置参数匹配权重：60%\n" +
                "4. 匹配结果要求：\n" +
                "   - 每个目标设备必须匹配到最相似的记录\n" +
                "   - 相似度分数必须大于60%才视为有效匹配\n" +
                "   - 返回JSON格式的匹配结果，包含详细的匹配原因\n\n" +
                "返回格式示例：\n" +
                "[\n" +
                "  {\n" +
                "    \"deviceIndex\": 0,\n" +
                "    \"matchedIndex\": 1,\n" +
                "    \"similarityScore\": \"0.90\",\n" +
                "    \"matchReason\": \"名称匹配度0.85，配置参数匹配度0.95，综合相似度0.90\"\n" +
                "  }\n" +
                "]";
}
