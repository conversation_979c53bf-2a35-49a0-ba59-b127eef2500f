package com.sprixin.sa.admin.util.qwen;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class DocumentAnalysisMain {

    @Value("${qwen.api.key}")
    private  String apiKey;
    @Value("${qwen.api.url}")
    private  String url;
    @Value("${qwen.api.model}")
    private  String model;

    // 用于静态方法调用
    private static DocumentAnalysisMain instance;

    public DocumentAnalysisMain() {
        instance = this;
    }

    private static final Map<String, String> COLUMN_KEY_MAP = new HashMap<>();

    static {
        COLUMN_KEY_MAP.put("设备名称", "deviceName");
        COLUMN_KEY_MAP.put("名称", "deviceName");
        COLUMN_KEY_MAP.put("配置", "config");
        COLUMN_KEY_MAP.put("数量", "quantity");
        COLUMN_KEY_MAP.put("备注", "remark");
    }

    /**
     * 分析文档
     *
     * @param documentPath   文档路径
     * @return 分析结果
     */
    public List<HashMap<String, String>> analysisDocument(String documentPath) {
        List<HashMap<String, String>> allResults = new ArrayList<>();


        try {
            QwenApiClient client = new QwenApiClient(url, apiKey, model);
            documentPath = documentPath.replace("\\", "/");
            String documentContent = DocumentProcessor.extractTextFromFile(documentPath);
            String[] chunks = DocumentProcessor.splitTextIntoChunks(documentContent, 8000);

            for (int i = 0; i < chunks.length; i++) {
                String chunkPrompt = ConstantsPrompt.DOCUMNT_ANALYSIS_PROMPT + (chunks.length > 1 ? "（这是文档的第" + (i + 1) + "部分，共" + chunks.length + "部分）" : "");

                QwenResponse response = client.analyzeDocument(chunks[i], chunkPrompt);

                if (response.isSuccess()) {
                    String content = response.getAnalysisResult();
                    System.out.println("文档分解结果: " + content);
                    List<HashMap<String, String>> currentChunkResults = parseMarkdownTable(content);
                    allResults.addAll(currentChunkResults);
                }
            }

            // 在返回结果前添加去重步骤
            System.out.println("去重前设备数量: " + allResults.size());

            // 检查是否有重复设备
            Map<String, Long> deviceCount = allResults.stream()
                                                      .collect(Collectors.groupingBy(device ->
                                                              device.getOrDefault("deviceName", "").trim(),
                                                          Collectors.counting()));

            long duplicateCount = deviceCount.entrySet().stream()
                                             .filter(entry -> entry.getValue() > 1)
                                             .count();

            if (duplicateCount > 0) {
                System.out.println("检测到 " + duplicateCount + " 个重复设备名称需要合并");
                deviceCount.entrySet().stream()
                           .filter(entry -> entry.getValue() > 1)
                           .forEach(entry -> System.out.println("设备 '" + entry.getKey() + "' 有 " + entry.getValue() + " 条记录"));
            }

            allResults = removeDuplicateDevices(allResults);
            System.out.println("去重后设备数量: " + allResults.size());

        } catch (Exception e) {
            System.err.println("处理文档时出错：" + e.getMessage());
            e.printStackTrace();
        }

        return allResults;
    }

    /**
     * 移除重复设备，保留信息最完整的一条，不再合并关键信息
     *
     * @param devices 原始设备列表
     * @return 去重后的设备列表
     */
    private static List<HashMap<String, String>> removeDuplicateDevices(List<HashMap<String, String>> devices) {
        // 如果列表为空或只有一个元素，直接返回
        if (devices == null || devices.size() <= 1) {
            return devices;
        }

        // 按设备名称分组（不再使用配置作为分组依据）
        Map<String, List<HashMap<String, String>>> deviceGroups = devices.stream()
                                                                         .collect(Collectors.groupingBy(device -> {
                                                                             String deviceName = device.getOrDefault("deviceName", "").trim();
                                                                             return deviceName; // 只使用名称作为分组键
                                                                         }));

        // 对于每组重复设备，只保留信息最完整的一条记录
        List<HashMap<String, String>> uniqueDevices = new ArrayList<>();
        for (List<HashMap<String, String>> group : deviceGroups.values()) {
            if (group.size() == 1) {
                // 如果组内只有一个设备，直接添加
                uniqueDevices.add(group.get(0));
            } else {
                // 如果有多个，选择信息最完整的一条记录
                HashMap<String, String> bestDevice = selectBestDevice(group);
                uniqueDevices.add(bestDevice);
            }
        }

        return uniqueDevices;
    }

    /**
     * 合并多个设备记录的信息
     *
     * @param devices 需要合并的设备列表
     * @return 合并后的设备记录
     */
    private static HashMap<String, String> mergeDevices(List<HashMap<String, String>> devices) {
        // 选择信息最完整的设备作为基础
        HashMap<String, String> baseDevice = selectBestDevice(devices);
        HashMap<String, String> mergedDevice = new HashMap<>(baseDevice);

        // 收集所有设备的配置信息
        Set<String> configs = new HashSet<>();
        Set<String> quantities = new HashSet<>();
        Set<String> remarks = new HashSet<>();

        for (HashMap<String, String> device : devices) {
            // 合并配置信息
            String config = device.getOrDefault("config", "").trim();
            if (!config.isEmpty() && !config.equals("无明确配置要求")) {
                configs.add(config);
            }

            // 合并数量信息
            String quantity = device.getOrDefault("quantity", "").trim();
            if (!quantity.isEmpty() && !quantity.equals("未明确")) {
                quantities.add(quantity);
            }

            // 合并备注信息
            String remark = device.getOrDefault("remark", "").trim();
            if (!remark.isEmpty()) {
                remarks.add(remark);
            }
        }

        // 设置合并后的配置信息
        if (!configs.isEmpty()) {
            mergedDevice.put("config", String.join("; ", configs));
        }

        // 设置合并后的数量信息
        if (!quantities.isEmpty()) {
            // 尝试将数量转换为数字并累加
            try {
                int totalQuantity = 0;
                boolean allNumeric = true;

                for (String quantity : quantities) {
                    try {
                        totalQuantity += Integer.parseInt(quantity);
                    } catch (NumberFormatException e) {
                        allNumeric = false;
                        break;
                    }
                }

                if (allNumeric) {
                    mergedDevice.put("quantity", String.valueOf(totalQuantity));
                } else {
                    mergedDevice.put("quantity", String.join("; ", quantities));
                }
            } catch (Exception e) {
                // 如果转换失败，直接合并字符串
                mergedDevice.put("quantity", String.join("; ", quantities));
            }
        }

        // 设置合并后的备注信息
        if (!remarks.isEmpty()) {
            mergedDevice.put("remark", String.join("; ", remarks));
        }

        return mergedDevice;
    }

    /**
     * 从重复设备组中选择信息最完整的设备
     *
     * @param devices 重复设备组
     * @return 信息最完整的设备
     */
    private static HashMap<String, String> selectBestDevice(List<HashMap<String, String>> devices) {
        HashMap<String, String> bestDevice = devices.get(0);
        int maxScore = calculateCompleteness(bestDevice);

        for (int i = 1; i < devices.size(); i++) {
            HashMap<String, String> currentDevice = devices.get(i);
            int currentScore = calculateCompleteness(currentDevice);

            if (currentScore > maxScore) {
                maxScore = currentScore;
                bestDevice = currentDevice;
            }
        }

        return bestDevice;
    }

    /**
     * 计算设备信息的完整度分数
     *
     * @param device 设备信息
     * @return 完整度分数，分数越高表示信息越完整
     */
    private static int calculateCompleteness(HashMap<String, String> device) {
        int score = 0;

        // 检查各个关键字段是否存在且非空
        String[] keyFields = {"deviceName", "config", "quantity", "remark", "category"};
        for (String field : keyFields) {
            String value = device.get(field);
            if (value != null && !value.trim().isEmpty()) {
                score++;
                // 字段内容越长，分数越高（表示信息越详细）
                score += Math.min(5, value.length() / 10);
            }
        }

        return score;
    }

    /**
     * 解析Markdown表格
     *
     * @param markdownContent
     * @return 解析后的数据
     */
    private static List<HashMap<String, String>> parseMarkdownTable(String markdownContent) {
        List<HashMap<String, String>> parsedData = new ArrayList<>();
        String[] lines = markdownContent.split("\n");
        String currentCategory = "";
        List<String> currentHeaders = new ArrayList<>();
        List<String> currentInternalKeys = new ArrayList<>();

        boolean inTable = false;

        for (String line : lines) {
            String trimmedLine = line.trim();

            if (trimmedLine.startsWith("## ")) {
                currentCategory = trimmedLine.substring(3).trim();
                inTable = false;
                currentHeaders = new ArrayList<>();
                currentInternalKeys = new ArrayList<>();
            } else if (trimmedLine.startsWith("|") && trimmedLine.endsWith("|")) {
                // 确保至少有两列且内容不为空的行才进行处理
                String[] cells = trimmedLine.substring(1, trimmedLine.length() - 1).split("\\|", -1);
                cells = cleanCells(cells);

                if (cells.length > 0) {
                    if (trimmedLine.contains("---")) {
                        // 这是分隔线，设置 inTable 为 true 表示接下来是数据行
                        inTable = true;
                    } else if (!inTable && currentHeaders.isEmpty()) {
                        // 这是表头行
                        for (String header : cells) {
                            currentHeaders.add(header);
                            currentInternalKeys.add(COLUMN_KEY_MAP.getOrDefault(header, header.toLowerCase()));
                        }
                        inTable = true; // 即使是表头也算进入表格模式
                    } else if (inTable) {
                        // 这是数据行
                        HashMap<String, String> row = new HashMap<>();
                        row.put("category", currentCategory);

                        for (int i = 0; i < cells.length && i < currentInternalKeys.size(); i++) {
                            row.put(currentInternalKeys.get(i), cells[i]);
                        }
                        parsedData.add(row);
                    }
                }
            }
        }
        return parsedData;
    }

    /**
     * 清理单元格内容
     *
     * @param cells 单元格内容
     * @return 清理后的单元格内容
     */
    private static String[] cleanCells(String[] cells) {
        List<String> cleaned = new ArrayList<>();
        for (String cell : cells) {
            cleaned.add(cell.trim());
        }
        return cleaned.toArray(new String[0]);
    }

    /**
     * 将列表分割成指定大小的批次
     *
     * @param <T>       列表元素类型
     * @param list      原始列表
     * @param batchSize 批次大小
     * @return 分批后的列表集合
     */
    private static <T> List<List<T>> splitList(List<T> list, int batchSize) {
        if (list == null || list.isEmpty() || batchSize <= 0) {
            return Collections.emptyList();
        }

        List<List<T>> batches = new ArrayList<>();
        int total = list.size();

        for (int i = 0; i < total; i += batchSize) {
            int end = Math.min(i + batchSize, total);
            batches.add(list.subList(i, end));
        }

        return batches;
    }

    /**
     * 构建精简版的匹配提示词
     *
     * @param devices          设备列表
     * @param materialListData 物料清单
     * @return 精简的提示词
     */
    private static String buildSimplifiedPrompt(List<Map<String, Object>> devices, List<Map<String, Object>> materialListData) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请分析设备配置列表，为每个设备从物料清单中选择最匹配的方案。\n\n");

        // 添加目标设备信息（精简版）
        prompt.append("【设备列表】\n");
        for (int i = 0; i < devices.size(); i++) {
            Map<String, Object> device = devices.get(i);
            prompt.append(String.format(
                "设备%d：名称：%s；配置：%s；备注：%s\n",
                i + 1,
                device.get("deviceName"),
                device.get("config"),
                device.get("remark")
            ));
        }

        // 添加物料清单（精简版）
        prompt.append("\n【物料清单】\n");
        for (int i = 0; i < materialListData.size(); i++) {
            Map<String, Object> part = materialListData.get(i);
            prompt.append(String.format(
                "物料%d：名称：%s；配置：%s\n",
                i + 1,
                part.get("物料名称"),
                part.get("详细配置")
            ));
        }

        // 添加匹配规则（精简版）
        // prompt.append("\n请为每个设备找出最匹配的物料，返回JSON格式结果。结果格式示例：");
        // prompt.append("\n[{\"deviceIndex\":0,\"matchedIndex\":5,\"similarityScore\":\"0.92\"},");
        // prompt.append("\n{\"deviceIndex\":1,\"matchedIndex\":2,\"similarityScore\":\"0.85\"}]");
        // prompt.append("\n\n每个对象包含：设备索引(deviceIndex)、匹配物料索引(matchedIndex)、相似度(similarityScore)");
        // prompt.append("\n注意：索引从0开始计数。");
        prompt.append(ConstantsPrompt.DOCUMNT_MATCH_PROMPT);

        return prompt.toString();
    }

    /**
     * 处理单批设备匹配
     *
     * @param devices          设备批次
     * @param materialListData 物料清单
     * @param deviceOffset     设备批次在原列表中的偏移量
     * @return 匹配结果
     */
    private List<JSONObject> matchDeviceBatch(List<Map<String, Object>> devices,
                                                     List<Map<String, Object>> materialListData,
                                                     int deviceOffset) {
        try {
            // 构建精简prompt
            String prompt = buildSimplifiedPrompt(devices, materialListData);

            // 调用Qwen API
            QwenApiClient client = new QwenApiClient(url, apiKey, model);
            QwenResponse response = client.analyzeDocument(prompt, "你是设备配置匹配专家");

            if (!response.isSuccess()) {
                System.err.println("批次API调用失败: " + response.getRawResponse());
                return Collections.emptyList();
            }

            // 解析匹配结果
            String analysisResult = response.getAnalysisResult();
            // 清理JSON字符串
            int startIndex = analysisResult.indexOf('[');
            int endIndex = analysisResult.lastIndexOf(']') + 1;
            if (startIndex >= 0 && endIndex > startIndex) {
                analysisResult = analysisResult.substring(startIndex, endIndex);
            } else {
                System.err.println("批次匹配未找到有效的JSON数组");
                return Collections.emptyList();
            }

            List<JSONObject> matchResults = JSON.parseArray(analysisResult, JSONObject.class);

            // 调整设备索引，加上偏移量
            if (deviceOffset > 0 && matchResults != null) {
                for (JSONObject result : matchResults) {
                    int originalIndex = result.getIntValue("deviceIndex");
                    result.put("deviceIndex", originalIndex + deviceOffset);
                }
            }

            return matchResults != null ? matchResults : Collections.emptyList();

        } catch (Exception e) {
            System.err.println("处理批次匹配时出错: " + e.getMessage());
            e.printStackTrace();
            return Collections.emptyList();
        }
    }

    /**
     * 批量处理文档匹配（优化版）
     *
     * @param analysisJson     文档分析结果
     * @param materialListData 物料清单数据
     * @param batchSize        批次大小
     * @return 匹配后的文档分析结果
     */
    public String matchDocumentBatch(String analysisJson,
                                            List<Map<String, Object>> materialListData,
                                            int batchSize) {
        try {
            // 1. 解析输入数据
            List<Map<String, Object>> analysisResults =
                JSON.parseObject(analysisJson, new TypeReference<List<Map<String, Object>>>() {
                });

            // 2. 验证物料清单数据
            if (materialListData == null || materialListData.isEmpty()) {
                System.err.println("警告: 物料清单中没有有效数据");
                return null;
            }

            // 3. 分批处理设备列表
            List<List<Map<String, Object>>> deviceBatches = splitList(analysisResults, batchSize);
            // 使用Map来存储每个设备的最佳匹配，键为设备索引，值为匹配结果
            Map<Integer, JSONObject> bestMatchResults = new HashMap<>();

            // 4. 逐批处理匹配
            for (int i = 0; i < deviceBatches.size(); i++) {
                List<Map<String, Object>> batch = deviceBatches.get(i);
                int deviceOffset = i * batchSize;

                System.out.println("处理第" + (i + 1) + "批设备，共" + batch.size() + "个设备");
                List<JSONObject> batchResults = matchDeviceBatch(batch, materialListData, deviceOffset);

                // 只保留每个设备的最佳匹配（相似度最高）
                for (JSONObject result : batchResults) {
                    int deviceIndex = result.getIntValue("deviceIndex");
                    var similarityScore = result.getString("similarityScore");
                    if (StringUtils.isBlank(similarityScore)) {
                        continue;
                    }
                    double similarity = Double.parseDouble(similarityScore);

                    // 检查是否已有该设备的匹配结果
                    if (!bestMatchResults.containsKey(deviceIndex) ||
                        Double.parseDouble(bestMatchResults.get(deviceIndex).getString("similarityScore")) < similarity) {
                        // 如果没有或者新的相似度更高，则更新
                        bestMatchResults.put(deviceIndex, result);
                    }
                }
            }

            // 转换Map为List
            List<JSONObject> allMatchResults = new ArrayList<>(bestMatchResults.values());

            // 5. 合并并更新匹配结果
            for (JSONObject matchResult : allMatchResults) {
                int deviceIndex = matchResult.getIntValue("deviceIndex");
                int matchedIndex = matchResult.getIntValue("matchedIndex");

                if (deviceIndex >= 0 && deviceIndex < analysisResults.size() &&
                    matchedIndex >= 0 && matchedIndex < materialListData.size()) {

                    Map<String, Object> device = analysisResults.get(deviceIndex);
                    Map<String, Object> matchedPart = materialListData.get(matchedIndex);

                    // 添加匹配信息，保持原有数据不变
                    device.put("materialId", matchedPart.get("物料ID")); // 添加物料ID
                    device.put("price", matchedPart.get("单价"));
                    device.put("xconfig", matchedPart.get("详细配置"));
                    device.put("similarityScore", matchResult.getString("similarityScore"));
                    // 添加单位字段的复制
                    device.put("unit", matchedPart.get("计量单位"));

                    // 添加供应商名称作为生产厂家
                    Object supplierName = matchedPart.get("供应商名称");
                    if (supplierName != null && supplierName.toString().trim().length() > 0) {
                        device.put("manufacturer", supplierName.toString());
                    } else {
                        device.put("manufacturer", "国能日新"); // 默认值
                    }

                    // 添加品牌（模糊）作为产地
                    Object brandFuzzy = matchedPart.get("品牌（模糊）");
                    if (brandFuzzy != null && brandFuzzy.toString().trim().length() > 0) {
                        device.put("origin", brandFuzzy.toString());
                    } else {
                        device.put("origin", "中国"); // 默认值
                    }
                }
            }

            // 6. 对匹配结果进行去重
            System.out.println("匹配完成后，去重前设备数量: " + analysisResults.size());

            // 检查是否有重复设备
            Map<String, Long> deviceCount = analysisResults.stream()
                                                           .collect(Collectors.groupingBy(device ->
                                                                   String.valueOf(device.getOrDefault("deviceName", "")).trim(),
                                                               Collectors.counting()));

            long duplicateCount = deviceCount.entrySet().stream()
                                             .filter(entry -> entry.getValue() > 1)
                                             .count();

            if (duplicateCount > 0) {
                System.out.println("检测到 " + duplicateCount + " 个重复设备名称需要合并");
                deviceCount.entrySet().stream()
                           .filter(entry -> entry.getValue() > 1)
                           .forEach(entry -> {
                               System.out.println("设备 '" + entry.getKey() + "' 有 " + entry.getValue() + " 条记录");
                               // 打印每个重复设备的配置信息，便于调试
                               analysisResults.stream()
                                              .filter(device -> String.valueOf(device.getOrDefault("deviceName", "")).trim().equals(entry.getKey()))
                                              .forEach(device -> System.out.println("  - 配置: " + device.getOrDefault("config", "无配置")));
                           });
            }

            List<Map<String, Object>> dedupResults = removeDuplicateObjects(analysisResults);
            System.out.println("去重后设备数量: " + dedupResults.size());

            // 7. 返回更新后的JSON
            return JSON.toJSONString(dedupResults, SerializerFeature.WriteMapNullValue);

        } catch (Exception e) {
            System.err.println("批处理过程中出错: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 移除重复对象，保留信息最完整的一条，不再合并关键信息
     *
     * @param devices 原始设备列表
     * @return 去重后的设备列表
     */
    private static List<Map<String, Object>> removeDuplicateObjects(List<Map<String, Object>> devices) {
        // 如果列表为空或只有一个元素，直接返回
        if (devices == null || devices.size() <= 1) {
            return devices;
        }

        // 按设备名称分组（不再使用配置作为分组依据）
        Map<String, List<Map<String, Object>>> deviceGroups = devices.stream()
                                                                     .collect(Collectors.groupingBy(device -> {
                                                                         String deviceName = String.valueOf(device.getOrDefault("deviceName", "")).trim();
                                                                         return deviceName; // 只使用名称作为分组键
                                                                     }));

        // 对于每组重复设备，只保留信息最完整的一条记录
        List<Map<String, Object>> uniqueDevices = new ArrayList<>();
        for (List<Map<String, Object>> group : deviceGroups.values()) {
            if (group.size() == 1) {
                // 如果组内只有一个设备，直接添加
                uniqueDevices.add(group.get(0));
            } else {
                // 如果有多个，选择信息最完整的一条记录
                Map<String, Object> bestDevice = selectBestObject(group);
                uniqueDevices.add(bestDevice);
            }
        }

        return uniqueDevices;
    }

    /**
     * 合并多个设备对象的信息
     *
     * @param devices 需要合并的设备列表
     * @return 合并后的设备对象
     */
    private static Map<String, Object> mergeObjects(List<Map<String, Object>> devices) {
        // 选择信息最完整的设备作为基础
        Map<String, Object> baseDevice = selectBestObject(devices);
        Map<String, Object> mergedDevice = new HashMap<>(baseDevice);

        // 收集所有设备的配置信息
        Set<String> configs = new HashSet<>();
        Set<String> quantities = new HashSet<>();
        Set<String> remarks = new HashSet<>();

        for (Map<String, Object> device : devices) {
            // 合并配置信息
            Object configObj = device.get("config");
            if (configObj != null) {
                String config = String.valueOf(configObj).trim();
                if (!config.isEmpty() && !config.equals("无明确配置要求")) {
                    configs.add(config);
                }
            }

            // 合并数量信息
            Object quantityObj = device.get("quantity");
            if (quantityObj != null) {
                String quantity = String.valueOf(quantityObj).trim();
                if (!quantity.isEmpty() && !quantity.equals("未明确")) {
                    quantities.add(quantity);
                }
            }

            // 合并备注信息
            Object remarkObj = device.get("remark");
            if (remarkObj != null) {
                String remark = String.valueOf(remarkObj).trim();
                if (!remark.isEmpty()) {
                    remarks.add(remark);
                }
            }
        }

        // 设置合并后的配置信息
        if (!configs.isEmpty()) {
            mergedDevice.put("config", String.join("; ", configs));
        }

        // 设置合并后的数量信息
        if (!quantities.isEmpty()) {
            // 尝试将数量转换为数字并累加
            try {
                int totalQuantity = 0;
                boolean allNumeric = true;

                for (String quantity : quantities) {
                    try {
                        totalQuantity += Integer.parseInt(quantity);
                    } catch (NumberFormatException e) {
                        allNumeric = false;
                        break;
                    }
                }

                if (allNumeric) {
                    mergedDevice.put("quantity", String.valueOf(totalQuantity));
                } else {
                    mergedDevice.put("quantity", String.join("; ", quantities));
                }
            } catch (Exception e) {
                // 如果转换失败，直接合并字符串
                mergedDevice.put("quantity", String.join("; ", quantities));
            }
        }

        // 设置合并后的备注信息
        if (!remarks.isEmpty()) {
            mergedDevice.put("remark", String.join("; ", remarks));
        }

        return mergedDevice;
    }

    /**
     * 从重复设备组中选择信息最完整的设备
     *
     * @param devices 重复设备组
     * @return 信息最完整的设备
     */
    private static Map<String, Object> selectBestObject(List<Map<String, Object>> devices) {
        Map<String, Object> bestDevice = devices.get(0);
        int maxScore = calculateObjectCompleteness(bestDevice);

        for (int i = 1; i < devices.size(); i++) {
            Map<String, Object> currentDevice = devices.get(i);
            int currentScore = calculateObjectCompleteness(currentDevice);

            if (currentScore > maxScore) {
                maxScore = currentScore;
                bestDevice = currentDevice;
            }
        }

        return bestDevice;
    }

    /**
     * 计算设备信息的完整度分数
     *
     * @param device 设备信息
     * @return 完整度分数，分数越高表示信息越完整
     */
    private static int calculateObjectCompleteness(Map<String, Object> device) {
        int score = 0;

        // 检查各个关键字段是否存在且非空
        String[] keyFields = {"deviceName", "config", "quantity", "remark", "category", "materialId", "price", "xconfig", "unit", "manufacturer", "origin"};
        for (String field : keyFields) {
            Object value = device.get(field);
            if (value != null && !value.toString().trim().isEmpty()) {
                score++;
                // 字段内容越长，分数越高（表示信息越详细）
                score += Math.min(5, value.toString().length() / 10);
            }
        }

        // 如果有相似度分数，加分 (具有相似度说明已经做过匹配)
        Object similarityScore = device.get("similarityScore");
        if (similarityScore != null && !similarityScore.toString().trim().isEmpty()) {
            try {
                double similarity = Double.parseDouble(similarityScore.toString());
                score += similarity * 10; // 相似度越高，加分越多
            } catch (NumberFormatException e) {
                // 解析失败不加分
            }
        }

        return score;
    }

    /**
     * 匹配文档分析结果与物料清单
     *
     * @param analysisJson     文档分析结果
     * @param materialListData 物料清单数据，格式为List<Map<String, Object>>，每个Map包含：
     *                         - 物料名称 - 详细配置
     *                         - 单价 - 其他信息（供应商名称、品牌等）
     * @return 匹配后的文档分析结果
     */
    public  String matchDocument(String analysisJson, List<Map<String, Object>> materialListData) {
        // 记录原始数据数量
        try {
            List<Map<String, Object>> originalData = JSON.parseObject(analysisJson,
                new TypeReference<List<Map<String, Object>>>() {
                });
            System.out.println("原始数据共有 " + originalData.size() + " 条记录");

            // 检查原始数据中是否有重复
            Map<String, Long> deviceCount = originalData.stream()
                                                        .collect(Collectors.groupingBy(device -> {
                                                            String deviceName = String.valueOf(device.getOrDefault("deviceName", ""));
                                                            String config = String.valueOf(device.getOrDefault("config", ""));
                                                            return deviceName + "|" + config;
                                                        }, Collectors.counting()));

            long duplicateCount = deviceCount.values().stream().filter(count -> count > 1).count();
            if (duplicateCount > 0) {
                System.out.println("检测到原始数据中有 " + duplicateCount + " 组重复记录需要处理");
            } else {
                System.out.println("原始数据中没有检测到重复记录");
            }
        } catch (Exception e) {
            System.err.println("分析原始数据时出错: " + e.getMessage());
        }

        // 默认批次大小为10
        String result = matchDocumentBatch(analysisJson, materialListData, 20);

        // 记录去重后数据数量
        try {
            if (result != null) {
                List<Map<String, Object>> finalData = JSON.parseObject(result,
                    new TypeReference<List<Map<String, Object>>>() {
                    });
                System.out.println("去重后数据共有 " + finalData.size() + " 条记录");

                // 按类别分组统计
                Map<String, Long> categoryCount = finalData.stream()
                                                           .collect(Collectors.groupingBy(item ->
                                                                   String.valueOf(item.getOrDefault("category", "未分类")),
                                                               Collectors.counting()));

                System.out.println("去重后数据分类统计:");
                categoryCount.forEach((category, count) ->
                    System.out.println("- " + category + ": " + count + " 条记录"));
            }
        } catch (Exception e) {
            System.err.println("分析去重后数据时出错: " + e.getMessage());
        }

        return result;
    }

    public static void main(String[] args) {
        // String analysisJson = "[{\"quantity\":\"2\",\"remark\":\"满足项目当地电网要求\",\"category\":\"硬件部分\",\"deviceName\":\"功率预测服务器\",\"config\":\"CPU：四核，主频：3.1GHZ；内存：8GB；硬盘：1TB；网络：4个自适10/100/1000 Mps千兆网口；电源：双电源\"},{\"quantity\":\"1\",\"remark\":\"满足项目当地电网要求\",\"category\":\"硬件部分\",\"deviceName\":\"气象服务器\",\"config\":\"CPU：四核，主频：3.1GHZ；内存：8GB；硬盘：1TB；网络：4个自适10/100/1000 Mps千兆网口；电源：双电源\"},{\"quantity\":\"2\",\"remark\":\"满足项目当地电网要求\",\"category\":\"硬件部分\",\"deviceName\":\"PC工作站\",\"config\":\"CPU：双核；主频：2.8GHZ；内存：8G；硬盘：1T；网口：2个；22寸液晶显示器\"},{\"quantity\":\"1\",\"remark\":\"未指定供应商\",\"category\":\"硬件部分\",\"deviceName\":\"采集服务器\",\"config\":\"支持网口、串口及USB通讯，内置Linux系统，支持IEC104，MODBUS等多种传输协议\"},{\"quantity\":\"2\",\"remark\":\"未指定供应商\",\"category\":\"硬件部分\",\"deviceName\":\"KVM及显示器\",\"config\":\"8个VGA口，19寸液晶显示器；键鼠：1套\"},{\"quantity\":\"1\",\"remark\":\"南瑞、科东、珠海鸿瑞（满足版本要求）\",\"category\":\"硬件部分\",\"deviceName\":\"网络安全隔离设备（反向型）\",\"config\":\"内网口2个、外网口1个，隔离装置内外两个网卡在装置内部是非网络连接，且只允许数据单向传输\"},{\"quantity\":\"1\",\"remark\":\"南瑞、科东、珠海鸿瑞（满足版本要求）\",\"category\":\"硬件部分\",\"deviceName\":\"网络安全隔离设备（正向型）\",\"config\":\"内网口2个、外网口1个，隔离装置内外两个网卡在装置内部是非网络连接，且只允许数据单向传输\"},{\"quantity\":\"1\",\"remark\":\"未指定供应商\",\"category\":\"硬件部分\",\"deviceName\":\"电力屏柜及附件\",\"config\":\"尺寸：800(W)×1000(D)×2260(H)；颜色：GY09冰灰橘纹；配套机柜电源/接地线缆\"},{\"quantity\":\"1\",\"remark\":\"未指定供应商\",\"category\":\"硬件部分\",\"deviceName\":\"交换机\",\"config\":\"三层交换机，不少于24个，百兆-千兆以太网口；电源：AC220（UPS）冗余配置\"},{\"quantity\":\"1\",\"remark\":\"满足当地电网要求\",\"category\":\"硬件部分\",\"deviceName\":\"内网防火墙\",\"config\":\"4GE+2Combo,可扩展千兆电口/千兆光口/万兆光口，双电源\"},{\"quantity\":\"1\",\"remark\":\"满足当地电网要求\",\"category\":\"硬件部分\",\"deviceName\":\"外网防火墙\",\"config\":\"4GE+2Combo,可扩展千兆电口/千兆光口/万兆光口，双电源\"},{\"quantity\":\"1\",\"remark\":\"含基础、塔安装材料，测风塔含基础实施\",\"category\":\"硬件部分\",\"deviceName\":\"测风塔塔体\",\"config\":\"160米热镀锌三柱拉线、抗覆冰必须满足项目当地气候条件；加强加固型\"},{\"quantity\":\"1\",\"remark\":\"测风塔160m、150m、130m、110m、90m、70m、50m、30m\",\"category\":\"硬件部分\",\"deviceName\":\"测风设备\",\"config\":\"8风速、8风向、2温湿度、1大气压传感器及配套线缆\"},{\"quantity\":\"1\",\"remark\":\"未指定供应商\",\"category\":\"硬件部分\",\"deviceName\":\"数据采集系统\",\"config\":\"采集风向、风速、温湿度、大气压、测风塔等数据\"},{\"quantity\":\"1\",\"remark\":\"未指定供应商\",\"category\":\"硬件部分\",\"deviceName\":\"供电电源\",\"config\":\"1套80W太阳能电池，充电控制器，4块12V/24AH 胶体电池\"},{\"quantity\":\"1\",\"remark\":\"满足项目需要\",\"category\":\"硬件部分\",\"deviceName\":\"传输设备\",\"config\":\"光纤地埋（铺设距离按照项目现场实际需求配置，含施工）\"},{\"quantity\":\"5\",\"remark\":\"功率预测、气象数据、工作站需要\",\"category\":\"软件部分\",\"deviceName\":\"操作系统软件\",\"config\":\"国产操作系统，满足电网需求\"},{\"quantity\":\"4\",\"remark\":\"功率预测服务器、工作站需要\",\"category\":\"软件部分\",\"deviceName\":\"网安探针软件\",\"config\":\"国产操作系统的配套探针软件，满足电网需求\"},{\"quantity\":\"1\",\"remark\":\"未指定供应商\",\"category\":\"软件部分\",\"deviceName\":\"预测系统软件平台\",\"config\":\"风电功率预测系统软件平台；操作系统：国产安全操作系统\"},{\"quantity\":\"1\",\"remark\":\"未指定供应商\",\"category\":\"软件部分\",\"deviceName\":\"中长期预测建模\",\"config\":\"0-240h中长期功率预测模型开发\"},{\"quantity\":\"1\",\"remark\":\"未指定供应商\",\"category\":\"软件部分\",\"deviceName\":\"短期预测建模\",\"config\":\"0-72h短期功率预测模型开发\"},{\"quantity\":\"1\",\"remark\":\"未指定供应商\",\"category\":\"软件部分\",\"deviceName\":\"超短期预测建模\",\"config\":\"风电场0-4小时，超短期功率预测模型开发\"},{\"quantity\":\"1\",\"remark\":\"未指定供应商\",\"category\":\"软件部分\",\"deviceName\":\"通信应用接口开发\",\"config\":\"风电场风机与系统通信接口开发\"},{\"quantity\":\"5年\",\"remark\":\"未指定供应商\",\"category\":\"软件部分\",\"deviceName\":\"技术服务\",\"config\":\"模型再训练、数据备份、系统升级维护等服务\"},{\"quantity\":\"5年\",\"remark\":\"至少包含中国气象预报源\",\"category\":\"软件部分\",\"deviceName\":\"气象服务\",\"config\":\"风电功率预测系统数值天气服务\"}]";
        List<HashMap<String, String>> analysisList = new DocumentAnalysisMain().analysisDocument("d://询价清单-MySE6.25-200风力发电机组风功率预测系统清单（河南许继巨鹿东能100MW项目）.docx");
        String analysisJson = JSON.toJSONString(analysisList);
        System.out.println(analysisJson);

        // 测试去重功能
        System.out.println("\n======= 测试去重功能 =======");

        // 创建一个包含重复项的测试数据
        String testJsonWithDuplicates = "[" +
            "{\"quantity\":\"2\",\"remark\":\"满足项目当地电网要求\",\"category\":\"硬件部分\",\"deviceName\":\"功率预测服务器\",\"config\":\"CPU：四核，主频：3.1GHZ；内存：8GB；硬盘：1TB；网络：4个自适10/100/1000 Mps千兆网口；电源：双电源\"}," +
            "{\"quantity\":\"2\",\"remark\":\"满足项目当地电网要求\",\"category\":\"硬件部分\",\"deviceName\":\"功率预测服务器\",\"config\":\"CPU：四核，主频：3.1GHZ；内存：8GB；硬盘：1TB；网络：4个自适10/100/1000 Mps千兆网口；电源：双电源\"}," +
            "{\"quantity\":\"1\",\"remark\":\"满足项目当地电网要求\",\"category\":\"硬件部分\",\"deviceName\":\"气象服务器\",\"config\":\"CPU：四核，主频：3.1GHZ；内存：8GB；硬盘：1TB；网络：4个自适10/100/1000 Mps千兆网口；电源：双电源\"}," +
            "{\"quantity\":\"1\",\"remark\":\"满足项目当地电网要求 细节更多\",\"category\":\"硬件部分\",\"deviceName\":\"气象服务器\",\"config\":\"CPU：四核，主频：3.1GHZ；内存：8GB；硬盘：1TB；网络：4个自适10/100/1000 Mps千兆网口；电源：双电源\"}" +
            "]";

        // 直接测试removeDuplicateDevices方法
        List<HashMap<String, String>> duplicatesList = JSON.parseObject(testJsonWithDuplicates,
            new TypeReference<List<HashMap<String, String>>>() {
            });

        System.out.println("去重前数据: " + duplicatesList.size() + " 条记录");
        for (HashMap<String, String> item : duplicatesList) {
            System.out.println("- 设备: " + item.get("deviceName") + ", 备注: " + item.get("remark"));
        }

        List<HashMap<String, String>> dedupList = removeDuplicateDevices(duplicatesList);

        System.out.println("\n去重后数据: " + dedupList.size() + " 条记录");
        for (HashMap<String, String> item : dedupList) {
            System.out.println("- 设备: " + item.get("deviceName") + ", 备注: " + item.get("remark"));
        }

        // String materialList = "d://物料清单20250604.xlsx";
        // 此处的 main 方法仅用于测试，无需修改
        // String results = matchDocument(analysisJson,materialList);
        // System.out.println(results);
    }
}

