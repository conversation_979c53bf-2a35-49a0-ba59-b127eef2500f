package com.sprixin.sa.admin.util.qwen;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

@Slf4j
public class DocumentProcessor {
    
    private static final int MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
    private static final String[] SUPPORTED_EXTENSIONS = {"doc", "docx", "pdf", "txt"};
    
    /**
     * 根据文件类型提取文本内容
     */
    public static String extractTextFromFile(String filePath) throws IOException {
        if (filePath == null || filePath.trim().isEmpty()) {
            throw new IllegalArgumentException("文件路径不能为空");
        }
        
        // 标准化文件路径
        filePath = normalizePath(filePath);
        
        // 如果是URL，先下载到临时文件
        if (isUrl(filePath)) {
            return handleRemoteFile(filePath);
        }
        
        return extractTextFromLocalFile(filePath);
    }
    
    /**
     * 判断是否为URL
     */
    private static boolean isUrl(String path) {
        return path.startsWith("http://") || path.startsWith("https://");
    }
    
    /**
     * 处理远程文件
     */
    private static String handleRemoteFile(String url) throws IOException {
        Path tempFile = null;
        try {
            // 创建临时文件
            tempFile = Files.createTempFile("document_", "." + getFileExtension(url));
            
            // 下载文件
            try (InputStream in = new URL(url).openStream()) {
                // 检查文件大小
                long size = in.available();
                if (size > MAX_FILE_SIZE) {
                    throw new IOException("文件大小超过限制（最大50MB）");
                }
                
                // 下载文件
                Files.copy(in, tempFile, StandardCopyOption.REPLACE_EXISTING);
            }
            
            return extractTextFromLocalFile(tempFile.toString());
        } catch (IOException e) {
            log.error("处理远程文件失败: {}", url, e);
            throw new IOException("无法访问文件: " + url, e);
        } finally {
            // 确保临时文件被删除
            if (tempFile != null) {
                try {
                    Files.deleteIfExists(tempFile);
                } catch (IOException e) {
                    log.warn("删除临时文件失败: {}", tempFile, e);
                }
            }
        }
    }
    
    /**
     * 从本地文件提取文本
     */
    private static String extractTextFromLocalFile(String filePath) throws IOException {
        // 检查文件是否存在
        File file = new File(filePath);
        if (!file.exists()) {
            throw new FileNotFoundException("文件不存在: " + filePath);
        }
        
        // 检查文件大小
        if (file.length() > MAX_FILE_SIZE) {
            throw new IOException("文件大小超过限制（最大50MB）");
        }
        
        String extension = getFileExtension(filePath).toLowerCase();
        
        // 检查文件类型是否支持
        if (!isSupportedExtension(extension)) {
            throw new IllegalArgumentException("不支持的文件类型: " + extension);
        }
        
        // 对于Word文档，先检测实际文件格式
        if (extension.equals("docx") || extension.equals("doc")) {
            return extractTextFromWord(filePath);
        }
        
        switch (extension) {
            case "pdf":
                return extractTextFromPDF(filePath);
            case "txt":
                return extractTextFromTxt(filePath);
            default:
                throw new IllegalArgumentException("不支持的文件类型: " + extension);
        }
    }
    
    /**
     * 检测Word文档格式并提取文本
     */
    private static String extractTextFromWord(String filePath) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            // 尝试检测文件格式
            if (isDocxFormat(fis)) {
                return extractTextFromDocx(filePath);
            } else {
                return extractTextFromDoc(filePath);
            }
        }
    }
    
    /**
     * 检测文件是否为DOCX格式
     */
    private static boolean isDocxFormat(FileInputStream fis) throws IOException {
        try {
            // 尝试作为DOCX文件打开
            XWPFDocument document = new XWPFDocument(fis);
            document.close();
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 从PDF文件提取文本
     */
    private static String extractTextFromPDF(String filePath) throws IOException {
        try (PDDocument document = PDDocument.load(new File(filePath))) {
            PDFTextStripper stripper = new PDFTextStripper();
            return stripper.getText(document);
        }
    }
    
    /**
     * 从Word文档提取文本
     */
    private static String extractTextFromDocx(String filePath) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath);
             XWPFDocument document = new XWPFDocument(fis);
             XWPFWordExtractor extractor = new XWPFWordExtractor(document)) {
            return extractor.getText();
        }
    }
    
    /**
     * 从旧版Word文档(.doc)提取文本
     */
    private static String extractTextFromDoc(String filePath) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath);
             HWPFDocument document = new HWPFDocument(fis);
             WordExtractor extractor = new WordExtractor(document)) {
            return extractor.getText();
        }
    }
    
    /**
     * 从文本文件提取内容
     */
    private static String extractTextFromTxt(String filePath) throws IOException {
        return new String(Files.readAllBytes(Paths.get(filePath)), StandardCharsets.UTF_8);
    }
    
    /**
     * 获取文件扩展名
     */
    private static String getFileExtension(String filePath) {
        int lastDotIndex = filePath.lastIndexOf('.');
        return lastDotIndex > 0 ? filePath.substring(lastDotIndex + 1) : "";
    }
    
    /**
     * 检查文件扩展名是否支持
     */
    private static boolean isSupportedExtension(String extension) {
        for (String supported : SUPPORTED_EXTENSIONS) {
            if (supported.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 标准化文件路径
     */
    private static String normalizePath(String path) {
        // 替换Windows路径分隔符
        return path.replace('\\', '/');
    }
    
    /**
     * 文本分块处理（处理超长文档）
     */
    public static String[] splitTextIntoChunks(String text, int maxChunkSize) {
        if (text == null || text.isEmpty()) {
            return new String[0];
        }
        
        if (text.length() <= maxChunkSize) {
            return new String[]{text};
        }
        
        String[] sentences = text.split("[。！？\\n]");
        StringBuilder currentChunk = new StringBuilder();
        java.util.List<String> chunks = new java.util.ArrayList<>();
        
        for (String sentence : sentences) {
            if (currentChunk.length() + sentence.length() > maxChunkSize) {
                if (currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString());
                    currentChunk = new StringBuilder();
                }
            }
            currentChunk.append(sentence).append("。");
        }
        
        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString());
        }
        
        return chunks.toArray(new String[0]);
    }
}
