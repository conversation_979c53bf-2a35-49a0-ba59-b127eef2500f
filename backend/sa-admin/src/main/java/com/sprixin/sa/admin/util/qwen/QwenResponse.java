package com.sprixin.sa.admin.util.qwen;

public class QwenResponse {
    private final int statusCode;
    private final String rawResponse;
    private final String analysisResult;
    
    public QwenResponse(int statusCode, String rawResponse, String analysisResult) {
        this.statusCode = statusCode;
        this.rawResponse = rawResponse;
        this.analysisResult = analysisResult;
    }
    
    public int getStatusCode() {
        return statusCode;
    }
    
    public String getRawResponse() {
        return rawResponse;
    }
    
    public String getAnalysisResult() {
        return analysisResult;
    }
    
    public boolean isSuccess() {
        return statusCode >= 200 && statusCode < 300;
    }
    
    @Override
    public String toString() {
        return String.format("QwenResponse{statusCode=%d, analysisResult='%s'}", 
            statusCode, analysisResult);
    }
}
