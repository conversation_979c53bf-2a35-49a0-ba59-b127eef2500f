-- 创建技术方案表
CREATE TABLE IF NOT EXISTS `tech_solution` (
  `id`  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `content`  longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '内容',
  `prompt`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '提示词',
  `api_key`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'API Key (加密存储)',
  `create_time`  datetime NOT NULL COMMENT '创建时间',
  `update_time`  datetime NOT NULL COMMENT '更新时间',
  `creator_id`  bigint(20) NOT NULL COMMENT '创建人ID',
  `creator_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人姓名',
  `status`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'COMPLETED' COMMENT '状态：PROCESSING-处理中, COMPLETED-已完成, FAILED-失败',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='技术方案表' AUTO_INCREMENT=1;

-- 创建技术方案文件表
CREATE TABLE IF NOT EXISTS `tech_solution_file` (
  `id`  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `solution_id`  bigint(20) NOT NULL COMMENT '关联技术方案ID',
  `file_id`  bigint(20) NOT NULL COMMENT '文件ID',
  `file_name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件名',
  `file_type`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件类型',
  `file_size`  bigint(20) NULL DEFAULT NULL COMMENT '文件大小(字节)',
  `file_url`  varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件URL',
  `create_time`  datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  INDEX `idx_solution_id` (`solution_id`) USING BTREE,
  INDEX `idx_file_id` (`file_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='技术方案文件表' AUTO_INCREMENT=1; 