-- 知识库文件夹表
CREATE TABLE IF NOT EXISTS `t_knowledge_folder` (
  `folder_id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件夹ID',
  `folder_name` varchar(100) NOT NULL COMMENT '文件夹名称',
  `parent_id` bigint NOT NULL DEFAULT 0 COMMENT '父文件夹ID',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序号',
  `description` varchar(500) DEFAULT NULL COMMENT '文件夹描述',
  `creator_id` bigint DEFAULT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) DEFAULT NULL COMMENT '创建人名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`folder_id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库文件夹';

-- 知识库文件表
CREATE TABLE IF NOT EXISTS `t_knowledge_file` (
  `file_id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `folder_id` bigint NOT NULL COMMENT '所属文件夹ID',
  `file_name` varchar(200) NOT NULL COMMENT '文件名称',
  `file_key` varchar(100) NOT NULL COMMENT '文件唯一标识',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型',
  `description` varchar(500) DEFAULT NULL COMMENT '文件描述',
  `tags` varchar(500) DEFAULT NULL COMMENT '文件标签',
  `creator_id` bigint DEFAULT NULL COMMENT '创建人ID',
  `creator_name` varchar(50) DEFAULT NULL COMMENT '创建人名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`file_id`),
  KEY `idx_folder_id` (`folder_id`),
  KEY `idx_file_key` (`file_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='知识库文件';

-- 初始化根文件夹
INSERT INTO `t_knowledge_folder` (`folder_name`, `parent_id`, `sort`, `description`) 
VALUES ('知识库', 0, 0, '知识库根目录'); 