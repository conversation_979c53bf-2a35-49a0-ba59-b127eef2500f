<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.sa.admin.module.business.baojia.dao.BaojiaContentDao">

    <select id="queryContent" resultType="com.sprixin.sa.admin.module.business.baojia.domain.vo.BaojiaContentVO">
        SELECT
            id,
            user_id,
            name,
            business_name,
            product_name,
            content_type,
            generate_type,
            disabled_flag,
            deleted_flag,
            create_user_id,
            create_user_name,
            create_time,
            update_time
        FROM y_baojia_content
        <where>
            <if test="queryForm.keyword != null and queryForm.keyword != ''">
                AND (
                    INSTR(name,#{queryForm.keyword})
                    OR INSTR(business_name,#{queryForm.keyword})
                    OR INSTR(product_name,#{queryForm.keyword})
                )
            </if>
            <if test="queryForm.contentType != null">
                AND content_type = #{queryForm.contentType}
            </if>
            <if test="queryForm.generateType != null">
                AND generate_type = #{queryForm.generateType}
            </if>
            <if test="queryForm.userId != null">
                AND user_id = #{queryForm.userId}
            </if>
            <if test="queryForm.disabledFlag != null">
                AND disabled_flag = #{queryForm.disabledFlag}
            </if>
            <if test="queryForm.deletedFlag != null">
                AND deleted_flag = #{queryForm.deletedFlag}
            </if>
            <if test="queryForm.productTypeId != null">
                AND EXISTS (
                    SELECT 1 FROM y_product_type pt 
                    WHERE pt.id = #{queryForm.productTypeId} 
                    AND INSTR(product_name, pt.name) > 0
                )
            </if>
            <if test="queryForm.productTypeName != null and queryForm.productTypeName != ''">
                AND INSTR(product_name, #{queryForm.productTypeName}) > 0
            </if>
        </where>
        ORDER BY update_time DESC
    </select>

</mapper> 