<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.sa.admin.module.business.orderstandard.dao.OrderStandardDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sprixin.sa.admin.module.business.orderstandard.domain.entity.OrderStandardEntity">
        <id column="serial_number" property="serialNumber" />
        <result column="device_name" property="deviceName" />
        <result column="order_standard" property="orderStandard" />
        <result column="created_at" property="createdAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        serial_number, device_name, order_standard, created_at
    </sql>

    <!-- 查询设备下单标准列表 -->
    <select id="queryOrderStandard" resultType="com.sprixin.sa.admin.module.business.orderstandard.domain.vo.OrderStandardVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            y_order_standard
        <where>
            <if test="queryForm.keyword != null and queryForm.keyword != ''">
                AND (
                    device_name LIKE CONCAT('%', #{queryForm.keyword}, '%')
                    OR order_standard LIKE CONCAT('%', #{queryForm.keyword}, '%')
                )
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 根据ID查询设备下单标准详情 -->
    <select id="getOrderStandardById" resultType="com.sprixin.sa.admin.module.business.orderstandard.domain.vo.OrderStandardVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            y_order_standard
        WHERE
            serial_number = #{id}
    </select>

    <!-- 根据查询条件统计总数 -->
    <select id="selectCountByForm" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM
            y_order_standard
        <where>
            <if test="queryForm.keyword != null and queryForm.keyword != ''">
                AND (
                    device_name LIKE CONCAT('%', #{queryForm.keyword}, '%')
                    OR order_standard LIKE CONCAT('%', #{queryForm.keyword}, '%')
                )
            </if>
        </where>
    </select>

</mapper> 