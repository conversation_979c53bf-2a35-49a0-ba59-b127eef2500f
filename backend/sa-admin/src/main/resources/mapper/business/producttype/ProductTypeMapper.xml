<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.sa.admin.module.business.producttype.dao.ProductTypeDao">

    <select id="listAll" resultType="com.sprixin.sa.admin.module.business.producttype.domain.vo.ProductTypeVO">
        SELECT t.*,
               parent.name as parentName
        FROM y_product_type t
        LEFT JOIN y_product_type parent ON t.parent_id = parent.id
        WHERE t.is_deleted = 0
        ORDER BY t.id ASC
    </select>

    <select id="countSubProductType" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM y_product_type
        WHERE parent_id = #{parentId}
        AND is_deleted = 0
    </select>

    <select id="selectProductTypeVO" resultType="com.sprixin.sa.admin.module.business.producttype.domain.vo.ProductTypeVO">
        SELECT t.*,
               parent.name as parentName
        FROM y_product_type t
        LEFT JOIN y_product_type parent ON t.parent_id = parent.id
        WHERE t.id = #{id}
        AND t.is_deleted = 0
    </select>

</mapper> 