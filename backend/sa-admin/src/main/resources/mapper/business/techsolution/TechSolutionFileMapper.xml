<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.sa.admin.module.business.techsolution.dao.TechSolutionFileDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sprixin.sa.admin.module.business.techsolution.domain.entity.TechSolutionFileEntity">
        <id column="id" property="id" />
        <result column="solution_id" property="solutionId" />
        <result column="file_id" property="fileId" />
        <result column="file_name" property="fileName" />
        <result column="file_type" property="fileType" />
        <result column="file_size" property="fileSize" />
        <result column="file_url" property="fileUrl" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, solution_id, file_id, file_name, file_type, file_size, file_url, create_time
    </sql>

</mapper> 