<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.sa.admin.module.business.techsolution.dao.TechSolutionDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sprixin.sa.admin.module.business.techsolution.domain.entity.TechSolutionEntity">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="prompt" property="prompt" />
        <result column="api_key" property="apiKey" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator_id" property="creatorId" />
        <result column="creator_name" property="creatorName" />
        <result column="status" property="status" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, content, prompt, api_key, create_time, update_time, creator_id, creator_name, status
    </sql>

</mapper> 