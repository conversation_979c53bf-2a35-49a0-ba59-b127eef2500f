<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.sa.admin.module.business.tender.dao.TenderDocumentDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sprixin.sa.admin.module.business.tender.domain.entity.TenderDocumentEntity">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="tender_type" property="tenderType" />
        <result column="project_name" property="projectName" />
        <result column="tender_no" property="tenderNo" />
        <result column="content" property="content" />
        <result column="prompt" property="prompt" />
        <result column="api_key" property="apiKey" />
        <result column="status" property="status" />
        <result column="deadline" property="deadline" />
        <result column="estimated_amount" property="estimatedAmount" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator_id" property="creatorId" />
        <result column="creator_name" property="creatorName" />
    </resultMap>

    <!-- 列表查询映射结果 -->
    <resultMap id="ListResultMap" type="com.sprixin.sa.admin.module.business.tender.domain.vo.TenderDocumentListVO">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="tender_type" property="tenderType" />
        <result column="project_name" property="projectName" />
        <result column="tender_no" property="tenderNo" />
        <result column="status" property="status" />
        <result column="deadline" property="deadline" />
        <result column="estimated_amount" property="estimatedAmount" />
        <result column="create_time" property="createTime" />
        <result column="creator_name" property="creatorName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, tender_type, project_name, tender_no, content, prompt, api_key, status, 
        deadline, estimated_amount, create_time, update_time, creator_id, creator_name
    </sql>

    <!-- 列表查询结果列 -->
    <sql id="List_Column_List">
        id, title, tender_type, project_name, tender_no, status, 
        deadline, estimated_amount, create_time, creator_name
    </sql>

    <!-- 分页查询标书列表 -->
    <select id="queryPage" resultMap="ListResultMap">
        SELECT
        <include refid="List_Column_List"/>
        FROM tender_document
        <where>
            <if test="queryDTO.title != null and queryDTO.title != ''">
                AND title LIKE CONCAT('%', #{queryDTO.title}, '%')
            </if>
            <if test="queryDTO.tenderType != null and queryDTO.tenderType != ''">
                AND tender_type = #{queryDTO.tenderType}
            </if>
            <if test="queryDTO.projectName != null and queryDTO.projectName != ''">
                AND project_name LIKE CONCAT('%', #{queryDTO.projectName}, '%')
            </if>
            <if test="queryDTO.tenderNo != null and queryDTO.tenderNo != ''">
                AND tender_no LIKE CONCAT('%', #{queryDTO.tenderNo}, '%')
            </if>
            <if test="queryDTO.status != null and queryDTO.status != ''">
                AND status = #{queryDTO.status}
            </if>
            <if test="queryDTO.creatorName != null and queryDTO.creatorName != ''">
                AND creator_name LIKE CONCAT('%', #{queryDTO.creatorName}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID查询标书详情 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tender_document
        WHERE id = #{id}
    </select>

    <!-- 批量删除标书 -->
    <delete id="batchDelete">
        DELETE FROM tender_document
        WHERE id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
