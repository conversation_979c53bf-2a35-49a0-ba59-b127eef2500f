<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.sa.admin.module.business.document.dao.DocumentAnalysisDao">
    
    <sql id="Base_Column_List">
        id, file_id, template_id, document_name AS file_name, analysis_result, analysis_prompt, create_time, update_time, creator_id, creator_name
    </sql>
    
    <select id="selectById" resultType="com.sprixin.sa.admin.module.business.document.domain.entity.DocumentAnalysisResult">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM y_document_analysis_result
        WHERE id = #{id}
    </select>
    
    <select id="selectByFileId" resultType="com.sprixin.sa.admin.module.business.document.domain.entity.DocumentAnalysisResult">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM y_document_analysis_result
        WHERE file_id = #{fileId}
    </select>
    
    <insert id="insert" parameterType="com.sprixin.sa.admin.module.business.document.domain.entity.DocumentAnalysisResult">
        INSERT INTO y_document_analysis_result (
            file_id, template_id, document_name, analysis_result, analysis_prompt, creator_id, creator_name
        ) VALUES (
            #{fileId}, #{templateId}, #{documentName}, #{analysisResult}, #{analysisPrompt}, #{creatorId}, #{creatorName}
        )
    </insert>
    
    <update id="updateAnalysisResult">
        UPDATE y_document_analysis_result
        SET analysis_result = #{analysisResult}
        WHERE id = #{id}
    </update>

    <!-- 查询分析结果列表 -->
    <select id="queryAnalysisResults" resultType="com.sprixin.sa.admin.module.business.document.domain.vo.DocumentAnalysisResultVO">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM y_document_analysis_result
        <where>
            <if test="queryForm != null and queryForm.keyword != null and queryForm.keyword != ''">
                AND (
                document_name LIKE CONCAT('%', #{queryForm.keyword}, '%')
                OR creator_name LIKE CONCAT('%', #{queryForm.keyword}, '%')
                )
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 查询分析结果总数 -->
    <select id="selectCountByForm" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM y_document_analysis_result
        <where>
            <if test="queryForm != null and queryForm.keyword != null and queryForm.keyword != ''">
                AND (
                document_name LIKE CONCAT('%', #{queryForm.keyword}, '%')
                OR creator_name LIKE CONCAT('%', #{queryForm.keyword}, '%')
                )
            </if>
        </where>
    </select>

    <!-- 根据ID删除 -->
    <delete id="deleteById">
        DELETE FROM y_document_analysis_result
        WHERE id = #{id}
    </delete>
    
</mapper> 