<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.sa.admin.module.business.equipmentprocurement.dao.EquipmentProcurementDao">

    <!-- 结果映射 -->
    <resultMap id="EquipmentProcurementVO" type="com.sprixin.sa.admin.module.business.equipmentprocurement.domain.vo.EquipmentProcurementVO">
        <id column="id" property="id"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="tender_config" property="tenderConfig"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="bidding_brand" property="biddingBrand"/>
        <result column="bidding_model" property="biddingModel"/>
        <result column="bidding_config" property="biddingConfig"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="recommended_procurement_ratio" property="recommendedProcurementRatio"/>
        <result column="erp_material_code" property="erpMaterialCode"/>
        <result column="universal_code" property="universalCode"/>
        <result column="universal_code_quotation" property="universalCodeQuotation"/>
        <result column="remarks" property="remarks"/>
        <result column="supplier_type" property="supplierType"/>
        <result column="warranty_period_months" property="warrantyPeriodMonths"/>
        <result column="is_new_model_brand" property="isNewModelBrand"/>
        <result column="passed_testing" property="passedTesting"/>
        <result column="technical_contact_person" property="technicalContactPerson"/>
        <result column="technical_contact_phone" property="technicalContactPhone"/>
        <result column="can_fit_600mm_deep_cabinet" property="canFit600mmDeepCabinet"/>
        <result column="last_modified_date" property="lastModifiedDate"/>
    </resultMap>

    <!-- 基础查询列 -->
    <sql id="Base_Column_List">
        id, equipment_name, tender_config, supplier_name, bidding_brand, bidding_model, bidding_config,
        unit_price, recommended_procurement_ratio, erp_material_code, universal_code, universal_code_quotation,
        remarks, supplier_type, warranty_period_months, is_new_model_brand, passed_testing,
        technical_contact_person, technical_contact_phone, can_fit_600mm_deep_cabinet, last_modified_date
    </sql>

    <!-- 分页查询设备采购 -->
    <select id="queryEquipmentProcurement" resultMap="EquipmentProcurementVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM y_equipment_procurement
        <where>
            <if test="queryForm != null and queryForm.keyword != null and queryForm.keyword != ''">
                AND (
                equipment_name LIKE CONCAT('%', #{queryForm.keyword}, '%')
                OR supplier_name LIKE CONCAT('%', #{queryForm.keyword}, '%')
                OR bidding_brand LIKE CONCAT('%', #{queryForm.keyword}, '%')
                OR bidding_model LIKE CONCAT('%', #{queryForm.keyword}, '%')
                )
            </if>
        </where>
        ORDER BY last_modified_date DESC
    </select>

    <!-- 根据ID查询设备采购详情 -->
    <select id="getEquipmentProcurementById" resultMap="EquipmentProcurementVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM y_equipment_procurement
        WHERE id = #{id}
    </select>

    <!-- 根据查询表单统计总数 -->
    <select id="selectCountByForm" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM y_equipment_procurement
        <where>
            <if test="queryForm != null and queryForm.keyword != null and queryForm.keyword != ''">
                AND (
                equipment_name LIKE CONCAT('%', #{queryForm.keyword}, '%')
                OR supplier_name LIKE CONCAT('%', #{queryForm.keyword}, '%')
                OR bidding_brand LIKE CONCAT('%', #{queryForm.keyword}, '%')
                OR bidding_model LIKE CONCAT('%', #{queryForm.keyword}, '%')
                )
            </if>
        </where>
    </select>
</mapper> 