<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.sa.admin.module.business.materiallist.dao.MaterialListDao">

    <!-- 结果映射 -->
    <resultMap id="MaterialListVO" type="com.sprixin.sa.admin.module.business.materiallist.domain.vo.MaterialListVO">
        <id column="id" property="id"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="brand" property="brand"/>
        <result column="brand_fuzzy" property="brandFuzzy"/>
        <result column="model" property="model"/>
        <result column="unit" property="unit"/>
        <result column="detail_config" property="detailConfig"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="supplier_contact" property="supplierContact"/>
        <result column="supplier_phone" property="supplierPhone"/>
        <result column="created_at" property="createdAt"/>
    </resultMap>

    <!-- 基础查询列 -->
    <sql id="Base_Column_List">
        id, material_code, material_name, supplier_name, brand, brand_fuzzy, model, unit, detail_config, unit_price, supplier_contact, supplier_phone, created_at
    </sql>

    <!-- 分页查询物料清单 -->
    <select id="queryMaterialList" resultMap="MaterialListVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM y_material_list
        <where>
            <if test="queryForm != null and queryForm.keyword != null and queryForm.keyword != ''">
                AND (
                material_name LIKE CONCAT('%', #{queryForm.keyword}, '%')
                OR brand LIKE CONCAT('%', #{queryForm.keyword}, '%')
                OR model LIKE CONCAT('%', #{queryForm.keyword}, '%')
                )
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 查询物料清单总数（使用查询表单） -->
    <select id="selectCountByForm" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM y_material_list
        <where>
            <if test="queryForm != null and queryForm.keyword != null and queryForm.keyword != ''">
                AND (
                material_name LIKE CONCAT('%', #{queryForm.keyword}, '%')
                OR brand LIKE CONCAT('%', #{queryForm.keyword}, '%')
                OR model LIKE CONCAT('%', #{queryForm.keyword}, '%')
                )
            </if>
        </where>
    </select>

    <!-- 查询物料清单总数（使用 MyBatis-Plus 的查询条件） -->
    <select id="selectCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM y_material_list
        <where>
            <if test="ew != null">
                ${ew.customSqlSegment}
            </if>
        </where>
    </select>

    <!-- 根据ID查询物料清单详情 -->
    <select id="getMaterialListById" resultMap="MaterialListVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM y_material_list
        WHERE id = #{id}
    </select>

    <!-- 查询所有物料清单 -->
    <select id="findAll" resultType="com.sprixin.sa.admin.module.business.materiallist.domain.entity.MaterialListEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM y_material_list
    </select>
</mapper> 