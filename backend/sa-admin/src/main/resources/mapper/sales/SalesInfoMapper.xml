<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.sa.admin.module.business.sales.dao.SalesInfoDao">

    <!-- 结果映射 -->
    <resultMap id="SalesInfoVO" type="com.sprixin.sa.admin.module.business.sales.domain.vo.SalesInfoVO">
        <id column="id" property="id"/>
        <result column="sales_name" property="salesName"/>
        <result column="mobile_phone" property="mobilePhone"/>
        <result column="email" property="email"/>
        <result column="position" property="position"/>
        <result column="region" property="region"/>
        <result column="employment_status" property="employmentStatus"/>
        <result column="record_date" property="recordDate"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 基础查询列 -->
    <sql id="Base_Column_List">
        id, sales_name, mobile_phone, email, position, region, 
        employment_status, record_date, created_at, updated_at
    </sql>

    <!-- 分页查询销售信息 -->
    <select id="querySalesInfo" resultMap="SalesInfoVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM y_sales_info
        <where>
            <if test="queryForm != null and queryForm.keyword != null and queryForm.keyword != ''">
                AND (
                    sales_name LIKE CONCAT('%', #{queryForm.keyword}, '%')
                    OR mobile_phone LIKE CONCAT('%', #{queryForm.keyword}, '%')
                    OR email LIKE CONCAT('%', #{queryForm.keyword}, '%')
                    OR region LIKE CONCAT('%', #{queryForm.keyword}, '%')
                )
            </if>
        </where>
        ORDER BY updated_at DESC
    </select>

    <!-- 查询销售信息总数 -->
    <select id="selectCountByForm" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM y_sales_info
        <where>
            <if test="queryForm != null and queryForm.keyword != null and queryForm.keyword != ''">
                AND (
                    sales_name LIKE CONCAT('%', #{queryForm.keyword}, '%')
                    OR mobile_phone LIKE CONCAT('%', #{queryForm.keyword}, '%')
                    OR email LIKE CONCAT('%', #{queryForm.keyword}, '%')
                    OR region LIKE CONCAT('%', #{queryForm.keyword}, '%')
                )
            </if>
        </where>
    </select>

    <!-- 根据ID查询销售信息详情 -->
    <select id="getSalesInfoById" resultMap="SalesInfoVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM y_sales_info
        WHERE id = #{id}
    </select>

    <!-- 查询所有销售人员列表（用于下拉选择） -->
    <select id="queryAllSales" resultMap="SalesInfoVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM y_sales_info
        WHERE employment_status = '在职'
        ORDER BY sales_name ASC
    </select>
</mapper>