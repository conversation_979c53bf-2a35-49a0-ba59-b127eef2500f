<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.sa.admin.module.business.spareparts.dao.SparePartsDao">

    <!-- 结果映射 -->
    <resultMap id="SparePartsVO" type="com.sprixin.sa.admin.module.business.spareparts.domain.vo.SparePartsVO">
        <id column="id" property="id"/>
        <result column="device_name" property="deviceName"/>
        <result column="brand" property="brand"/>
        <result column="model" property="model"/>
        <result column="configuration" property="configuration"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 基础查询列 -->
    <sql id="Base_Column_List">
        id, device_name, brand, model, configuration, unit_price, updated_at
    </sql>

    <!-- 分页查询备品备件 -->
    <select id="querySpareParts" resultMap="SparePartsVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM y_spare_parts
        <where>
            <if test="queryForm != null and queryForm.keyword != null and queryForm.keyword != ''">
                AND (
                device_name LIKE CONCAT('%', #{queryForm.keyword}, '%')
                OR brand LIKE CONCAT('%', #{queryForm.keyword}, '%')
                OR model LIKE CONCAT('%', #{queryForm.keyword}, '%')
                )
            </if>
        </where>
        ORDER BY updated_at DESC
    </select>

    <!-- 查询备品备件总数（使用查询表单） -->
    <select id="selectCountByForm" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM y_spare_parts
        <where>
            <if test="queryForm != null and queryForm.keyword != null and queryForm.keyword != ''">
                AND (
                device_name LIKE CONCAT('%', #{queryForm.keyword}, '%')
                OR brand LIKE CONCAT('%', #{queryForm.keyword}, '%')
                OR model LIKE CONCAT('%', #{queryForm.keyword}, '%')
                )
            </if>
        </where>
    </select>

    <!-- 查询备品备件总数（使用 MyBatis-Plus 的查询条件） -->
    <select id="selectCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM y_spare_parts
        <where>
            <if test="ew != null">
                ${ew.customSqlSegment}
            </if>
        </where>
    </select>

    <!-- 根据ID查询备品备件详情 -->
    <select id="getSparePartsById" resultMap="SparePartsVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM y_spare_parts
        WHERE id = #{id}
    </select>

    <!-- 查询所有备品备件 -->
    <select id="findAll" resultType="com.sprixin.sa.admin.module.business.spareparts.domain.entity.SparePartsEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM y_spare_parts
    </select>
</mapper> 