package com.sprixin.sa.base.config;

import com.sprixin.sa.base.common.constant.StringConst;
import com.sprixin.sa.base.common.util.SmartRequestUtil;
import com.sprixin.sa.base.module.support.repeatsubmit.RepeatSubmitAspect;
import com.sprixin.sa.base.module.support.repeatsubmit.ticket.RepeatSubmitRedisTicket;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.ValueOperations;

/**
 * 重复提交配置
 */
@Configuration
public class RepeatSubmitConfig {

    @Resource
    private ValueOperations<String, String> valueOperations;

    @Bean
    public RepeatSubmitAspect repeatSubmitAspect() {
        RepeatSubmitRedisTicket caffeineTicket = new RepeatSubmitRedisTicket(valueOperations, this::ticket);
        return new RepeatSubmitAspect(caffeineTicket);
    }

    /**
     * 获取指明某个用户的凭证
     */
    private String ticket(String servletPath) {
        Long userId = SmartRequestUtil.getRequestUserId();
        if (null == userId) {
            return StringConst.EMPTY;
        }
        return servletPath + "_" + userId;
    }
}
