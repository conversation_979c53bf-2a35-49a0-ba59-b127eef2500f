package com.sprixin.sa.base.module.support.changelog.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.base.module.support.changelog.domain.entity.ChangeLogEntity;
import com.sprixin.sa.base.module.support.changelog.domain.form.ChangeLogQueryForm;
import com.sprixin.sa.base.module.support.changelog.domain.vo.ChangeLogVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 系统更新日志 Dao
 */

@Mapper
public interface ChangeLogDao extends BaseMapper<ChangeLogEntity> {

    /**
     * 分页 查询
     */
    List<ChangeLogVO> queryPage(Page page, @Param("queryForm") ChangeLogQueryForm queryForm);

    /**
     * 根据版本查询 ChangeLog
     */
    ChangeLogEntity selectByVersion(@Param("version") String version);

}
