package com.sprixin.sa.base.module.support.changelog.domain.form;

import com.sprixin.sa.base.common.domain.PageParam;
import com.sprixin.sa.base.common.swagger.SchemaEnum;
import com.sprixin.sa.base.common.validator.enumeration.CheckEnum;
import com.sprixin.sa.base.module.support.changelog.constant.ChangeLogTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.Data;

/**
 * 系统更新日志 查询
 */

@Data
public class ChangeLogQueryForm extends PageParam {

    @SchemaEnum(value = ChangeLogTypeEnum.class, desc = "更新类型:[1:特大版本功能更新;2:功能更新;3:bug修复]")
    @CheckEnum(value = ChangeLogTypeEnum.class, message = "更新类型:[1:特大版本功能更新;2:功能更新;3:bug修复] 错误")
    private Integer type;

    @Schema(description = "关键字")
    private String keyword;

    @Schema(description = "发布日期")
    private LocalDate publicDateBegin;

    @Schema(description = "发布日期")
    private LocalDate publicDateEnd;

    @Schema(description = "创建时间")
    private LocalDate createTime;

    @Schema(description = "跳转链接")
    private String link;

}
