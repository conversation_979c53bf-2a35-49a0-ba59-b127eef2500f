package com.sprixin.sa.base.module.support.codegenerator.domain.vo;

import com.sprixin.sa.base.module.support.codegenerator.domain.model.CodeBasic;
import com.sprixin.sa.base.module.support.codegenerator.domain.model.CodeDelete;
import com.sprixin.sa.base.module.support.codegenerator.domain.model.CodeField;
import com.sprixin.sa.base.module.support.codegenerator.domain.model.CodeInsertAndUpdate;
import com.sprixin.sa.base.module.support.codegenerator.domain.model.CodeQueryField;
import com.sprixin.sa.base.module.support.codegenerator.domain.model.CodeTableField;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * 表的配置信息
 */

@Data
public class TableConfigVO {

    @Schema(description = "基础命名信息")
    private CodeBasic basic;

    @Schema(description = "字段列")
    private List<CodeField> fields;

    @Schema(description = "增加、修改 信息")
    private CodeInsertAndUpdate insertAndUpdate;

    @Schema(description = "删除 信息")
    private CodeDelete deleteInfo;

    @Schema(description = "查询字段")
    private List<CodeQueryField> queryFields;

    @Schema(description = "列表字段")
    private List<CodeTableField> tableFields;
}
