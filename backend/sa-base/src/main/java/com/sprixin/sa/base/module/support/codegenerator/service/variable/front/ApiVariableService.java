package com.sprixin.sa.base.module.support.codegenerator.service.variable.front;

import com.sprixin.sa.base.module.support.codegenerator.domain.form.CodeGeneratorConfigForm;
import com.sprixin.sa.base.module.support.codegenerator.service.variable.CodeGenerateBaseVariableService;
import java.util.HashMap;
import java.util.Map;

/**
 *
 */

public class ApiVariableService extends CodeGenerateBaseVariableService {

    @Override
    public boolean isSupport(CodeGeneratorConfigForm form) {
        return true;
    }

    @Override
    public Map<String, Object> getInjectVariablesMap(CodeGeneratorConfigForm form) {
        Map<String, Object> variablesMap = new HashMap<>();

        return variablesMap;
    }
}
