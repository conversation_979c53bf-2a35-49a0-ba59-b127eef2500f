package com.sprixin.sa.base.module.support.datatracer.domain.bo;

import java.lang.reflect.Field;
import lombok.Data;

/**
 * 变动内容
 */
@Data
public class DataTracerContentBO {

    /**
     * 变动字段
     */
    private Field field;

    /**
     * 变动字段的值
     */
    private Object fieldValue;

    /**
     * 变动字段描述
     */
    private String fieldDesc;

    /**
     * 变动内容
     */
    private String fieldContent;

}
