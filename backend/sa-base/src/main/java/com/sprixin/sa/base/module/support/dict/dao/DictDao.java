package com.sprixin.sa.base.module.support.dict.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.base.module.support.dict.domain.entity.DictEntity;
import com.sprixin.sa.base.module.support.dict.domain.form.DictQueryForm;
import com.sprixin.sa.base.module.support.dict.domain.vo.DictVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 数据字典 Dao
 */

@Mapper
@Component
public interface DictDao extends BaseMapper<DictEntity> {

    /**
     * 分页 查询
     */
    List<DictVO> queryPage(Page page, @Param("queryForm") DictQueryForm queryForm);

    /**
     * 根据 dictCode 去查询
     */
    DictEntity selectByCode(@Param("code") String code);

}
