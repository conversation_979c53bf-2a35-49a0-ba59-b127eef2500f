package com.sprixin.sa.base.module.support.feedback.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.base.module.support.feedback.domain.FeedbackEntity;
import com.sprixin.sa.base.module.support.feedback.domain.FeedbackQueryForm;
import com.sprixin.sa.base.module.support.feedback.domain.FeedbackVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 意见反馈 dao
 */
@Mapper
public interface FeedbackDao extends BaseMapper<FeedbackEntity> {

    /**
     * 分页查询
     */
    List<FeedbackVO> queryPage(Page page, @Param("query") FeedbackQueryForm query);
}
