package com.sprixin.sa.base.module.support.feedback.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.base.common.domain.RequestUser;
import com.sprixin.sa.base.common.domain.ResponseDTO;
import com.sprixin.sa.base.common.util.SmartBeanUtil;
import com.sprixin.sa.base.common.util.SmartPageUtil;
import com.sprixin.sa.base.module.support.feedback.dao.FeedbackDao;
import com.sprixin.sa.base.module.support.feedback.domain.FeedbackAddForm;
import com.sprixin.sa.base.module.support.feedback.domain.FeedbackEntity;
import com.sprixin.sa.base.module.support.feedback.domain.FeedbackQueryForm;
import com.sprixin.sa.base.module.support.feedback.domain.FeedbackVO;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 意见反馈
 */
@Service
public class FeedbackService {

    @Resource
    private FeedbackDao feedbackDao;

    /**
     * 分页查询
     */
    public ResponseDTO<PageResult<FeedbackVO>> query(FeedbackQueryForm queryForm) {
        Page page = SmartPageUtil.convert2PageQuery(queryForm);
        List<FeedbackVO> list = feedbackDao.queryPage(page, queryForm);
        PageResult<FeedbackVO> pageResultDTO = SmartPageUtil.convert2PageResult(page, list);
        if (pageResultDTO.getEmptyFlag()) {
            return ResponseDTO.ok(pageResultDTO);
        }
        return ResponseDTO.ok(pageResultDTO);
    }

    /**
     * 新建
     */
    public ResponseDTO<String> add(FeedbackAddForm addForm, RequestUser requestUser) {
        FeedbackEntity feedback = SmartBeanUtil.copy(addForm, FeedbackEntity.class);
        feedback.setUserType(requestUser.getUserType().getValue());
        feedback.setUserId(requestUser.getUserId());
        feedback.setUserName(requestUser.getUserName());
        feedbackDao.insert(feedback);
        return ResponseDTO.ok();
    }
}
