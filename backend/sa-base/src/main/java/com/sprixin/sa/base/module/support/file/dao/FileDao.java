package com.sprixin.sa.base.module.support.file.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.base.module.support.file.domain.entity.FileEntity;
import com.sprixin.sa.base.module.support.file.domain.form.FileQueryForm;
import com.sprixin.sa.base.module.support.file.domain.vo.FileVO;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 文件服务
 */
@Mapper
public interface FileDao extends BaseMapper<FileEntity> {

    /**
     * 文件key单个查询
     *
     * @param fileKey
     * @return
     */
    FileVO getByFileKey(@Param("fileKey") String fileKey);

    /**
     * 批量获取
     */
    List<FileVO> selectByFileKeyList(@Param("fileKeyList") Collection<String> fileKeyList);

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<FileVO> queryPage(Page page, @Param("queryForm") FileQueryForm queryForm);

    /**
     * 根据文件ID获取文件信息
     *
     * @param fileId
     * @return
     */
    FileVO getByFileId(@Param("fileId") Long fileId);

}
