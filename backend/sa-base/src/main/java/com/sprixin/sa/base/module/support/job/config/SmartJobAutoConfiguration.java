package com.sprixin.sa.base.module.support.job.config;

import com.sprixin.sa.base.module.support.job.core.SmartJob;
import com.sprixin.sa.base.module.support.job.core.SmartJobLauncher;
import com.sprixin.sa.base.module.support.job.repository.SmartJobRepository;
import java.util.List;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 定时任务 配置
 */
@Configuration
@EnableConfigurationProperties(SmartJobConfig.class)
@ConditionalOnProperty(
        prefix = SmartJobConfig.CONFIG_PREFIX,
        name = "enabled",
        havingValue = "true"
)
public class SmartJobAutoConfiguration {

    private final SmartJobConfig jobConfig;

    private final SmartJobRepository jobRepository;

    private final List<SmartJob> jobInterfaceList;

    public SmartJobAutoConfiguration(SmartJobConfig jobConfig,
                                     SmartJobRepository jobRepository,
                                     List<SmartJob> jobInterfaceList) {
        this.jobConfig = jobConfig;
        this.jobRepository = jobRepository;
        this.jobInterfaceList = jobInterfaceList;
    }

    /**
     * 定时任务启动器
     *
     * @return
     */
    @Bean
    public SmartJobLauncher initJobLauncher(RedissonClient redissonClient) {
        return new SmartJobLauncher(jobConfig, jobRepository, jobInterfaceList, redissonClient);
    }
}
