package com.sprixin.sa.base.module.support.knowledgebase.controller;

import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.base.common.domain.RequestUser;
import com.sprixin.sa.base.common.domain.ResponseDTO;
import com.sprixin.sa.base.module.support.operatelog.annotation.OperateLog;
import com.sprixin.sa.base.module.support.knowledgebase.domain.form.KnowledgeFileAddForm;
import com.sprixin.sa.base.module.support.knowledgebase.domain.form.KnowledgeFileQueryForm;
import com.sprixin.sa.base.module.support.knowledgebase.domain.vo.KnowledgeFileVO;
import com.sprixin.sa.base.module.support.knowledgebase.service.KnowledgeFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 知识库文件控制器
 */
@RestController
@Tag(name = "知识库文件管理")
@RequestMapping("/knowledge/file")
public class KnowledgeFileController {

    @Resource
    private KnowledgeFileService knowledgeFileService;

    @Operation(summary = "分页查询文件列表")
    @PostMapping("/query")
    public ResponseDTO<PageResult<KnowledgeFileVO>> queryPage(@RequestBody KnowledgeFileQueryForm queryForm) {
        return knowledgeFileService.queryPage(queryForm);
    }

    @Operation(summary = "根据文件夹ID获取文件列表")
    @GetMapping("/list/{folderId}")
    public ResponseDTO<List<KnowledgeFileVO>> getFilesByFolderId(@PathVariable Long folderId) {
        return knowledgeFileService.getFilesByFolderId(folderId);
    }

    @Operation(summary = "获取文件详情")
    @GetMapping("/get/{fileId}")
    public ResponseDTO<KnowledgeFileVO> getFileDetail(@PathVariable Long fileId) {
        return knowledgeFileService.getFileDetail(fileId);
    }

    @OperateLog
    @Operation(summary = "添加知识库文件")
    @PostMapping("/add")
    public ResponseDTO<Long> addFile(@RequestBody @Valid KnowledgeFileAddForm form, RequestUser requestUser) {
        return knowledgeFileService.addFile(form, requestUser);
    }

    @OperateLog
    @Operation(summary = "删除知识库文件")
    @GetMapping("/delete/{fileId}")
    public ResponseDTO<Void> deleteFile(@PathVariable Long fileId) {
        return knowledgeFileService.deleteFile(fileId);
    }

    @OperateLog
    @Operation(summary = "移动知识库文件")
    @PostMapping("/move/{fileId}/{targetFolderId}")
    public ResponseDTO<Void> moveFile(@PathVariable Long fileId, @PathVariable Long targetFolderId) {
        return knowledgeFileService.moveFile(fileId, targetFolderId);
    }
    
    //@OperateLog(content = "知识库文件上传")
    @Operation(summary = "知识库文件上传")
    @PostMapping("/upload")
    public ResponseDTO<KnowledgeFileVO> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("folderId") Long folderId,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "tags", required = false) String tags,
            RequestUser requestUser) {
        return knowledgeFileService.uploadFile(file, folderId, description, tags, requestUser);
    }
} 