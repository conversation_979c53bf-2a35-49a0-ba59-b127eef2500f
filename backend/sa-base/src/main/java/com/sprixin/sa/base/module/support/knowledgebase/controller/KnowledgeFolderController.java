package com.sprixin.sa.base.module.support.knowledgebase.controller;

import com.sprixin.sa.base.common.domain.RequestUser;
import com.sprixin.sa.base.common.domain.ResponseDTO;
import com.sprixin.sa.base.module.support.operatelog.annotation.OperateLog;
import com.sprixin.sa.base.module.support.knowledgebase.domain.form.KnowledgeFolderAddForm;
import com.sprixin.sa.base.module.support.knowledgebase.domain.form.KnowledgeFolderUpdateForm;
import com.sprixin.sa.base.module.support.knowledgebase.domain.vo.KnowledgeFolderVO;
import com.sprixin.sa.base.module.support.knowledgebase.service.KnowledgeFolderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 知识库文件夹控制器
 */
@RestController
@Tag(name = "知识库文件夹管理")
@RequestMapping("/knowledge/folder")
public class KnowledgeFolderController {

    @Resource
    private KnowledgeFolderService knowledgeFolderService;

    @Operation(summary = "获取文件夹树")
    @GetMapping("/tree")
    public ResponseDTO<List<KnowledgeFolderVO>> getFolderTree() {
        return knowledgeFolderService.getFolderTree();
    }

    @Operation(summary = "获取文件夹详情")
    @GetMapping("/get/{folderId}")
    public ResponseDTO<KnowledgeFolderVO> getFolderDetail(@PathVariable Long folderId) {
        return knowledgeFolderService.getFolderDetail(folderId);
    }

    @OperateLog
    @Operation(summary = "添加知识库文件夹")
    @PostMapping("/add")
    public ResponseDTO<Long> addFolder(@RequestBody @Valid KnowledgeFolderAddForm form, RequestUser requestUser) {
        return knowledgeFolderService.addFolder(form, requestUser);
    }

    @OperateLog
    @Operation(summary = "更新知识库文件夹")
    @PostMapping("/update")
    public ResponseDTO<Void> updateFolder(@RequestBody @Valid KnowledgeFolderUpdateForm form) {
        return knowledgeFolderService.updateFolder(form);
    }

    @OperateLog
    @Operation(summary = "删除知识库文件夹")
    @GetMapping("/delete/{folderId}")
    public ResponseDTO<Void> deleteFolder(@PathVariable Long folderId) {
        return knowledgeFolderService.deleteFolder(folderId);
    }
} 