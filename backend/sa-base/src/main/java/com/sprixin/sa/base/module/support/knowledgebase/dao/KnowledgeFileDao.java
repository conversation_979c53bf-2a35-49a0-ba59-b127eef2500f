package com.sprixin.sa.base.module.support.knowledgebase.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.base.module.support.knowledgebase.domain.entity.KnowledgeFileEntity;
import com.sprixin.sa.base.module.support.knowledgebase.domain.form.KnowledgeFileQueryForm;
import com.sprixin.sa.base.module.support.knowledgebase.domain.vo.KnowledgeFileVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 知识库文件DAO
 */
@Mapper
public interface KnowledgeFileDao extends BaseMapper<KnowledgeFileEntity> {

    /**
     * 分页查询文件
     *
     * @param page 分页参数
     * @param queryForm 查询表单
     * @return 文件列表
     */
    List<KnowledgeFileVO> queryPage(Page page, @Param("queryForm") KnowledgeFileQueryForm queryForm);

    /**
     * 根据文件夹ID获取文件列表
     *
     * @param folderId 文件夹ID
     * @return 文件列表
     */
    List<KnowledgeFileVO> getFilesByFolderId(@Param("folderId") Long folderId);

    /**
     * 获取文件详情
     *
     * @param fileId 文件ID
     * @return 文件详情
     */
    KnowledgeFileVO getFileDetail(@Param("fileId") Long fileId);
} 