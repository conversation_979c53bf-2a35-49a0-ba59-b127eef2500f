package com.sprixin.sa.base.module.support.knowledgebase.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sprixin.sa.base.module.support.knowledgebase.domain.entity.KnowledgeFolderEntity;
import com.sprixin.sa.base.module.support.knowledgebase.domain.vo.KnowledgeFolderVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 知识库文件夹DAO
 */
@Mapper
public interface KnowledgeFolderDao extends BaseMapper<KnowledgeFolderEntity> {

    /**
     * 获取文件夹树
     *
     * @return 文件夹树列表
     */
    List<KnowledgeFolderVO> getFolderTree();

    /**
     * 根据父ID获取文件夹列表
     *
     * @param parentId 父文件夹ID
     * @return 文件夹列表
     */
    List<KnowledgeFolderVO> getFoldersByParentId(@Param("parentId") Long parentId);

    /**
     * 获取文件夹详情
     *
     * @param folderId 文件夹ID
     * @return 文件夹详情
     */
    KnowledgeFolderVO getFolderDetail(@Param("folderId") Long folderId);
} 