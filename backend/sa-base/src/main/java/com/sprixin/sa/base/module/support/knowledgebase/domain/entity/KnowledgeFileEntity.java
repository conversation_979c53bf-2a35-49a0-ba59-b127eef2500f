package com.sprixin.sa.base.module.support.knowledgebase.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 知识库文件
 */
@Data
@TableName(value = "t_knowledge_file")
public class KnowledgeFileEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long fileId;

    /**
     * 所属文件夹ID
     */
    private Long folderId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件key，用于文件下载
     */
    private String fileKey;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件描述
     */
    private String description;

    /**
     * 文件标签
     */
    private String tags;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人 姓名
     */
    private String creatorName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 