package com.sprixin.sa.base.module.support.knowledgebase.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 知识库文件夹
 */
@Data
@TableName(value = "t_knowledge_folder")
public class KnowledgeFolderEntity {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long folderId;

    /**
     * 文件夹名称
     */
    private String folderName;

    /**
     * 父文件夹ID
     */
    private Long parentId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 文件夹描述
     */
    private String description;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人 姓名
     */
    private String creatorName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 