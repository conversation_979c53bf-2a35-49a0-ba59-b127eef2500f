package com.sprixin.sa.base.module.support.knowledgebase.domain.form;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 知识库文件添加表单
 */
@Data
public class KnowledgeFileAddForm {

    /**
     * 文件夹ID
     */
    @NotNull(message = "文件夹ID不能为空")
    private Long folderId;

    /**
     * 文件标识符
     */
    @NotBlank(message = "文件标识符不能为空")
    private String fileKey;

    /**
     * 文件名称
     */
    @NotBlank(message = "文件名称不能为空")
    private String fileName;

    /**
     * 文件URL
     */
    @NotBlank(message = "文件URL不能为空")
    private String fileUrl;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 描述
     */
    private String description;

    /**
     * 标签，多个用逗号分隔
     */
    private String tags;
} 