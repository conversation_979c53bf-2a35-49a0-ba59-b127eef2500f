package com.sprixin.sa.base.module.support.knowledgebase.domain.form;

import com.sprixin.sa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDate;

/**
 * 知识库文件查询表单
 */
@Data
public class KnowledgeFileQueryForm extends PageParam {

    @Schema(description = "文件夹ID")
    private Long folderId;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "文件标签")
    private String tags;

    @Schema(description = "创建人名称")
    private String creatorName;

    @Schema(description = "创建开始时间")
    private LocalDate createTimeBegin;

    @Schema(description = "创建结束时间")
    private LocalDate createTimeEnd;

    @Schema(description = "关键字（文件名、描述、标签）")
    private String keywords;
} 