package com.sprixin.sa.base.module.support.knowledgebase.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 知识库文件夹添加表单
 */
@Data
public class KnowledgeFolderAddForm {

    @Schema(description = "文件夹名称")
    @NotBlank(message = "文件夹名称不能为空")
    private String folderName;

    @Schema(description = "父文件夹ID")
    @NotNull(message = "父文件夹ID不能为空")
    private Long parentId;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "文件夹描述")
    private String description;
} 