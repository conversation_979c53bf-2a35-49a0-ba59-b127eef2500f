package com.sprixin.sa.base.module.support.knowledgebase.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 知识库文件VO
 */
@Data
public class KnowledgeFileVO {

    @Schema(description = "文件ID")
    private Long fileId;

    @Schema(description = "文件夹ID")
    private Long folderId;

    @Schema(description = "文件夹名称")
    private String folderName;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件Key")
    private String fileKey;

    @Schema(description = "文件大小")
    private Long fileSize;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "文件URL")
    private String fileUrl;

    @Schema(description = "文件描述")
    private String description;

    @Schema(description = "文件标签")
    private String tags;

    @Schema(description = "创建人ID")
    private Long creatorId;

    @Schema(description = "创建人名称")
    private String creatorName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
} 