package com.sprixin.sa.base.module.support.knowledgebase.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 知识库文件夹VO
 */
@Data
public class KnowledgeFolderVO {

    @Schema(description = "文件夹ID")
    private Long folderId;

    @Schema(description = "文件夹名称")
    private String folderName;

    @Schema(description = "父文件夹ID")
    private Long parentId;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "文件夹描述")
    private String description;

    @Schema(description = "创建人ID")
    private Long creatorId;

    @Schema(description = "创建人名称")
    private String creatorName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "子文件夹")
    private List<KnowledgeFolderVO> children;
} 