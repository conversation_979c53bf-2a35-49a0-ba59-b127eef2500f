package com.sprixin.sa.base.module.support.knowledgebase.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.base.common.domain.RequestUser;
import com.sprixin.sa.base.common.domain.ResponseDTO;
import com.sprixin.sa.base.common.util.SmartPageUtil;
import com.sprixin.sa.base.module.support.file.constant.FileFolderTypeEnum;
import com.sprixin.sa.base.module.support.file.domain.vo.FileVO;
import com.sprixin.sa.base.module.support.file.domain.vo.FileUploadVO;
import com.sprixin.sa.base.module.support.file.service.FileService;
import com.sprixin.sa.base.module.support.knowledgebase.dao.KnowledgeFileDao;
import com.sprixin.sa.base.module.support.knowledgebase.dao.KnowledgeFolderDao;
import com.sprixin.sa.base.module.support.knowledgebase.domain.entity.KnowledgeFileEntity;
import com.sprixin.sa.base.module.support.knowledgebase.domain.entity.KnowledgeFolderEntity;
import com.sprixin.sa.base.module.support.knowledgebase.domain.form.KnowledgeFileAddForm;
import com.sprixin.sa.base.module.support.knowledgebase.domain.form.KnowledgeFileQueryForm;
import com.sprixin.sa.base.module.support.knowledgebase.domain.vo.KnowledgeFileVO;
import com.sprixin.sa.base.common.code.SystemErrorCode;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collections;
import java.util.List;

/**
 * 知识库文件服务
 */
@Service
public class KnowledgeFileService {

    @Resource
    private KnowledgeFileDao knowledgeFileDao;

    @Resource
    private KnowledgeFolderDao knowledgeFolderDao;

    @Resource
    private FileService fileService;

    /**
     * 分页查询文件列表
     *
     * @param queryForm 查询表单
     * @return 分页结果
     */
    public ResponseDTO<PageResult<KnowledgeFileVO>> queryPage(KnowledgeFileQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<KnowledgeFileVO> fileList = knowledgeFileDao.queryPage(page, queryForm);
        
        // 获取文件URL
        setFileUrl(fileList);
        
        PageResult<KnowledgeFileVO> pageResult = SmartPageUtil.convert2PageResult(page, fileList);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 根据文件夹ID获取文件列表
     *
     * @param folderId 文件夹ID
     * @return 文件列表
     */
    public ResponseDTO<List<KnowledgeFileVO>> getFilesByFolderId(Long folderId) {
        // 检查文件夹是否存在
        KnowledgeFolderEntity folderEntity = knowledgeFolderDao.selectById(folderId);
        if (folderEntity == null) {
            return ResponseDTO.userErrorParam("文件夹不存在");
        }
        
        List<KnowledgeFileVO> fileList = knowledgeFileDao.getFilesByFolderId(folderId);
        
        // 获取文件URL
        setFileUrl(fileList);
        
        return ResponseDTO.ok(fileList);
    }

    /**
     * 获取文件详情
     *
     * @param fileId 文件ID
     * @return 文件详情
     */
    public ResponseDTO<KnowledgeFileVO> getFileDetail(Long fileId) {
        KnowledgeFileVO fileDetail = knowledgeFileDao.getFileDetail(fileId);
        if (fileDetail == null) {
            return ResponseDTO.userErrorParam("文件不存在");
        }
        
        // 获取文件URL
        setFileUrl(Collections.singletonList(fileDetail));
        
        return ResponseDTO.ok(fileDetail);
    }

    /**
     * 添加文件
     *
     * @param form 添加表单
     * @param requestUser 请求用户
     * @return 添加结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Long> addFile(KnowledgeFileAddForm form, RequestUser requestUser) {
        // 检查文件夹是否存在
        KnowledgeFolderEntity folderEntity = knowledgeFolderDao.selectById(form.getFolderId());
        if (folderEntity == null) {
            return ResponseDTO.userErrorParam("文件夹不存在");
        }
        
        // 获取文件信息
        List<FileVO> fileList = fileService.getFileList(Collections.singletonList(form.getFileKey()));
        if (fileList.isEmpty()) {
            return ResponseDTO.userErrorParam("文件不存在");
        }
        
        FileVO fileVO = fileList.get(0);
        
        // 创建文件实体
        KnowledgeFileEntity fileEntity = new KnowledgeFileEntity();
        fileEntity.setFolderId(form.getFolderId());
        fileEntity.setFileName(fileVO.getFileName());
        fileEntity.setFileKey(fileVO.getFileKey());
        fileEntity.setFileSize(fileVO.getFileSize() != null ? Long.valueOf(fileVO.getFileSize()) : null);
        fileEntity.setFileType(fileVO.getFileType());
        fileEntity.setDescription(form.getDescription());
        fileEntity.setTags(form.getTags());
        fileEntity.setCreatorId(requestUser.getUserId());
        fileEntity.setCreatorName(requestUser.getUserName());
        
        // 插入数据库
        knowledgeFileDao.insert(fileEntity);
        
        return ResponseDTO.ok(fileEntity.getFileId());
    }

    /**
     * 删除文件
     *
     * @param fileId 文件ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Void> deleteFile(Long fileId) {
        // 检查文件是否存在
        KnowledgeFileEntity fileEntity = knowledgeFileDao.selectById(fileId);
        if (fileEntity == null) {
            return ResponseDTO.userErrorParam("文件不存在");
        }
        
        // 删除文件
        knowledgeFileDao.deleteById(fileId);
        
        return ResponseDTO.ok();
    }

    /**
     * 移动文件
     *
     * @param fileId 文件ID
     * @param targetFolderId 目标文件夹ID
     * @return 移动结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Void> moveFile(Long fileId, Long targetFolderId) {
        // 检查文件是否存在
        KnowledgeFileEntity fileEntity = knowledgeFileDao.selectById(fileId);
        if (fileEntity == null) {
            return ResponseDTO.userErrorParam("文件不存在");
        }
        
        // 检查目标文件夹是否存在
        KnowledgeFolderEntity folderEntity = knowledgeFolderDao.selectById(targetFolderId);
        if (folderEntity == null) {
            return ResponseDTO.userErrorParam("目标文件夹不存在");
        }
        
        // 更新文件所属文件夹
        fileEntity.setFolderId(targetFolderId);
        knowledgeFileDao.updateById(fileEntity);
        
        return ResponseDTO.ok();
    }

    /**
     * 一站式上传知识库文件
     *
     * @param file 文件
     * @param folderId 文件夹ID
     * @param description 描述
     * @param tags 标签
     * @param requestUser 请求用户
     * @return 上传结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<KnowledgeFileVO> uploadFile(MultipartFile file, Long folderId, 
                                                String description, String tags, RequestUser requestUser) {
        // 1. 检查文件夹是否存在
        KnowledgeFolderEntity folderEntity = knowledgeFolderDao.selectById(folderId);
        if (folderEntity == null) {
            return ResponseDTO.userErrorParam("文件夹不存在");
        }
        
        // 2. 上传文件到存储系统
        ResponseDTO<FileUploadVO> uploadResponse = fileService.fileUpload(
            file, FileFolderTypeEnum.KNOWLEDGE_BASE.getValue(), requestUser);
        
        if (!uploadResponse.getOk()) {
            return ResponseDTO.error(SystemErrorCode.SYSTEM_ERROR, uploadResponse.getMsg());
        }
        
        FileUploadVO fileVO = uploadResponse.getData();
        
        // 3. 创建知识库文件记录
        KnowledgeFileEntity fileEntity = new KnowledgeFileEntity();
        fileEntity.setFolderId(folderId);
        fileEntity.setFileName(fileVO.getFileName());
        fileEntity.setFileKey(fileVO.getFileKey());
        fileEntity.setFileSize(file.getSize());
        fileEntity.setFileType(fileVO.getFileType());
        fileEntity.setDescription(description);
        fileEntity.setTags(tags);
        fileEntity.setCreatorId(requestUser.getUserId());
        fileEntity.setCreatorName(requestUser.getUserName());
        
        // 4. 保存到数据库
        knowledgeFileDao.insert(fileEntity);
        
        // 5. 构建返回数据
        KnowledgeFileVO result = new KnowledgeFileVO();
        BeanUtils.copyProperties(fileEntity, result);
        result.setFileUrl(fileVO.getFileUrl());
        
        return ResponseDTO.ok(result);
    }
    
    /**
     * 为文件列表设置URL
     *
     * @param fileList 文件列表
     */
    private void setFileUrl(List<KnowledgeFileVO> fileList) {
        if (fileList == null || fileList.isEmpty()) {
            return;
        }
        
        for (KnowledgeFileVO fileVO : fileList) {
            ResponseDTO<String> response = fileService.getFileUrl(fileVO.getFileKey());
            if (response.getOk()) {
                fileVO.setFileUrl(response.getData());
            }
        }
    }
} 