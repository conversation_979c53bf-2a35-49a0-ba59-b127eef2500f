package com.sprixin.sa.base.module.support.knowledgebase.service;

import com.sprixin.sa.base.common.domain.RequestUser;
import com.sprixin.sa.base.common.domain.ResponseDTO;
import com.sprixin.sa.base.module.support.knowledgebase.dao.KnowledgeFolderDao;
import com.sprixin.sa.base.module.support.knowledgebase.domain.entity.KnowledgeFolderEntity;
import com.sprixin.sa.base.module.support.knowledgebase.domain.form.KnowledgeFolderAddForm;
import com.sprixin.sa.base.module.support.knowledgebase.domain.form.KnowledgeFolderUpdateForm;
import com.sprixin.sa.base.module.support.knowledgebase.domain.vo.KnowledgeFolderVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 知识库文件夹服务
 */
@Service
public class KnowledgeFolderService {

    @Resource
    private KnowledgeFolderDao knowledgeFolderDao;

    /**
     * 获取文件夹树
     *
     * @return 文件夹树
     */
    public ResponseDTO<List<KnowledgeFolderVO>> getFolderTree() {
        List<KnowledgeFolderVO> folderList = knowledgeFolderDao.getFolderTree();
        List<KnowledgeFolderVO> resultList = buildFolderTree(folderList);
        return ResponseDTO.ok(resultList);
    }

    /**
     * 构建文件夹树
     *
     * @param folderList 文件夹列表
     * @return 树形结构
     */
    private List<KnowledgeFolderVO> buildFolderTree(List<KnowledgeFolderVO> folderList) {
        List<KnowledgeFolderVO> resultList = new ArrayList<>();
        
        // 按父ID分组
        Map<Long, List<KnowledgeFolderVO>> parentIdMap = folderList.stream()
                .collect(Collectors.groupingBy(KnowledgeFolderVO::getParentId));
        
        // 找出顶级文件夹
        List<KnowledgeFolderVO> rootFolders = parentIdMap.getOrDefault(0L, new ArrayList<>());
        
        // 递归构建树
        for (KnowledgeFolderVO rootFolder : rootFolders) {
            buildFolderTreeRecursive(rootFolder, parentIdMap);
            resultList.add(rootFolder);
        }
        
        return resultList;
    }

    /**
     * 递归构建文件夹树
     *
     * @param folder 当前文件夹
     * @param parentIdMap 父ID映射
     */
    private void buildFolderTreeRecursive(KnowledgeFolderVO folder, Map<Long, List<KnowledgeFolderVO>> parentIdMap) {
        List<KnowledgeFolderVO> children = parentIdMap.getOrDefault(folder.getFolderId(), new ArrayList<>());
        if (!children.isEmpty()) {
            folder.setChildren(children);
            for (KnowledgeFolderVO child : children) {
                buildFolderTreeRecursive(child, parentIdMap);
            }
        }
    }

    /**
     * 获取文件夹详情
     *
     * @param folderId 文件夹ID
     * @return 文件夹详情
     */
    public ResponseDTO<KnowledgeFolderVO> getFolderDetail(Long folderId) {
        KnowledgeFolderVO folderDetail = knowledgeFolderDao.getFolderDetail(folderId);
        if (folderDetail == null) {
            return ResponseDTO.userErrorParam("文件夹不存在");
        }
        return ResponseDTO.ok(folderDetail);
    }

    /**
     * 添加文件夹
     *
     * @param form 添加表单
     * @param requestUser 请求用户
     * @return 添加结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Long> addFolder(KnowledgeFolderAddForm form, RequestUser requestUser) {
        // 检查父文件夹是否存在
        if (form.getParentId() != 0) {
            KnowledgeFolderEntity parentFolder = knowledgeFolderDao.selectById(form.getParentId());
            if (parentFolder == null) {
                return ResponseDTO.userErrorParam("父文件夹不存在");
            }
        }
        
        // 创建文件夹实体
        KnowledgeFolderEntity folderEntity = new KnowledgeFolderEntity();
        folderEntity.setFolderName(form.getFolderName());
        folderEntity.setParentId(form.getParentId());
        folderEntity.setSort(form.getSort() != null ? form.getSort() : 0);
        folderEntity.setDescription(form.getDescription());
        folderEntity.setCreatorId(requestUser.getUserId());
        folderEntity.setCreatorName(requestUser.getUserName());
        
        // 插入数据库
        knowledgeFolderDao.insert(folderEntity);
        
        return ResponseDTO.ok(folderEntity.getFolderId());
    }

    /**
     * 更新文件夹
     *
     * @param form 更新表单
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Void> updateFolder(KnowledgeFolderUpdateForm form) {
        // 检查文件夹是否存在
        KnowledgeFolderEntity folderEntity = knowledgeFolderDao.selectById(form.getFolderId());
        if (folderEntity == null) {
            return ResponseDTO.userErrorParam("文件夹不存在");
        }
        
        // 检查父文件夹是否存在
        if (form.getParentId() != 0) {
            KnowledgeFolderEntity parentFolder = knowledgeFolderDao.selectById(form.getParentId());
            if (parentFolder == null) {
                return ResponseDTO.userErrorParam("父文件夹不存在");
            }
            
            // 防止将文件夹移动到其子文件夹下
            if (isChildFolder(form.getFolderId(), form.getParentId())) {
                return ResponseDTO.userErrorParam("不能将文件夹移动到其子文件夹下");
            }
        }
        
        // 更新文件夹信息
        folderEntity.setFolderName(form.getFolderName());
        folderEntity.setParentId(form.getParentId());
        folderEntity.setSort(form.getSort() != null ? form.getSort() : folderEntity.getSort());
        folderEntity.setDescription(form.getDescription());
        
        // 更新数据库
        knowledgeFolderDao.updateById(folderEntity);
        
        return ResponseDTO.ok();
    }

    /**
     * 判断是否是子文件夹
     *
     * @param parentId 父ID
     * @param childId 子ID
     * @return 是否是子文件夹
     */
    private boolean isChildFolder(Long parentId, Long childId) {
        if (parentId.equals(childId)) {
            return true;
        }
        
        List<KnowledgeFolderVO> childFolders = knowledgeFolderDao.getFoldersByParentId(parentId);
        if (childFolders.isEmpty()) {
            return false;
        }
        
        for (KnowledgeFolderVO folder : childFolders) {
            if (isChildFolder(folder.getFolderId(), childId)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 删除文件夹
     *
     * @param folderId 文件夹ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Void> deleteFolder(Long folderId) {
        // 检查文件夹是否存在
        KnowledgeFolderEntity folderEntity = knowledgeFolderDao.selectById(folderId);
        if (folderEntity == null) {
            return ResponseDTO.userErrorParam("文件夹不存在");
        }
        
        // 检查是否有子文件夹
        List<KnowledgeFolderVO> childFolders = knowledgeFolderDao.getFoldersByParentId(folderId);
        if (!childFolders.isEmpty()) {
            return ResponseDTO.userErrorParam("文件夹下存在子文件夹，请先删除子文件夹");
        }
        
        // 删除文件夹
        knowledgeFolderDao.deleteById(folderId);
        
        return ResponseDTO.ok();
    }
} 