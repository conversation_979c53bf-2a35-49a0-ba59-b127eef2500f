package com.sprixin.sa.base.module.support.operatelog;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.base.module.support.operatelog.domain.OperateLogEntity;
import com.sprixin.sa.base.module.support.operatelog.domain.OperateLogQueryForm;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 操作日志
 */
@Mapper
public interface OperateLogDao extends BaseMapper<OperateLogEntity> {

    /**
     * 分页查询
     *
     * @param page
     * @param queryForm
     * @return UserOperateLogEntity
     */
    List<OperateLogEntity> queryByPage(Page page, @Param("query") OperateLogQueryForm queryForm);

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    void deleteByIds(@Param("idList") List<Long> idList);
}
