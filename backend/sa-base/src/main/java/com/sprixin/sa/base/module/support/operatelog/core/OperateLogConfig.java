package com.sprixin.sa.base.module.support.operatelog.core;

import com.sprixin.sa.base.module.support.operatelog.domain.OperateLogEntity;
import java.util.function.Function;
import lombok.Builder;
import lombok.Data;

/**
 * 配置
 */
@Data
@Builder
public class OperateLogConfig {

    /**
     * 操作日志存储方法
     */
    private Function<OperateLogEntity, Boolean> saveFunction;

    /**
     * 核心线程数
     */
    private Integer corePoolSize;

    /**
     * 队列大小
     */
    private Integer queueCapacity;

}
