package com.sprixin.sa.base.module.support.reload;

import com.sprixin.sa.base.common.util.SmartBeanUtil;
import com.sprixin.sa.base.module.support.reload.core.AbstractSmartReloadCommand;
import com.sprixin.sa.base.module.support.reload.core.domain.SmartReloadItem;
import com.sprixin.sa.base.module.support.reload.core.domain.SmartReloadResult;
import com.sprixin.sa.base.module.support.reload.dao.ReloadItemDao;
import com.sprixin.sa.base.module.support.reload.dao.ReloadResultDao;
import com.sprixin.sa.base.module.support.reload.domain.ReloadItemEntity;
import com.sprixin.sa.base.module.support.reload.domain.ReloadResultEntity;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 * reload 操作
 */
@Component
public class ReloadCommand extends AbstractSmartReloadCommand {

    @Resource
    private ReloadItemDao reloadItemDao;

    @Resource
    private ReloadResultDao reloadResultDao;

    /**
     * 读取数据库中SmartReload项
     *
     * @return List<ReloadItem>
     */
    @Override
    public List<SmartReloadItem> readReloadItem() {
        List<ReloadItemEntity> reloadItemEntityList = reloadItemDao.selectList(null);
        return SmartBeanUtil.copyList(reloadItemEntityList, SmartReloadItem.class);
    }

    /**
     * 保存reload结果
     *
     * @param smartReloadResult
     */
    @Override
    public void handleReloadResult(SmartReloadResult smartReloadResult) {
        ReloadResultEntity reloadResultEntity = SmartBeanUtil.copy(smartReloadResult, ReloadResultEntity.class);
        reloadResultDao.insert(reloadResultEntity);
    }
}
