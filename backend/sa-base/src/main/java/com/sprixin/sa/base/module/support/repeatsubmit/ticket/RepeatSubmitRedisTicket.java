package com.sprixin.sa.base.module.support.repeatsubmit.ticket;

import java.util.function.Function;
import org.springframework.data.redis.core.ValueOperations;

/**
 * 凭证（redis实现）
 */
public class RepeatSubmitRedisTicket extends AbstractRepeatSubmitTicket {

    private final ValueOperations<String, String> redisValueOperations;

    public RepeatSubmitRedisTicket(ValueOperations<String, String> redisValueOperations,
                                   Function<String, String> ticketFunction) {
        super(ticketFunction);
        this.redisValueOperations = redisValueOperations;
    }

    @Override
    public Long getTicketTimestamp(String ticket) {
        String ticketLastTime = redisValueOperations.get(ticket);
        return ticketLastTime == null ? null : Long.valueOf(ticketLastTime);
    }

    @Override
    public void putTicket(String ticket) {
        redisValueOperations.getOperations().delete(ticket);
        this.getTicketTimestamp(ticket);
    }

}
