package com.sprixin.sa.base.module.support.serialnumber.constant;

import com.sprixin.sa.base.common.enumeration.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 单据序列号 枚举
 */
@AllArgsConstructor
@Getter
public enum SerialNumberIdEnum implements BaseEnum {

    ORDER(1, "订单id"),

    CONTRACT(2, "合同id"),

    ;

    private final Integer serialNumberId;

    private final String desc;

    @Override
    public Integer getValue() {
        return serialNumberId;
    }

    @Override
    public String toString() {
        return "SerialNumberIdEnum{" +
               "serialNumberId=" + serialNumberId +
               ", desc='" + desc + '\'' +
               '}';
    }
}
