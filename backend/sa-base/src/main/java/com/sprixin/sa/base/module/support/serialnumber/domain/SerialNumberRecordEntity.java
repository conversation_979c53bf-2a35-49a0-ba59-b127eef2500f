package com.sprixin.sa.base.module.support.serialnumber.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 单据序列号 表结构
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_serial_number_record")
public class SerialNumberRecordEntity {

    /**
     * 单号id
     */
    @TableId(type = IdType.NONE)
    private Integer serialNumberId;

    /**
     * 记录日期
     */
    private LocalDate recordDate;

    /**
     * 最后更新值
     */
    private Long lastNumber;

    /**
     * 上次生成时间
     */
    private LocalDateTime lastTime;

    /**
     * 数量
     */
    private Long count;

    private LocalDateTime updateTime;

    private LocalDateTime createTime;

}
