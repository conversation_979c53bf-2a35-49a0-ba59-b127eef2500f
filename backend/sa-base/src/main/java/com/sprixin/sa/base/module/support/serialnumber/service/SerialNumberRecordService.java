package com.sprixin.sa.base.module.support.serialnumber.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sprixin.sa.base.common.domain.PageResult;
import com.sprixin.sa.base.common.util.SmartPageUtil;
import com.sprixin.sa.base.module.support.serialnumber.dao.SerialNumberRecordDao;
import com.sprixin.sa.base.module.support.serialnumber.domain.SerialNumberRecordEntity;
import com.sprixin.sa.base.module.support.serialnumber.domain.SerialNumberRecordQueryForm;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 单据序列号 记录
 */
@Service
public class SerialNumberRecordService {

    @Resource
    private SerialNumberRecordDao serialNumberRecordDao;

    public PageResult<SerialNumberRecordEntity> query(SerialNumberRecordQueryForm queryForm) {
        Page page = SmartPageUtil.convert2PageQuery(queryForm);
        List<SerialNumberRecordEntity> recordList = serialNumberRecordDao.query(page, queryForm);
        return SmartPageUtil.convert2PageResult(page, recordList);
    }
}
