package ${packageName};

#foreach ($importClass in $importPackageList)
$importClass
#end

/**
 * ${basic.description} 新建表单
 *
 */

@Data
public class ${name.upperCamel}AddForm {
#foreach ($field in $fields)

#if($field.isEnum)
    ${field.apiModelProperty}
    ${field.checkEnum}
    private $field.javaType $field.fieldName;
#end
#if(!$field.isEnum)
    ${field.apiModelProperty}$!{field.notEmpty}$!{field.dict}$!{field.file}
    private $field.javaType $field.fieldName;
#end
#end

}
