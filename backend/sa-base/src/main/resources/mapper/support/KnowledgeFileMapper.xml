<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.sa.base.module.support.knowledgebase.dao.KnowledgeFileDao">

    <!-- 分页查询文件 -->
    <select id="queryPage" resultType="com.sprixin.sa.base.module.support.knowledgebase.domain.vo.KnowledgeFileVO">
        SELECT
            f.file_id as fileId,
            f.folder_id as folderId,
            fold.folder_name as folderName,
            f.file_name as fileName,
            f.file_key as fileKey,
            f.file_size as fileSize,
            f.file_type as fileType,
            f.description,
            f.tags,
            f.creator_id as creatorId,
            f.creator_name as creatorName,
            f.create_time as createTime,
            f.update_time as updateTime
        FROM
            t_knowledge_file f
        LEFT JOIN
            t_knowledge_folder fold ON f.folder_id = fold.folder_id
        <where>
            <if test="queryForm.folderId != null">
                AND f.folder_id = #{queryForm.folderId}
            </if>
            <if test="queryForm.fileName != null and queryForm.fileName != ''">
                AND f.file_name LIKE CONCAT('%', #{queryForm.fileName}, '%')
            </if>
            <if test="queryForm.fileType != null and queryForm.fileType != ''">
                AND f.file_type = #{queryForm.fileType}
            </if>
            <if test="queryForm.tags != null and queryForm.tags != ''">
                AND f.tags LIKE CONCAT('%', #{queryForm.tags}, '%')
            </if>
            <if test="queryForm.creatorName != null and queryForm.creatorName != ''">
                AND f.creator_name LIKE CONCAT('%', #{queryForm.creatorName}, '%')
            </if>
            <if test="queryForm.createTimeBegin != null">
                AND DATE(f.create_time) &gt;= #{queryForm.createTimeBegin}
            </if>
            <if test="queryForm.createTimeEnd != null">
                AND DATE(f.create_time) &lt;= #{queryForm.createTimeEnd}
            </if>
            <if test="queryForm.keywords != null and queryForm.keywords != ''">
                AND (
                    f.file_name LIKE CONCAT('%', #{queryForm.keywords}, '%')
                    OR f.description LIKE CONCAT('%', #{queryForm.keywords}, '%')
                    OR f.tags LIKE CONCAT('%', #{queryForm.keywords}, '%')
                )
            </if>
        </where>
        ORDER BY
            f.create_time DESC
    </select>

    <!-- 根据文件夹ID获取文件列表 -->
    <select id="getFilesByFolderId" resultType="com.sprixin.sa.base.module.support.knowledgebase.domain.vo.KnowledgeFileVO">
        SELECT
            f.file_id as fileId,
            f.folder_id as folderId,
            fold.folder_name as folderName,
            f.file_name as fileName,
            f.file_key as fileKey,
            f.file_size as fileSize,
            f.file_type as fileType,
            f.description,
            f.tags,
            f.creator_id as creatorId,
            f.creator_name as creatorName,
            f.create_time as createTime,
            f.update_time as updateTime
        FROM
            t_knowledge_file f
        LEFT JOIN
            t_knowledge_folder fold ON f.folder_id = fold.folder_id
        WHERE
            f.folder_id = #{folderId}
        ORDER BY
            f.create_time DESC
    </select>

    <!-- 获取文件详情 -->
    <select id="getFileDetail" resultType="com.sprixin.sa.base.module.support.knowledgebase.domain.vo.KnowledgeFileVO">
        SELECT
            f.file_id as fileId,
            f.folder_id as folderId,
            fold.folder_name as folderName,
            f.file_name as fileName,
            f.file_key as fileKey,
            f.file_size as fileSize,
            f.file_type as fileType,
            f.description,
            f.tags,
            f.creator_id as creatorId,
            f.creator_name as creatorName,
            f.create_time as createTime,
            f.update_time as updateTime
        FROM
            t_knowledge_file f
        LEFT JOIN
            t_knowledge_folder fold ON f.folder_id = fold.folder_id
        WHERE
            f.file_id = #{fileId}
    </select>

</mapper> 