<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.sa.base.module.support.knowledgebase.dao.KnowledgeFolderDao">

    <!-- 获取文件夹树 -->
    <select id="getFolderTree" resultType="com.sprixin.sa.base.module.support.knowledgebase.domain.vo.KnowledgeFolderVO">
        SELECT
            folder_id as folderId,
            folder_name as folderName,
            parent_id as parentId,
            sort,
            description,
            creator_id as creatorId,
            creator_name as creator<PERSON><PERSON>,
            create_time as createTime,
            update_time as updateTime
        FROM
            t_knowledge_folder
        ORDER BY
            parent_id, sort
    </select>

    <!-- 根据父ID获取文件夹列表 -->
    <select id="getFoldersByParentId" resultType="com.sprixin.sa.base.module.support.knowledgebase.domain.vo.KnowledgeFolderVO">
        SELECT
            folder_id as folderId,
            folder_name as folderName,
            parent_id as parentId,
            sort,
            description,
            creator_id as creatorId,
            creator_name as creatorName,
            create_time as createTime,
            update_time as updateTime
        FROM
            t_knowledge_folder
        WHERE
            parent_id = #{parentId}
        ORDER BY
            sort
    </select>

    <!-- 获取文件夹详情 -->
    <select id="getFolderDetail" resultType="com.sprixin.sa.base.module.support.knowledgebase.domain.vo.KnowledgeFolderVO">
        SELECT
            folder_id as folderId,
            folder_name as folderName,
            parent_id as parentId,
            sort,
            description,
            creator_id as creatorId,
            creator_name as creatorName,
            create_time as createTime,
            update_time as updateTime
        FROM
            t_knowledge_folder
        WHERE
            folder_id = #{folderId}
    </select>

</mapper> 