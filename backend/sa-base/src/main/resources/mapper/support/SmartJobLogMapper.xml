<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.sa.base.module.support.job.repository.SmartJobLogDao">

    <!-- 定时任务-执行记录-分页查询 -->
    <select id="query" resultType="com.sprixin.sa.base.module.support.job.api.domain.SmartJobLogVO">
        SELECT *
        FROM t_smart_job_log
        <where>
            <if test="query.searchWord != null and query.searchWord != ''">
                AND ( INSTR(job_name,#{query.searchWord})
                OR INSTR(param,#{query.searchWord})
                OR INSTR(execute_result,#{query.searchWord})
                OR INSTR(create_name,#{query.searchWord})
                )
            </if>
            <if test="query.jobId != null">
                AND job_id = #{query.jobId}
            </if>
            <if test="query.successFlag != null">
                AND success_flag = #{query.successFlag}
            </if>
            <if test="query.startTime != null">
                AND DATE_FORMAT(execute_start_time, '%Y-%m-%d') &gt;= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                AND DATE_FORMAT(execute_start_time, '%Y-%m-%d') &lt;= #{query.endTime}
            </if>
        </where>
        <if test="query.sortItemList == null or query.sortItemList.size == 0">
            ORDER BY log_id DESC
        </if>
    </select>

</mapper>
