{"name": "sprixin-ai-baojia-system", "version": "3.0.0", "license": "MIT", "scripts": {"_install": "npm install", "dev": "vite", "build": "vite build --mode production"}, "type": "module", "dependencies": {"@ant-design/icons-vue": "7.0.1", "@univerjs/presets": "0.10.1", "@wangeditor-next/editor": "5.6.34", "@wangeditor-next/editor-for-vue": "5.1.14", "ant-design-vue": "4.2.6", "axios": "1.9.0", "clipboard": "2.0.11", "crypto-js": "4.2.0", "dayjs": "1.11.13", "decimal.js": "10.5.0", "diff": "5.2.0", "diff2html": "3.4.51", "dompurify": "^3.0.8", "echarts": "^5.6.0", "exceljs": "4.4.0", "file-saver": "2.0.5", "highlight.js": "^11.9.0", "lodash": "4.17.21", "lunar-javascript": "1.7.3", "markdown-it": "^13.0.2", "marked": "^5.0.5", "mitt": "3.0.1", "nprogress": "0.2.0", "pinia": "3.0.2", "sm-crypto": "0.3.13", "sortablejs": "1.15.6", "ua-parser-js": "1.0.35", "uuid": "11.1.0", "v-viewer": "1.6.4", "vue": "3.5.14", "vue-echarts": "^7.0.3", "vue-i18n": "11.1.3", "vue-router": "4.5.1", "vue3-json-viewer": "2.3.0"}, "devDependencies": {"@vitejs/plugin-vue": "5.2.4", "less": "4.3.0", "less-loader": "12.3.0", "prettier": "3.6.2", "rimraf": "5.0.10", "terser": "5.39.2", "vite": "^6.3.5"}, "engines": {"node": ">=18"}}