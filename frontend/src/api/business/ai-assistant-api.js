import { request, postRequest, getRequest } from '/@/lib/axios';
import axios from 'axios';
import { localRead } from '/@/utils/local-util';
import LocalStorageKeyConst from '/@/constants/local-storage-key-const.js';
import { message } from 'ant-design-vue';

// API URL前缀 - 修改为正确的路径
const API_PREFIX = '/sa-admin/ai/assistant';

/**
 * AI助手API
 */
export default {
  /**
   * 发送聊天消息
   * @param {Object} data 消息数据
   * @param {string} data.sessionId 会话ID
   * @param {string} data.message 消息内容
   * @param {Object} data.parameters 模型参数
   * @returns {Promise} 请求Promise
   */
  sendMessage(data) {
    // 添加返回HTML格式的参数
    if (!data.parameters) {
      data.parameters = {};
    }
    data.parameters.returnHtml = true;
    
    // 使用项目封装的postRequest
    return postRequest(`${API_PREFIX}/chat`, data);
  },
  
  /**
   * 发送聊天消息（流式响应）
   * @param {Object} data 消息数据
   * @param {string} data.sessionId 会话ID
   * @param {string} data.message 消息内容
   * @param {Object} data.parameters 模型参数
   * @param {Object} callbacks 回调函数对象
   * @param {Function} callbacks.onMessage 接收消息的回调
   * @param {Function} callbacks.onError 错误回调
   * @param {Function} callbacks.onClose 关闭回调
   */
  sendMessageStream(data, callbacks) {
    // 添加返回HTML格式的参数
    if (!data.parameters) {
      data.parameters = {};
    }
    data.parameters.returnHtml = true;
    
    const token = localRead(LocalStorageKeyConst.USER_TOKEN);
    const baseUrl = import.meta.env.VITE_APP_API_URL || '';
    
    // 创建一个AbortController，用于取消请求
    const controller = new AbortController();
    const { signal } = controller;
    
    // 完整的API URL
    const url = `${baseUrl}${API_PREFIX}/chat/stream`;
    
    // 使用fetch API发送POST请求并处理SSE响应
    fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        'Accept': 'text/event-stream'
      },
      body: JSON.stringify(data),
      signal,
      credentials: 'include'
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      // 获取响应的reader
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      
      // 读取流式数据
      function readStream() {
        reader.read().then(({ done, value }) => {
          if (done) {
            // 流结束
            if (callbacks.onClose) {
              callbacks.onClose();
            }
            return;
          }
          
          // 解码二进制数据
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;
          
          // 处理SSE格式数据
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // 最后一行可能不完整，保留到下次处理
          
          for (const line of lines) {
            if (line.trim() === '') continue;
            
            if (line.startsWith('data:')) {
              const eventData = line.substring(5).trim();
              console.log('收到SSE事件数据:', eventData);
              
              // 处理不同类型的事件
              if (eventData === '[DONE]') {
                if (callbacks.onClose) {
                  callbacks.onClose();
                }
              } else {
                // 检查是否是纯文本内容（非JSON）
                // 增强对中文字符的检测
                const hasChinese = /[\u4e00-\u9fa5]/.test(eventData);
                const startsWithPunctuation = /^[，。？！；：""''「」【】、]/.test(eventData);
                const isPlainText = hasChinese || 
                                   startsWithPunctuation || 
                                   eventData.startsWith('"') || 
                                   eventData.startsWith('自然') || 
                                   eventData.startsWith('语言') || 
                                   eventData.startsWith('对话');
                
                if (isPlainText && !eventData.startsWith('{') && !eventData.startsWith('[')) {
                  // 如果是纯文本内容，直接作为文本处理
                  console.log('检测到纯文本内容，直接处理:', eventData);
                  if (callbacks.onMessage) {
                    // 检查是否包含HTML标签
                    if (/<[a-z][\s\S]*>/i.test(eventData)) {
                      callbacks.onMessage({
                        html: eventData
                      });
                    } else {
                      callbacks.onMessage(eventData);
                    }
                  }
                  continue;
                }
                
                try {
                  // 尝试解析JSON数据
                  const parsedData = JSON.parse(eventData);
                  console.log('解析后的数据:', parsedData);
                  
                  // 处理特定的事件类型
                  if (parsedData.name === 'message' && parsedData.data) {
                    // 如果是后端自定义的message事件
                    if (callbacks.onMessage) {
                      // 确保换行符被正确处理
                      let messageContent = typeof parsedData.data === 'string' ? parsedData.data : JSON.stringify(parsedData.data);
                      
                      // 处理特殊标记，确保它们在前端能被正确识别
                      messageContent = messageContent
                        .replace(/\\:marker/g, ':marker')
                        .replace(/\\:before/g, ':before');
                      
                      // 检查是否包含HTML内容标记
                      if (parsedData.isHtml || (parsedData.data && parsedData.data.isHtml)) {
                        console.log('检测到HTML格式的消息');
                        // 如果是HTML内容，创建一个包含html字段的对象
                        callbacks.onMessage({
                          html: messageContent
                        });
                      } else if (parsedData.isMarkdown || (parsedData.data && parsedData.data.isMarkdown)) {
                        console.log('检测到Markdown格式的消息');
                        // 如果是Markdown内容，创建一个包含markdown字段的对象
                        callbacks.onMessage({
                          markdown: messageContent
                        });
                      } else {
                        callbacks.onMessage(messageContent);
                      }
                    }
                    continue;
                  }
                  
                  // 提取通义千问特定格式的内容
                  let content = '';
                  let isHtml = false;
                  let isMarkdown = false;

                  if (parsedData.choices && parsedData.choices.length > 0) {
                    const choice = parsedData.choices[0];
                    
                    // 检查是否有HTML标记
                    if (choice.delta && choice.delta.html_content) {
                      content = choice.delta.html_content;
                      isHtml = true;
                    } else if (choice.html_content) {
                      content = choice.html_content;
                      isHtml = true;
                    } else if (choice.delta && choice.delta.markdown_content) {
                      content = choice.delta.markdown_content;
                      isMarkdown = true;
                    } else if (choice.markdown_content) {
                      content = choice.markdown_content;
                      isMarkdown = true;
                    } else if (choice.delta && choice.delta.reasoning_content) {
                      content = choice.delta.reasoning_content;
                    } else if (choice.delta && choice.delta.content) {
                      content = choice.delta.content;
                    } else if (choice.text) {
                      content = choice.text;
                    } else if (choice.content) {
                      content = choice.content;
                    }
                  }
                  
                  // 检查是否有HTML格式的响应
                  if (parsedData.html) {
                    content = parsedData.html;
                    isHtml = true;
                  }
                  
                  // 如果成功提取到内容，直接传递内容字符串
                  if (content) {
                    console.log('提取到内容:', content);
                    console.log('AI回复片段内容:', content);
                    if (callbacks.onMessage) {
                      // 确保换行符和特殊标记被正确处理
                      content = content
                        .replace(/\\:marker/g, ':marker')
                        .replace(/\\:before/g, ':before');
                      
                      // 如果是HTML内容，创建一个包含html字段的对象
                      if (isHtml) {
                        callbacks.onMessage({
                          html: content
                        });
                      } else if (isMarkdown) {
                        callbacks.onMessage({
                          markdown: content
                        });
                      } else {
                        callbacks.onMessage(content);
                      }
                    }
                  }
                } catch (e) {
                  console.warn('解析JSON失败:', e, '原始数据:', eventData);
                  // 如果不是有效的JSON，直接作为纯文本处理
                  const textContent = eventData.trim();
                  if (textContent && callbacks.onMessage) {
                    console.log('作为纯文本处理:', textContent);
                    // 检查是否包含HTML标签
                    if (/<[a-z][\s\S]*>/i.test(textContent)) {
                      callbacks.onMessage({
                        html: textContent
                      });
                    } else {
                      callbacks.onMessage(textContent);
                    }
                  }
                }
              }
            }
          }
          
          // 继续读取
          readStream();
        }).catch(error => {
          if (callbacks.onError) {
            callbacks.onError(error);
          }
        });
      }
      
      // 开始读取
      readStream();
    })
    .catch(error => {
      console.error('流式请求错误:', error);
      if (callbacks.onError) {
        callbacks.onError(error);
      }
    });
    
    // 返回控制器，用于取消请求
    return controller;
  },

  /**
   * 获取会话历史记录
   * @param {string} sessionId 会话ID
   * @returns {Promise} 请求Promise
   */
  getSessionHistory(sessionId) {
    return getRequest(`${API_PREFIX}/history/${sessionId}`);
  },
  
  /**
   * 创建新会话
   * @returns {Promise<string>} 返回会话ID
   */
  createSession() {
    return postRequest(`${API_PREFIX}/session`);
  }
}; 