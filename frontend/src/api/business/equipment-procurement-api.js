/*
 *  设备采购
 */

import { getRequest, postRequest } from '/@/lib/axios';

export const equipmentProcurementApi = {
    /**
     * 查询设备采购列表
     */
    queryEquipmentProcurement: (params) => {
        return postRequest('/api/equipmentProcurement/query', params);
    },
    /**
     * 添加设备采购
     */
    addEquipmentProcurement: (params) => {
        return postRequest('/api/equipmentProcurement/add', params);
    },
    /**
     * 更新设备采购
     */
    updateEquipmentProcurement: (params) => {
        return postRequest('/api/equipmentProcurement/update', params);
    },
    /**
     * 删除设备采购
     */
    deleteEquipmentProcurement: (id) => {
        return getRequest(`/api/equipmentProcurement/delete/${id}`);
    },
    /**
     * 批量删除设备采购
     */
    batchDeleteEquipmentProcurement: (idList) => {
        return postRequest('/api/equipmentProcurement/batch/delete', idList);
    },
    /**
     * 获取设备采购详情
     */
    getEquipmentProcurementDetail: (id) => {
        return getRequest(`/api/equipmentProcurement/get/${id}`);
    }
}; 