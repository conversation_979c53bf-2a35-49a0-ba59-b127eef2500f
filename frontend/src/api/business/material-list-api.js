/*
 *  物料清单
 */

import { getRequest, postRequest } from '/@/lib/axios';

export const materialListApi = {
    /**
     * 查询物料清单列表
     */
    queryMaterialList: (params) => {
        return postRequest('/materialList/query', params);
    },
    /**
     * 添加物料清单
     */
    addMaterialList: (params) => {
        return postRequest('/materialList/add', params);
    },
    /**
     * 更新物料清单
     */
    updateMaterialList: (params) => {
        return postRequest('/materialList/update', params);
    },
    /**
     * 删除物料清单
     */
    deleteMaterialList: (id) => {
        return getRequest(`/materialList/delete/${id}`);
    },
    /**
     * 批量删除物料清单
     */
    batchDeleteMaterialList: (idList) => {
        return postRequest('/materialList/batch/delete', idList);
    },
    /**
     * 获取物料清单详情
     */
    getMaterialListDetail: (id) => {
        return getRequest(`/materialList/get/${id}`);
    },
    /**
     * 获取所有物料清单列表（用于下拉选择）
     */
    queryAllMaterials: () => {
        return getRequest('/materialList/queryAll');
    }
};