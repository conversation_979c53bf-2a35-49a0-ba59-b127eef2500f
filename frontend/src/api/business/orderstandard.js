import { postRequest, getRequest } from '/@/lib/axios';

/**
 * 设备下单标准API
 */
export const orderStandardApi = {
  /**
   * 查询设备下单标准列表
   * @param {Object} param 查询参数
   * @returns {Promise} 查询结果
   */
  queryOrderStandard: (param) => {
    return postRequest('/orderStandard/query', param);
  },

  /**
   * 添加设备下单标准
   * @param {Object} param 添加参数
   * @returns {Promise} 添加结果
   */
  addOrderStandard: (param) => {
    return postRequest('/orderStandard/add', param);
  },

  /**
   * 更新设备下单标准
   * @param {Object} param 更新参数
   * @returns {Promise} 更新结果
   */
  updateOrderStandard: (param) => {
    return postRequest('/orderStandard/update', param);
  },

  /**
   * 删除设备下单标准
   * @param {number} id 设备下单标准ID
   * @returns {Promise} 删除结果
   */
  deleteOrderStandard: (id) => {
    return getRequest(`/orderStandard/delete/${id}`);
  },

  /**
   * 批量删除设备下单标准
   * @param {Array} idList 设备下单标准ID列表
   * @returns {Promise} 删除结果
   */
  batchDeleteOrderStandard: (idList) => {
    return postRequest('/orderStandard/batchDelete', idList);
  },

  /**
   * 获取设备下单标准详情
   * @param {number} id 设备下单标准ID
   * @returns {Promise} 详情结果
   */
  getOrderStandardDetail: (id) => {
    return getRequest(`/orderStandard/get/${id}`);
  },

  /**
   * 获取设备下单标准总数
   * @returns {Promise} 总数结果
   */
  getOrderStandardCount: () => {
    return getRequest('/orderStandard/count');
  },
}; 