/* 产品类型 */
import { getRequest, postRequest } from '/@/lib/axios';

export const productTypeApi = {
    /**
     * 查询产品类型列表
     */
    queryAllProductType: () => {
        return getRequest('/productType/listAll');
    },

    /**
     * 查询产品类型树形列表
     */
    queryProductTypeTree: () => {
        return getRequest('/productType/treeList');
    },

    /**
     * 获取产品类型列表（支持查询参数）
     */
    getProductTypes: (params) => {
        return postRequest('/productType/query', params);
    },

    /**
     * 获取所有启用的产品类型（用于下拉选择）
     */
    getAllEnabled: () => {
        return getRequest('/productType/all-enabled');
    },

    /**
     * 添加产品类型
     */
    addProductType: (param) => {
        return postRequest('/productType/add', param);
    },

    /**
     * 更新产品类型信息
     */
    updateProductType: (param) => {
        return postRequest('/productType/update', param);
    },

    /**
     * 删除
     */
    deleteProductType: (id) => {
        return getRequest(`/productType/delete/${id}`);
    }
}; 