/*
 *  销售管理
 */

import { getRequest, postRequest } from '/@/lib/axios';

export const salesInfoApi = {
    /**
     * 查询销售信息列表
     */
    querySalesInfo: (params) => {
        return postRequest('/salesInfo/query', params);
    },
    /**
     * 添加销售信息
     */
    addSalesInfo: (params) => {
        return postRequest('/salesInfo/add', params);
    },
    /**
     * 更新销售信息
     */
    updateSalesInfo: (params) => {
        return postRequest('/salesInfo/update', params);
    },
    /**
     * 删除销售信息
     */
    deleteSalesInfo: (id) => {
        return getRequest(`/salesInfo/delete/${id}`);
    },
    /**
     * 批量删除销售信息
     */
    batchDeleteSalesInfo: (idList) => {
        return postRequest('/salesInfo/batch/delete', idList);
    },
    /**
     * 获取销售信息详情
     */
    getSalesInfoDetail: (id) => {
        return getRequest(`/salesInfo/get/${id}`);
    },
    /**
     * 导出销售信息
     */
    exportSalesInfo: (params) => {
        return postRequest('/salesInfo/export', params, {
            responseType: 'blob'
        });
    },
    /**
     * 获取所有销售人员列表（用于下拉选择）
     */
    queryAllSales: () => {
        return getRequest('/salesInfo/queryAll');
    }
};