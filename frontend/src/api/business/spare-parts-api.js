/*
 *  备品备件
 */

import { getRequest, postRequest } from '/@/lib/axios';

export const sparePartsApi = {
    /**
     * 查询备品备件列表
     */
    querySpareParts: (params) => {
        return postRequest('/spareParts/query', params);
    },
    /**
     * 添加备品备件
     */
    addSpareParts: (params) => {
        return postRequest('/spareParts/add', params);
    },
    /**
     * 更新备品备件
     */
    updateSpareParts: (params) => {
        return postRequest('/spareParts/update', params);
    },
    /**
     * 删除备品备件
     */
    deleteSpareParts: (id) => {
        return getRequest(`/spareParts/delete/${id}`);
    },
    /**
     * 批量删除备品备件
     */
    batchDeleteSpareParts: (idList) => {
        return postRequest('/spareParts/batch/delete', idList);
    },
    /**
     * 获取备品备件详情
     */
    getSparePartsDetail: (id) => {
        return getRequest(`/spareParts/get/${id}`);
    }
}; 