import { postRequest, getRequest } from '/@/lib/axios';

/**
 * 技术方案API
 */
export const techSolutionApi = {
  /**
   * 查询技术方案列表
   * @param {Object} param 查询参数
   * @returns {Promise} 查询结果
   */
  queryTechSolution: (param) => {
    return postRequest('/techSolution/query', param);
  },

  /**
   * 生成技术方案
   * @param {Object} param 生成参数，包含提示词、API Key等
   * @returns {Promise} 生成结果
   */
  generateTechSolution: (param) => {
    return postRequest('/techSolution/generate', param);
  },

  /**
   * 获取技术方案详情
   * @param {number} id 技术方案ID
   * @returns {Promise} 详情结果
   */
  getTechSolutionDetail: (id) => {
    return getRequest(`/techSolution/get/${id}`);
  },

  /**
   * 删除技术方案
   * @param {number} id 技术方案ID
   * @returns {Promise} 删除结果
   */
  deleteTechSolution: (id) => {
    return getRequest(`/techSolution/delete/${id}`);
  },

  /**
   * 批量删除技术方案
   * @param {Array} idList 技术方案ID列表
   * @returns {Promise} 删除结果
   */
  batchDeleteTechSolution: (idList) => {
    return postRequest('/techSolution/batchDelete', idList);
  },

  /**
   * 获取技术方案生成状态
   * @param {number} id 技术方案ID
   * @returns {Promise} 状态结果
   */
  getTechSolutionStatus: (id) => {
    return getRequest(`/techSolution/status/${id}`);
  },

  /**
   * 更新技术方案内容
   * @param {number} id 技术方案ID
   * @param {string} content 更新后的内容
   * @returns {Promise} 更新结果
   */
  updateTechSolutionContent: (id, content) => {
    return postRequest(`/techSolution/updateContent`, {
      id,
      content
    });
  }
}; 