import { getRequest, postRequest } from '/@/lib/axios';

export const templateApi = {
    /**
     * 分页查询模板及报价
     */
    query: (params) => {
        return postRequest('/baojia/content/query', params);
    },
    /**
     * 新增模板或报价
     */
    add: (params) => {
        return postRequest('/baojia/content/add', params);
    },
    /**
     * 更新模板或报价
     */
    update: (params) => {
        return postRequest('/baojia/content/update', params);
    },
    /**
     * 批量删除模板或报价
     */
    delete: (idList) => {
        return postRequest('/baojia/content/delete', idList);
    },
    /**
     * 获取模板或报价详情
     */
    detail: (id) => {
        return getRequest(`/baojia/content/detail/${id}`);
    },
    /**
     * 切换禁用/启用状态
     */
    updateDisabled: (id) => {
        return getRequest(`/baojia/content/update/disabled/${id}`);
    }
};
