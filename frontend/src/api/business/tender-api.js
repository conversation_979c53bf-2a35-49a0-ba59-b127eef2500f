import { postRequest, getRequest } from '/@/lib/axios';

/**
 * 标书API
 */
export const tenderApi = {
  /**
   * 查询标书列表
   * @param {Object} param 查询参数
   * @returns {Promise} 查询结果
   */
  queryTender: (param) => {
    return postRequest('/tenderDocument/query', param);
  },

  /**
   * 生成标书
   * @param {Object} param 生成参数，包含提示词、API Key等
   * @returns {Promise} 生成结果
   */
  generateTender: (param) => {
    return postRequest('/tenderDocument/generate', param);
  },

  /**
   * 获取标书详情
   * @param {number} id 标书ID
   * @returns {Promise} 详情结果
   */
  getTenderDetail: (id) => {
    return getRequest(`/tenderDocument/get/${id}`);
  },

  /**
   * 更新标书
   * @param {Object} param 更新参数
   * @returns {Promise} 更新结果
   */
  updateTender: (param) => {
    return postRequest('/tenderDocument/update', param);
  },

  /**
   * 删除标书
   * @param {number} id 标书ID
   * @returns {Promise} 删除结果
   */
  deleteTender: (id) => {
    return getRequest(`/tenderDocument/delete/${id}`);
  },

  /**
   * 批量删除标书
   * @param {Array} idList ID列表
   * @returns {Promise} 删除结果
   */
  batchDeleteTender: (idList) => {
    return postRequest('/tenderDocument/batchDelete', idList);
  },

  /**
   * 获取标书生成状态
   * @param {number} id 标书ID
   * @returns {Promise} 状态结果
   */
  getTenderStatus: (id) => {
    return getRequest(`/tenderDocument/status/${id}`);
  },

  /**
   * 更新标书内容
   * @param {Object} param 更新参数，包含ID和内容
   * @returns {Promise} 更新结果
   */
  updateTenderContent: (param) => {
    return postRequest('/tenderDocument/updateContent', param);
  }
};
