/* 文档分析 */
import { postRequest, getRequest, deleteRequest, getDownload } from '/@/lib/axios';

export const documentAnalysisApi = {
    // 分析文档
    analyzeDocument: (param) => {
        return postRequest('/support/document/analyze', param);
    },
    
    // 获取分析结果
    getAnalysisResult: (id) => {
        return getRequest(`/support/document/analysis/${id}`);
    },
    
    // 更新分析结果
    updateAnalysisResult: (id, analysisResult) => {
        return postRequest(`/support/document/analysis/${id}`, analysisResult);
    },

    // 查询分析结果列表
    queryAnalysisResults: (params) => {
        return postRequest('/support/document/analysis/query', params);
    },

    // 删除分析结果
    deleteAnalysisResult: (id) => {
        return deleteRequest(`/support/document/analysis/${id}`);
    },
    
    // 导出报价单
    exportQuotation: (id) => {
        const params = { t: Date.now() }; // 防止缓存
        return getDownload(`/support/document/analysis/export/${id}`, params);
    }
}; 