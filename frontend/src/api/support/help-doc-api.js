/* 帮助文档 */
import { postRequest, getRequest } from '/@/lib/axios';

export const helpDocApi = {
    // 【管理】帮助文档-分页查询
    query: (param) => {
        return postRequest('/support/helpDoc/query', param);
    },

    //【管理】帮助文档-更新
    update: (param) => {
        return postRequest('/support/helpDoc/update', param);
    },

    // 【管理】帮助文档-添加
    add: (param) => {
        return postRequest('/support/helpDoc/add', param);
    },

    //【管理】帮助文档-删除
    delete: (helpDocId) => {
        return getRequest(`/support/helpDoc/delete/${helpDocId}`);
    },

    //【管理】帮助文档-获取详情
    getDetail: (helpDocId) => {
        return getRequest(`/support/helpDoc/getDetail/${helpDocId}`);
    },

    //【管理】帮助文档-根据关联id查询
    queryHelpDocByRelationId: (relationId) => {
        return getRequest(`/support/helpDoc/queryHelpDocByRelationId/${relationId}`);
    },

    //----------------------- 用户相关 --------------------------------

    //【用户】帮助文档-查询全部
    getAllHelpDocList() {
        return getRequest('/support/helpDoc/user/queryAllHelpDocList');
    },

    //【用户】帮助文档-查询全部
    view(helpDocId) {
        return new Promise((resolve, reject) => {
            getRequest(`/support/helpDoc/user/view/${helpDocId}`)
                .then(response => {
                    resolve(response);
                })
                .catch(error => {
                    // 如果后端返回"帮助文档不存在"的错误，将其转换为可处理的错误对象
                    if (error.data && error.data.msg === "帮助文档不存在") {
                        reject({ message: "帮助文档不存在", code: 404 });
                    } else {
                        reject(error);
                    }
                });
        });
    },

    //【用户】帮助文档-查询 查看记录
    queryViewRecord(param) {
        return postRequest('/support/helpDoc/user/queryViewRecord', param);
    }
};
