/* 知识库文件 */
import { getRequest, postRequest, getDownload } from '/@/lib/axios';

export const knowledgeFileApi = {
    /**
     * 分页查询
     */
    queryPage: (param) => {
        return postRequest('/knowledge/file/query', param);
    },

    /**
     * 根据文件夹ID获取文件列表
     */
    getFilesByFolderId: (folderId) => {
        return getRequest(`/knowledge/file/list/${folderId}`);
    },

    /**
     * 获取文件详情
     */
    getFileDetail: (fileId) => {
        return getRequest(`/knowledge/file/get/${fileId}`);
    },

    /**
     * 添加文件
     */
    addFile: (param) => {
        return postRequest('/knowledge/file/add', param);
    },

    /**
     * 删除文件
     */
    deleteFile: (fileId) => {
        return getRequest(`/knowledge/file/delete/${fileId}`);
    },

    /**
     * 移动文件
     */
    moveFile: (fileId, targetFolderId) => {
        return postRequest(`/knowledge/file/move/${fileId}/${targetFolderId}`);
    },
    
    /**
     * 整合式文件上传
     */
    uploadFile: (file, folderId, description, tags) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('folderId', folderId);
        if (description) formData.append('description', description);
        if (tags) formData.append('tags', tags);
        
        return postRequest('/knowledge/file/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
    }
}; 