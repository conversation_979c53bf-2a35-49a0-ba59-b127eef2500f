/* 知识库文件夹 */
import { getRequest, postRequest } from '/@/lib/axios';

export const knowledgeFolderApi = {
    /**
     * 获取文件夹树
     */
    getFolderTree: async () => {
        console.log('调用知识库文件夹树API');
        try {
            const response = await getRequest('/knowledge/folder/tree');
            console.log('知识库文件夹树API返回:', response);
            return response;
        } catch (error) {
            console.error('知识库文件夹树API错误:', error);
            throw error;
        }
    },

    /**
     * 获取文件夹详情
     */
    getFolderDetail: (folderId) => {
        return getRequest(`/knowledge/folder/get/${folderId}`);
    },

    /**
     * 添加文件夹
     */
    addFolder: (param) => {
        return postRequest('/knowledge/folder/add', param);
    },

    /**
     * 更新文件夹
     */
    updateFolder: (param) => {
        return postRequest('/knowledge/folder/update', param);
    },

    /**
     * 删除文件夹
     */
    deleteFolder: (folderId) => {
        return getRequest(`/knowledge/folder/delete/${folderId}`);
    }
}; 