<template>
  <div class="ai-assistant">
    <!-- 头部 -->
    <div class="header">
      <h2>AI智能助手</h2>
      <div class="header-actions">
        <!-- <a-tag color="blue" size="small" style="margin-right: 8px;">
          {{ apiMode === 'real' ? '真实API' : '模拟模式' }}
        </a-tag> -->
        <a-button @click="clearMessages" size="small">清空</a-button>
      </div>
    </div>
    
    <!-- 消息列表 -->
    <div class="messages" ref="messagesRef">
      <div v-if="messages.length === 0" class="welcome">
        <div class="welcome-icon">🤖</div>
        <h3>欢迎使用AI助手</h3>
        <p>请输入您的问题，我会尽力为您解答</p>
      </div>
      
      <div v-for="(msg, index) in messages" :key="index" :class="['message', msg.role]">
        <div class="avatar">
          {{ msg.role === 'user' ? '👤' : '🤖' }}
        </div>
        <div class="content">
          <div v-if="msg.role === 'user'" class="text">{{ msg.content }}</div>
          <div v-else class="ai-response">
            <div class="markdown" v-html="renderMarkdown(msg.content)"></div>
            <div class="message-actions">
              <a-button
                type="text"
                size="small"
                @click="copyMessage(msg.content)"
                class="copy-btn"
                title="复制内容"
              >
                <template #icon>
                  <CopyOutlined />
                </template>
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- AI正在回复的等待状态 -->
      <div v-if="loading" class="message assistant">
        <div class="avatar">🤖</div>
        <div class="content">
          <div class="ai-response">
            <div class="typing-indicator">
              <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <span class="typing-text">AI正在思考中...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 输入框 -->
    <div class="input-area">
      <a-textarea 
        v-model:value="inputText"
        :rows="3"
        placeholder="请输入您的问题..."
        @keydown.ctrl.enter="sendMessage"
        :disabled="loading"
      />
      <div class="input-actions">
        <a-button 
          type="primary" 
          @click="sendMessage"
          :loading="loading"
          :disabled="!inputText.trim()"
        >
          发送 (Ctrl+Enter)
        </a-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { marked } from 'marked'
import { CopyOutlined } from '@ant-design/icons-vue'

export default {
  name: 'SimpleAiAssistant',
  components: {
    CopyOutlined
  },
  setup() {
    const messages = ref([])
    const inputText = ref('')
    const loading = ref(false)
    const messagesRef = ref(null)
    const apiMode = ref('real') // 'real' 或 'mock'

    // 渲染Markdown
    const renderMarkdown = (content) => {
      try {
        return marked(content, {
          breaks: true,
          gfm: true
        })
      } catch (error) {
        console.error('Markdown渲染错误:', error)
        return content
      }
    }

    // 模拟AI回复
    const getMockAiResponse = (userMessage) => {
      const responses = [
        `您好！我是AI智能助手，很高兴为您服务。\n\n关于您的问题"${userMessage}"，我来为您详细解答：\n\n这是一个很好的问题。根据我的理解，我建议您可以从以下几个方面来考虑：\n\n1. **分析问题背景**：首先需要了解问题的具体情况\n2. **制定解决方案**：基于分析结果制定相应的解决策略\n3. **实施与监控**：执行方案并持续监控效果\n\n希望这些建议对您有所帮助！如果您还有其他问题，请随时告诉我。`,
        `感谢您的提问！\n\n针对"${userMessage}"这个问题，我为您提供以下信息：\n\n**核心要点：**\n- 这是一个常见且重要的问题\n- 需要综合考虑多个因素\n- 建议采用系统性的方法来解决\n\n**具体建议：**\n1. 收集相关信息和数据\n2. 分析现状和问题根源\n3. 制定可行的解决方案\n4. 逐步实施并调整优化\n\n如果您需要更详细的指导，请告诉我具体的应用场景，我会为您提供更有针对性的建议。`,
        `很高兴回答您的问题！\n\n关于"${userMessage}"，我的理解是：\n\n这个问题涉及到多个层面的考虑。让我为您详细分析：\n\n**问题分析：**\n- 当前情况评估\n- 潜在影响因素\n- 可能的解决路径\n\n**推荐方案：**\n✅ 短期措施：立即可以采取的行动\n✅ 中期规划：需要一定时间准备的方案\n✅ 长期目标：持续改进和优化的方向\n\n请告诉我您更关注哪个方面，我可以为您提供更深入的分析和建议。`
      ]

      return responses[Math.floor(Math.random() * responses.length)]
    }

    // 发送消息
    const sendMessage = async () => {
      if (!inputText.value.trim() || loading.value) return

      const userMessage = inputText.value.trim()
      inputText.value = ''

      // 添加用户消息
      messages.value.push({
        role: 'user',
        content: userMessage,
        timestamp: new Date()
      })

      // 滚动到底部
      await nextTick()
      scrollToBottom()

      // 发送请求
      loading.value = true
      try {
        console.log('发送AI请求:', userMessage)

        // 首先尝试真实API
        const requestBody = {
          message: userMessage,
          sessionId: 'simple-chat-' + Date.now(),
          parameters: {
            temperature: 0.7,
            maxTokens: 2000,
            useStreamResponse: false
          }
        }

        console.log('请求体:', requestBody)

        // 尝试获取认证token
        const token = localStorage.getItem('Authorization') || sessionStorage.getItem('Authorization')
        const headers = {
          'Content-Type': 'application/json'
        }

        // 如果有token，添加到请求头
        if (token) {
          headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`
        }

        let aiResponse = null

        try {
          const response = await fetch('/sa-admin/ai/assistant/chat', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(requestBody)
          })

          console.log('响应状态:', response.status)

          if (response.ok) {
            const data = await response.json()
            console.log('AI响应:', data)
            aiResponse = data.message
            apiMode.value = 'real'
          } else {
            throw new Error(`HTTP ${response.status}`)
          }
        } catch (apiError) {
          console.warn('API调用失败，使用模拟响应:', apiError.message)
          // 使用模拟响应
          aiResponse = getMockAiResponse(userMessage)
          apiMode.value = 'mock'
        }

        // 添加AI回复
        messages.value.push({
          role: 'assistant',
          content: aiResponse || '抱歉，我无法回答这个问题。',
          timestamp: new Date()
        })

        await nextTick()
        scrollToBottom()

      } catch (error) {
        console.error('发送消息失败:', error)
        message.error('发送失败，使用模拟模式')

        // 使用模拟响应作为后备
        messages.value.push({
          role: 'assistant',
          content: getMockAiResponse(userMessage),
          timestamp: new Date()
        })
      } finally {
        loading.value = false
        await nextTick()
        scrollToBottom()
      }
    }

    // 清空消息
    const clearMessages = () => {
      messages.value = []
      message.success('对话已清空')
    }

    // 复制消息内容
    const copyMessage = (content) => {
      if (!content) return

      navigator.clipboard.writeText(content)
        .then(() => {
          message.success('内容已复制到剪贴板')
        })
        .catch(() => {
          // 如果navigator.clipboard API失败，使用备用方法
          const textarea = document.createElement('textarea')
          textarea.value = content
          textarea.style.position = 'fixed'
          textarea.style.opacity = '0'
          document.body.appendChild(textarea)
          textarea.select()

          try {
            document.execCommand('copy')
            message.success('内容已复制到剪贴板')
          } catch (err) {
            message.error('复制失败，请手动复制')
          } finally {
            document.body.removeChild(textarea)
          }
        })
    }

    // 滚动到底部
    const scrollToBottom = () => {
      if (messagesRef.value) {
        messagesRef.value.scrollTop = messagesRef.value.scrollHeight
      }
    }

    return {
      messages,
      inputText,
      loading,
      messagesRef,
      apiMode,
      renderMarkdown,
      sendMessage,
      clearMessages,
      copyMessage
    }
  }
}
</script>

<style scoped>
.ai-assistant {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.header h2 {
  margin: 0;
  color: #1890ff;
  font-size: 18px;
}

.messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  max-height: 500px;
}

.welcome {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.welcome-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.welcome h3 {
  margin: 16px 0 8px;
  color: #333;
}

.message {
  display: flex;
  margin-bottom: 20px;
  align-items: flex-start;
}

.message.user {
  flex-direction: row-reverse;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin: 0 12px;
  flex-shrink: 0;
}

.content {
  max-width: 70%;
}

.message.user .content {
  color: white;
}

.text {
  padding: 12px 16px;
  border-radius: 12px;
  background: #f5f5f5;
}

.message.user .text {
  background: #1890ff;
}

.text {
  line-height: 1.6;
  word-break: break-word;
}

/* AI回复相关样式 */
.ai-response {
  position: relative;
  background: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  padding: 16px;
}

.ai-response:hover .message-actions {
  opacity: 1;
}

.message-actions {
  position: absolute;
  bottom: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.copy-btn {
  color: #666;
  border: none;
  box-shadow: none;
  padding: 4px 6px;
  height: auto;
  min-height: auto;
}

.copy-btn:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

/* 等待状态样式 */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 0;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #1890ff;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-text {
  color: #666;
  font-size: 14px;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.input-area {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

/* Markdown样式 */
.markdown {
  line-height: 1.6;
  color: #333;
}

.markdown :deep(h1),
.markdown :deep(h2),
.markdown :deep(h3),
.markdown :deep(h4),
.markdown :deep(h5),
.markdown :deep(h6) {
  margin: 16px 0 8px;
  color: #333;
}

.markdown :deep(p) {
  margin: 8px 0;
}

.markdown :deep(ul),
.markdown :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.markdown :deep(li) {
  margin: 4px 0;
}

.markdown :deep(code) {
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
}

.markdown :deep(pre) {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 12px 0;
}

.markdown :deep(blockquote) {
  border-left: 4px solid #ddd;
  padding-left: 12px;
  margin: 12px 0;
  color: #666;
}
</style>
