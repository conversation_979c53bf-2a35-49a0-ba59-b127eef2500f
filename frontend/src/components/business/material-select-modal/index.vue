<!--
  * 物料选择弹窗组件
-->
<template>
    <a-modal 
        v-model:open="visible" 
        :width="1200" 
        title="选择物料" 
        @cancel="closeModal" 
        @ok="onSelectMaterial"
        :confirmLoading="confirmLoading"
    >
        <!-- 筛选区域 -->
        <a-form class="smart-query-form" style="margin-bottom: 16px;">
            <a-row class="smart-query-form-row" :gutter="16">
                <a-col :span="6">
                    <a-form-item label="名称" class="smart-query-form-item">
                        <a-input 
                            v-model:value="filterParams.materialName" 
                            placeholder="物料名称" 
                            @input="handleFilterChange"
                        />
                    </a-form-item>
                </a-col>
                <a-col :span="6">
                    <a-form-item label="供应商" class="smart-query-form-item">
                        <a-input 
                            v-model:value="filterParams.supplierName" 
                            placeholder="供应商名称" 
                            @input="handleFilterChange"
                        />
                    </a-form-item>
                </a-col>
                <a-col :span="6">
                    <a-form-item label="品牌" class="smart-query-form-item">
                        <a-input 
                            v-model:value="filterParams.brand" 
                            placeholder="品牌" 
                            @input="handleFilterChange"
                        />
                    </a-form-item>
                </a-col>
                <a-col :span="6">
                    <a-form-item label="型号" class="smart-query-form-item">
                        <a-input 
                            v-model:value="filterParams.model" 
                            placeholder="规格型号" 
                            @input="handleFilterChange"
                        />
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row class="smart-query-form-row" :gutter="16">
                <a-col :span="6">
                    <a-form-item label="配置" class="smart-query-form-item">
                        <a-input 
                            v-model:value="filterParams.detailConfig" 
                            placeholder="详细配置" 
                            @input="handleFilterChange"
                        />
                    </a-form-item>
                </a-col>
                <a-col :span="6">
                    <a-form-item label="筛选逻辑" class="smart-query-form-item">
                        <a-select 
                            v-model:value="filterLogic" 
                            style="width: 100%"
                            @change="handleFilterChange"
                        >
                            <a-select-option value="AND">与（AND）</a-select-option>
                            <a-select-option value="OR">或（OR）</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item class="smart-query-form-item">
                        <a-button @click="resetFilter" style="margin-left: 8px;">
                            <template #icon>
                                <ReloadOutlined />
                            </template>
                            重置
                        </a-button>
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>

        <!-- 数据表格 -->
        <a-table
            :row-selection="{
                type: 'radio',
                selectedRowKeys: selectedRowKeys,
                onChange: onSelectChange
            }"
            size="small"
            :columns="columns"
            :data-source="filteredMaterialList"
            :pagination="paginationConfig"
            :loading="tableLoading"
            :scroll="{ x: 1000, y: 400 }"
            row-key="id"
            bordered
            :custom-row="customRow"
        >
            <template #bodyCell="{ text, record, column }">
                <template v-if="column.dataIndex === 'detailConfig'">
                    <a-tooltip :title="text">
                        <div class="detail-config-content">{{ text }}</div>
                    </a-tooltip>
                </template>
                <template v-else-if="column.dataIndex === 'unitPrice'">
                    <span>{{ formatPrice(text) }}</span>
                </template>
            </template>
        </a-table>
    </a-modal>
</template>

<script setup>
    import { ref, reactive, computed, onMounted, watch } from 'vue';
    import { message } from 'ant-design-vue';
    import { ReloadOutlined } from '@ant-design/icons-vue';
    import { materialListApi } from '/@/api/business/material-list-api';
    import { smartSentry } from '/@/lib/smart-sentry';

    // ----------------------- 组件定义 ---------------------
    const emits = defineEmits(['selectData']);
    defineExpose({
        showModal
    });

    // ----------------------- 响应式数据 ---------------------
    const visible = ref(false);
    const confirmLoading = ref(false);
    const tableLoading = ref(false);
    const materialList = ref([]);
    const selectedRowKeys = ref([]);
    const selectedMaterial = ref(null);

    // 筛选参数
    const filterParams = reactive({
        materialName: '',
        supplierName: '',
        brand: '',
        model: '',
        detailConfig: ''
    });

    const filterLogic = ref('AND'); // 筛选逻辑：AND 或 OR

    // ----------------------- 表格配置 ---------------------
    const columns = [
        {
            title: '物料编码',
            dataIndex: 'materialCode',
            width: 120
        },
        {
            title: '物料名称',
            dataIndex: 'materialName',
            width: 150
        },
        {
            title: '供应商名称',
            dataIndex: 'supplierName',
            width: 150
        },
        {
            title: '品牌',
            dataIndex: 'brand',
            width: 120
        },
        {
            title: '规格型号',
            dataIndex: 'model',
            width: 120
        },
        {
            title: '详细配置',
            dataIndex: 'detailConfig',
            width: 200
        },
        {
            title: '单价(元)',
            dataIndex: 'unitPrice',
            width: 100
        }
    ];

    // 分页配置
    const paginationConfig = reactive({
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条`,
        pageSize: 10,
        current: 1,
        total: 0,
        onChange: (page, pageSize) => {
            paginationConfig.current = page;
            paginationConfig.pageSize = pageSize;
        },
        onShowSizeChange: (current, size) => {
            paginationConfig.current = 1;
            paginationConfig.pageSize = size;
        }
    });

    // ----------------------- 计算属性 ---------------------
    // 筛选后的物料列表（不分页）
    const filteredMaterialListAll = computed(() => {
        if (!materialList.value.length) return [];

        // 获取所有非空的筛选条件
        const filters = Object.entries(filterParams).filter(([key, value]) =>
            value && value.trim() !== ''
        );

        if (filters.length === 0) return materialList.value;

        return materialList.value.filter(material => {
            const matches = filters.map(([key, value]) => {
                const materialValue = material[key] || '';

                // 支持空格分割的多关键词搜索
                const keywords = value.trim().split(/\s+/);
                return keywords.some(keyword =>
                    materialValue.toLowerCase().includes(keyword.toLowerCase())
                );
            });

            // 根据筛选逻辑返回结果
            return filterLogic.value === 'AND'
                ? matches.every(match => match)
                : matches.some(match => match);
        });
    });

    // 分页后的物料列表
    const filteredMaterialList = computed(() => {
        const allData = filteredMaterialListAll.value;
        const { current, pageSize } = paginationConfig;

        // 更新总数
        paginationConfig.total = allData.length;

        // 计算分页数据
        const startIndex = (current - 1) * pageSize;
        const endIndex = startIndex + pageSize;

        return allData.slice(startIndex, endIndex);
    });

    // ----------------------- 方法定义 ---------------------
    
    // 显示弹窗
    async function showModal() {
        visible.value = true;
        selectedRowKeys.value = [];
        selectedMaterial.value = null;
        // 重置分页状态
        paginationConfig.current = 1;
        paginationConfig.pageSize = 10;
        await queryMaterialList();
    }

    // 关闭弹窗
    function closeModal() {
        visible.value = false;
        resetFilter();
    }

    // 查询物料列表
    async function queryMaterialList() {
        try {
            tableLoading.value = true;
            const res = await materialListApi.queryAllMaterials();
            if (res.ok) {
                materialList.value = res.data || [];
            } else {
                message.error(res.msg || '查询物料列表失败');
                materialList.value = [];
            }
        } catch (error) {
            smartSentry.captureError(error);
            message.error('查询物料列表异常');
            materialList.value = [];
        } finally {
            tableLoading.value = false;
        }
    }

    // 表格行选择
    function onSelectChange(keys, rows) {
        selectedRowKeys.value = keys;
        selectedMaterial.value = rows[0] || null;
    }

    // 行点击事件
    function onRowClick(record) {
        const key = record.id;
        selectedRowKeys.value = [key];
        selectedMaterial.value = record;
    }

    // 自定义行属性，添加点击样式
    function customRow(record) {
        return {
            style: { cursor: 'pointer' },
            onClick: () => onRowClick(record)
        };
    }

    // 确认选择物料
    function onSelectMaterial() {
        if (!selectedMaterial.value) {
            message.warning('请选择一个物料');
            return;
        }

        confirmLoading.value = true;
        
        // 模拟异步操作
        setTimeout(() => {
            emits('selectData', selectedMaterial.value);
            confirmLoading.value = false;
            closeModal();
            message.success('物料选择成功');
        }, 300);
    }

    // 筛选条件变化处理
    function handleFilterChange() {
        // 重置分页到第一页
        paginationConfig.current = 1;
        // 清空选中项（因为分页变化可能导致选中项不在当前页）
        selectedRowKeys.value = [];
        selectedMaterial.value = null;
    }

    // 重置筛选条件
    function resetFilter() {
        Object.keys(filterParams).forEach(key => {
            filterParams[key] = '';
        });
        filterLogic.value = 'AND';
        selectedRowKeys.value = [];
        selectedMaterial.value = null;
        paginationConfig.current = 1;
        paginationConfig.pageSize = 10;
    }

    // 格式化价格
    function formatPrice(price) {
        if (!price) return '0.00';
        return Number(price).toFixed(2);
    }

    // ----------------------- 生命周期 ---------------------
    onMounted(() => {
        // 组件挂载时可以预加载数据，但这里选择在显示弹窗时再加载
    });
</script>

<style scoped>
.detail-config-content {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.smart-query-form {
    background: #fafafa;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;
}

.smart-query-form-row {
    margin-bottom: 0;
}

.smart-query-form-item {
    margin-bottom: 16px;
}

.smart-query-form-item:last-child {
    margin-bottom: 0;
}
</style>
