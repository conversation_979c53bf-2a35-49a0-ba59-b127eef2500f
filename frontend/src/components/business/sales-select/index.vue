<!--
  * 销售人员下拉选择框
-->
<template>
    <a-select
        v-model:value="selectValue"
        :style="`width: ${width}`"
        :placeholder="props.placeholder"
        :showSearch="true"
        :allowClear="true"
        :size="size"
        :filter-option="false"
        @search="handleSearch"
        @change="onChange"
    >
        <a-select-option v-for="item in filteredSalesList" :key="item.id" :value="item.id">
            {{ item.salesName }}
            <template v-if="item.region && item.region.trim()"> （{{ item.region }}） </template>
        </a-select-option>
    </a-select>
</template>

<script setup>
    import { onMounted, ref, watch, computed } from 'vue';
    import { salesInfoApi } from '/@/api/business/sales-info-api';
    import { smartSentry } from '/@/lib/smart-sentry';

    // =========== 属性定义 和 事件方法暴露 =============

    const props = defineProps({
        value: [Number, Array],
        placeholder: {
            type: String,
            default: '请选择销售人员'
        },
        width: {
            type: String,
            default: '100%'
        },
        size: {
            type: String,
            default: 'default'
        }
    });

    const emit = defineEmits(['update:value', 'change']);

    // =========== 查询数据 =============

    // 销售人员列表数据
    const salesList = ref([]);
    // 搜索关键词
    const searchKeyword = ref('');

    async function query() {
        try {
            let resp = await salesInfoApi.queryAllSales();
            salesList.value = resp.data || [];
        } catch (e) {
            smartSentry.captureError(e);
        }
    }
    onMounted(query);

    // =========== 搜索过滤 =============

    // 过滤后的销售人员列表
    const filteredSalesList = computed(() => {
        if (!searchKeyword.value) {
            return salesList.value;
        }
        
        const keyword = searchKeyword.value.toLowerCase();
        return salesList.value.filter(item => {
            return (
                (item.salesName && item.salesName.toLowerCase().includes(keyword)) ||
                (item.mobilePhone && item.mobilePhone.includes(keyword)) ||
                (item.email && item.email.toLowerCase().includes(keyword)) ||
                (item.region && item.region.toLowerCase().includes(keyword))
            );
        });
    });

    function handleSearch(value) {
        searchKeyword.value = value;
    }

    // =========== 选择 监听、事件 =============

    const selectValue = ref(props.value);
    watch(
        () => props.value,
        (newValue) => {
            selectValue.value = newValue;
        }
    );

    function onChange(value) {
        emit('update:value', value);
        emit('change', value);
    }
</script>
