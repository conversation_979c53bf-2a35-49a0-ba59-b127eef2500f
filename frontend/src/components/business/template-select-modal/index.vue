<!--
  * 模板选择弹窗组件
-->
<template>
    <a-modal
        v-model:open="visible"
        :width="1200"
        :title="props.onlyAiTemplate ? '选择AI模板' : '选择模板'"
        @cancel="closeModal"
        @ok="onSelectTemplate"
        :confirmLoading="confirmLoading"
    >
        <!-- 搜索区域 -->
        <a-form class="smart-query-form" style="margin-bottom: 16px;">
            <a-row class="smart-query-form-row" :gutter="16">
                <a-col :span="16">
                    <a-form-item label="关键字" class="smart-query-form-item">
                        <a-input
                            v-model:value="queryParams.keyword"
                            placeholder="模板名称/业务线/产品类型"
                            @pressEnter="handleSearch"
                            allowClear
                        />
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item class="smart-query-form-item">
                        <a-button type="primary" @click="handleSearch" style="margin-right: 8px;">
                            <template #icon>
                                <SearchOutlined />
                            </template>
                            搜索
                        </a-button>
                        <a-button @click="resetSearch">
                            <template #icon>
                                <ReloadOutlined />
                            </template>
                            重置
                        </a-button>
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>

        <!-- 数据表格 -->
        <a-table
            :row-selection="{
                type: 'radio',
                selectedRowKeys: selectedRowKeys,
                onChange: onSelectChange
            }"
            size="small"
            :columns="columns"
            :data-source="filteredTemplateList"
            :pagination="paginationConfig"
            :loading="tableLoading"
            :scroll="{ x: 1000, y: 400 }"
            row-key="id"
            bordered
            :custom-row="customRow"
        >
            <template #bodyCell="{ text, record, column }">
                <template v-if="column.dataIndex === 'generateType'">
                    <a-tag :color="record.generateType === 2 ? 'blue' : 'default'">
                        {{ record.generateType === 2 ? 'AI智能' : '人工' }}
                    </a-tag>
                </template>
                <template v-else-if="column.dataIndex === 'createTime'">
                    <span>{{ formatDate(text) }}</span>
                </template>
                <template v-else-if="column.dataIndex === 'updateTime'">
                    <span>{{ formatDate(text) }}</span>
                </template>
                <template v-else-if="column.dataIndex === 'disabledFlag'">
                    <a-tag :color="text ? 'red' : 'green'">
                        {{ text ? '已禁用' : '启用中' }}
                    </a-tag>
                </template>
            </template>
        </a-table>
    </a-modal>
</template>

<script setup>
    import { ref, reactive, computed, onMounted } from 'vue';
    import { message } from 'ant-design-vue';
    import { ReloadOutlined, SearchOutlined } from '@ant-design/icons-vue';
    import { templateApi } from '/@/api/business/template-api';
    import { smartSentry } from '/@/lib/smart-sentry';
    import { formatDate } from '/@/lib/smart-util';

    // ----------------------- 组件定义 ---------------------
    const props = defineProps({
        // 是否只显示AI模板
        onlyAiTemplate: {
            type: Boolean,
            default: false
        }
    });

    const emits = defineEmits(['selectData']);
    defineExpose({
        showModal
    });

    // ----------------------- 响应式数据 ---------------------
    const visible = ref(false);
    const confirmLoading = ref(false);
    const tableLoading = ref(false);
    const templateList = ref([]);
    const selectedRowKeys = ref([]);
    const selectedTemplate = ref(null);

    // 查询参数（对应后端字段）
    const queryParams = reactive({
        keyword: '', // 关键字搜索（对应后端的keyword字段）
        contentType: 1, // 只查询模板类型
        generateType: undefined, // 生成类型筛选
        disabledFlag: null, // 不筛选状态，只显示启用的
        pageNum: 1,
        pageSize: 10
    });

    // 总数
    const total = ref(0);

    // ----------------------- 表格配置 ---------------------
    const columns = [
        {
            title: 'ID',
            dataIndex: 'id',
            width: 80
        },
        {
            title: '模板名称',
            dataIndex: 'name',
            width: 200
        },
        {
            title: '业务线',
            dataIndex: 'businessName',
            width: 150
        },
        {
            title: '产品类型',
            dataIndex: 'productName',
            width: 150
        },
        {
            title: '类型',
            dataIndex: 'generateType',
            width: 100
        },
        {
            title: '状态',
            dataIndex: 'disabledFlag',
            width: 100
        },
        {
            title: '创建人',
            dataIndex: 'createUserName',
            width: 120
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 160
        },
        {
            title: '最后修改时间',
            dataIndex: 'updateTime',
            width: 160
        }
    ];

    // 分页配置
    const paginationConfig = reactive({
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条`,
        pageSize: 10,
        current: 1,
        total: 0,
        onChange: (page, pageSize) => {
            queryParams.pageNum = page;
            queryParams.pageSize = pageSize;
            queryTemplateList();
        },
        onShowSizeChange: (current, size) => {
            queryParams.pageNum = 1;
            queryParams.pageSize = size;
            queryTemplateList();
        }
    });

    // ----------------------- 计算属性 ---------------------
    // 直接使用后端返回的模板列表，无需前端筛选
    const filteredTemplateList = computed(() => {
        return templateList.value;
    });

    // ----------------------- 方法定义 ---------------------
    
    // 显示弹窗
    async function showModal() {
        visible.value = true;
        selectedRowKeys.value = [];
        selectedTemplate.value = null;

        // 根据props设置查询参数
        if (props.onlyAiTemplate) {
            queryParams.generateType = 2; // 只查询AI模板
        } else {
            queryParams.generateType = undefined; // 查询所有类型
        }

        // 重置分页状态
        paginationConfig.current = 1;
        paginationConfig.pageSize = 10;
        await queryTemplateList();
    }

    // 关闭弹窗
    function closeModal() {
        visible.value = false;
        resetSearch();
    }

    // 查询模板列表
    async function queryTemplateList() {
        try {
            tableLoading.value = true;
            // 确保查询参数中不包含用户权限相关的过滤条件
            const params = {
                ...queryParams,
                disabledFlag: false, // 只查询启用的模板
                // 明确移除任何用户相关的过滤条件，确保所有用户都能访问管理员创建的模板
                // userId: undefined,
                // createUserId: undefined,
                // createUser: undefined
            };

            const res = await templateApi.query(params);
            if (res.ok) {
                templateList.value = res.data.list || [];
                total.value = res.data.total || 0;
                // 更新分页配置
                paginationConfig.total = total.value;
                paginationConfig.current = queryParams.pageNum;
                paginationConfig.pageSize = queryParams.pageSize;
            } else {
                message.error(res.msg || '查询模板列表失败');
                templateList.value = [];
                total.value = 0;
            }
        } catch (error) {
            smartSentry.captureError(error);
            message.error('查询模板列表异常');
            templateList.value = [];
            total.value = 0;
        } finally {
            tableLoading.value = false;
        }
    }

    // 表格行选择
    function onSelectChange(keys, rows) {
        selectedRowKeys.value = keys;
        selectedTemplate.value = rows[0] || null;
    }

    // 行点击事件
    function onRowClick(record) {
        const key = record.id;
        selectedRowKeys.value = [key];
        selectedTemplate.value = record;
    }

    // 自定义行属性，添加点击样式
    function customRow(record) {
        return {
            style: { cursor: 'pointer' },
            onClick: () => onRowClick(record)
        };
    }

    // 确认选择模板
    function onSelectTemplate() {
        if (!selectedTemplate.value) {
            message.warning('请选择一个模板');
            return;
        }

        confirmLoading.value = true;
        
        // 模拟异步操作
        setTimeout(() => {
            emits('selectData', selectedTemplate.value);
            confirmLoading.value = false;
            closeModal();
            message.success('模板选择成功');
        }, 300);
    }

    // 搜索处理
    function handleSearch() {
        // 重置分页到第一页
        queryParams.pageNum = 1;
        // 清空选中项
        selectedRowKeys.value = [];
        selectedTemplate.value = null;
        // 执行查询
        queryTemplateList();
    }

    // 重置搜索条件
    function resetSearch() {
        queryParams.keyword = '';
        queryParams.pageNum = 1;
        queryParams.pageSize = 10;

        // 根据props设置generateType
        if (props.onlyAiTemplate) {
            queryParams.generateType = 2; // 只查询AI模板
        } else {
            queryParams.generateType = undefined; // 查询所有类型
        }

        selectedRowKeys.value = [];
        selectedTemplate.value = null;
        // 执行查询
        queryTemplateList();
    }

    // ----------------------- 生命周期 ---------------------
    onMounted(() => {
        // 组件挂载时可以预加载数据，但这里选择在显示弹窗时再加载
    });
</script>

<style scoped>
.smart-query-form {
    background: #fafafa;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;
}

.smart-query-form-row {
    margin-bottom: 0;
}

.smart-query-form-item {
    margin-bottom: 16px;
}

.smart-query-form-item:last-child {
    margin-bottom: 0;
}
</style>
