<!--
  * 表格操作组件
-->
<template>
    <a-dropdown>
        <a-button>
           
            <!-- 列设置按钮已去除 -->
            <!-- 列设置 -->
        </a-button>
        <template #overlay>
            <a-menu>
                <a-menu-item v-for="item in columns" :key="item.dataIndex">
                    <a-checkbox
                        v-model:checked="item.visible"
                        @change="(e) => handleColumnChange(e, item)"
                    >
                        {{ item.title }}
                    </a-checkbox>
                </a-menu-item>
            </a-menu>
        </template>
    </a-dropdown>
</template>

<script setup>
import { ref, watch } from 'vue';
import { SettingOutlined } from '@ant-design/icons-vue';
import { useLocalStorage } from '/@/hooks/useLocalStorage';

const props = defineProps({
    columns: {
        type: Array,
        required: true
    },
    tableId: {
        type: String,
        required: true
    },
    refresh: {
        type: Function,
        required: true
    }
});

const { getItem, setItem } = useLocalStorage();

// 初始化列显示状态
const initColumnVisible = () => {
    const savedColumns = getItem(props.tableId);
    if (savedColumns) {
        props.columns.forEach(column => {
            const savedColumn = savedColumns.find(item => item.dataIndex === column.dataIndex);
            if (savedColumn) {
                column.visible = savedColumn.visible;
            }
        });
    }
};

// 监听列变化
watch(() => props.columns, () => {
    initColumnVisible();
}, { immediate: true });

// 处理列显示状态变化
const handleColumnChange = (e, column) => {
    column.visible = e.target.checked;
    const columns = props.columns.map(item => ({
        dataIndex: item.dataIndex,
        visible: item.visible
    }));
    setItem(props.tableId, columns);
    props.refresh();
};
</script>

<style scoped lang="less">
.ant-dropdown-menu {
    max-height: 400px;
    overflow-y: auto;
}
</style> 