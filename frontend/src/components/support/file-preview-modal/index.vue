<!--
  * 文件预览 弹窗
-->
<template>
    <div class="container">
        <!-- 图片预览 -->
        <a-image
            class="img-prev"
            :style="{ display: 'none' }"
            :preview="{
                visible: imageVisible,
                onVisibleChange: setImageVisible
            }"
            :src="previewUrl"
        />
        
        <!-- 文件预览模态框 -->
        <a-modal
            v-model:open="modalVisible"
            :title="currentFileName"
            :width="800"
            :footer="null"
            :destroyOnClose="true"
            @cancel="closeModal"
        >
            <div class="file-preview-container">
                <!-- PDF预览 -->
                <div v-if="isPdf" class="pdf-container">
                    <iframe :src="previewUrl" width="100%" height="500" frameborder="0"></iframe>
                </div>
                
                <!-- 不支持预览的文件类型 -->
                <div v-if="!canPreview" class="unsupported-file">
                    <p>该文件类型暂不支持在线预览，请下载后查看</p>
                    <a-button type="primary" @click="downloadCurrentFile">下载文件</a-button>
                </div>
            </div>
        </a-modal>
    </div>
</template>

<script setup>
    import { ref, computed } from 'vue';
    import { fileApi } from '/@/api/support/file-api';
    import { smartSentry } from '/@/lib/smart-sentry';
    import { SmartLoading } from '/@/components/framework/smart-loading';

    // 支持的文件类型
    const imageFileTypes = ['jpg', 'jpeg', 'png', 'gif'];
    const pdfFileType = ['pdf'];
    
    // 状态变量
    const previewUrl = ref('');
    const currentFileName = ref('');
    const currentFileType = ref('');
    const currentFileKey = ref('');
    
    // 图片预览状态
    const imageVisible = ref(false);
    const setImageVisible = (value) => {
        imageVisible.value = value;
        if (!value) {
            modalVisible.value = false;
        }
    };
    
    // 模态框状态
    const modalVisible = ref(false);
    
    // 计算属性：是否支持预览
    const isPdf = computed(() => {
        return pdfFileType.includes(getFileExtension(currentFileName.value));
    });
    
    const isImage = computed(() => {
        return imageFileTypes.includes(getFileExtension(currentFileName.value));
    });
    
    const canPreview = computed(() => {
        return isImage.value || isPdf.value;
    });
    
    // 获取文件扩展名
    function getFileExtension(filename) {
        if (!filename) return '';
        return filename.split('.').pop().toLowerCase();
    }
    
    // 兼容原有方法
    function showPreview(fileItem) {
        if (!fileItem.fileUrl) {
            (async () => {
                SmartLoading.show();
                try {
                    let res = await fileApi.getUrl(fileItem.fileKey);
                    fileItem.fileUrl = res.data;
                    showFile(fileItem);
                } catch (e) {
                    smartSentry.captureError(e);
                } finally {
                    SmartLoading.hide();
                }
            })();
        } else {
            showFile(fileItem);
        }
    }
    
    // 新增方法，兼容知识库文件列表调用
    function showModal(fileUrl, fileName) {
        previewUrl.value = fileUrl;
        currentFileName.value = fileName;
        currentFileType.value = getFileExtension(fileName);
        
        if (isImage.value) {
            // 图片使用a-image组件的预览功能
            setImageVisible(true);
        } else {
            // 其他类型文件使用模态框预览
            modalVisible.value = true;
        }
    }
    
    // 原有方法保持不变
    function showFile(fileItem) {
        if (isImg(fileItem.fileType)) {
            previewUrl.value = fileItem.fileUrl;
            setImageVisible(true);
            return;
        }
        fileApi.downLoadFile(fileItem.fileKey);
    }

    // 判断图片类型
    function isImg(fileType) {
        return imageFileTypes.includes(fileType);
    }
    
    // 关闭模态框
    function closeModal() {
        modalVisible.value = false;
        imageVisible.value = false;
    }
    
    // 下载当前文件
    function downloadCurrentFile() {
        if (currentFileKey.value) {
            fileApi.downLoadFile(currentFileKey.value);
        } else if (previewUrl.value) {
            const link = document.createElement('a');
            link.href = previewUrl.value;
            link.download = currentFileName.value;
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    defineExpose({
        showPreview,
        showModal
    });
</script>

<style lang="less" scoped>
    .container {
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .file-preview-container {
        width: 100%;
        min-height: 500px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    
    .pdf-container {
        width: 100%;
        height: 500px;
    }
    
    .unsupported-file {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 300px;
        
        p {
            margin-bottom: 20px;
            font-size: 16px;
            color: #666;
        }
    }
</style>
