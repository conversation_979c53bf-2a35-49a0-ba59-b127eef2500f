<!--
  * 文件类型图标
-->
<template>
    <span class="file-type-icon">
        <component :is="iconComponent" />
    </span>
</template>

<script setup>
    import {
        FileOutlined,
        FileImageOutlined,
        FileWordOutlined,
        FileExcelOutlined,
        FilePdfOutlined,
        FilePptOutlined,
        FileZipOutlined,
        FileTextOutlined,
        VideoCameraOutlined,
        SoundOutlined
    } from '@ant-design/icons-vue';
    import { computed } from 'vue';

    const props = defineProps({
        fileType: {
            type: String,
            default: ''
        }
    });

    const iconComponent = computed(() => {
        const fileType = props.fileType ? props.fileType.toLowerCase() : '';
        
        // 图片类型
        if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'bmp', 'webp'].includes(fileType)) {
            return FileImageOutlined;
        }
        
        // Word文档
        if (['doc', 'docx'].includes(fileType)) {
            return FileWordOutlined;
        }
        
        // Excel文件
        if (['xls', 'xlsx', 'csv'].includes(fileType)) {
            return FileExcelOutlined;
        }
        
        // PDF文件
        if (fileType === 'pdf') {
            return FilePdfOutlined;
        }
        
        // PPT文件
        if (['ppt', 'pptx'].includes(fileType)) {
            return FilePptOutlined;
        }
        
        // 压缩文件
        if (['zip', 'rar', '7z', 'tar', 'gz'].includes(fileType)) {
            return FileZipOutlined;
        }
        
        // 文本文件
        if (['txt', 'md', 'json', 'xml', 'html', 'css', 'js', 'ts'].includes(fileType)) {
            return FileTextOutlined;
        }
        
        // 视频文件
        if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(fileType)) {
            return VideoCameraOutlined;
        }
        
        // 音频文件
        if (['mp3', 'wav', 'ogg', 'aac', 'flac'].includes(fileType)) {
            return SoundOutlined;
        }
        
        // 默认文件图标
        return FileOutlined;
    });
</script>

<style scoped lang="less">
    .file-type-icon {
        font-size: 16px;
        display: inline-flex;
        align-items: center;
    }
</style> 