<!--
  * 知识库文件列表
-->
<template>
    <div class="knowledge-file-list">
        <div class="file-list-header">
            <a-space>
                <a-input-search v-model:value="queryForm.keywords" placeholder="搜索文件名" style="width: 200px" @search="queryFiles" />
                <a-button type="primary" @click="addFile" v-privilege="'knowledge:file:add'">上传文件</a-button>
            </a-space>
        </div>
        <div class="file-list-content">
            <a-table
                :columns="columns"
                :data-source="fileList"
                :pagination="false"
                :loading="loading"
                :rowKey="(record) => record.fileId"
                size="middle"
            >
                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'action'">
                        <a-space>
                            <a-button type="link" @click="previewFile(record)" size="small" v-if="canPreview(record.fileName)">预览</a-button>
                            <a-button type="link" @click="downloadFile(record)" size="small" v-else>下载</a-button>
                            <a-dropdown>
                                <a-button type="link" size="small">
                                    更多
                                    <down-outlined />
                                </a-button>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item @click="downloadFile(record)">下载</a-menu-item>
                                        <a-menu-item @click="moveFile(record)" v-privilege="'knowledge:file:update'">移动</a-menu-item>
                                        <a-menu-item @click="deleteFile(record)" v-privilege="'knowledge:file:delete'">删除</a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </a-space>
                    </template>
                    <template v-else-if="column.key === 'fileName'">
                        <div class="file-name">
                            <FileTypeIcon :file-type="record.fileType" />
                            <span>{{ record.fileName }}</span>
                        </div>
                    </template>
                    <template v-else-if="column.key === 'fileSize'">
                        <span>{{ formatFileSize(record.fileSize) }}</span>
                    </template>
                </template>
            </a-table>
            <a-pagination
                v-model:current="queryForm.pageNum"
                :total="total"
                :page-size="queryForm.pageSize"
                :page-size-options="pageSizeOptions"
                v-model:pageSize="queryForm.pageSize"
                show-size-changer
                show-quick-jumper
                show-total
                @change="handlePageChange"
                @showSizeChange="handlePageSizeChange"
                style="margin-top: 16px; text-align: right"
            />
        </div>

        <!-- 文件预览 -->
        <FilePreviewModal ref="filePreviewModal" />

        <!-- 文件上传 -->
        <a-modal
            v-model:open="fileUploadVisible"
            title="上传文件"
            :destroyOnClose="true"
            @ok="handleFileUpload"
            @cancel="fileUploadVisible = false"
        >
            <a-form ref="fileFormRef" :model="fileForm" :rules="fileRules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                <a-form-item label="文件">
                    <a-upload-dragger
                        v-model:fileList="uploadFileList"
                        :multiple="false"
                        :maxCount="1"
                        :beforeUpload="() => false"
                        @change="handleFileChange"
                        class="knowledge-file-upload"
                        accept=".doc,.docx,.pdf,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png,.gif,.zip,.rar"
                    >
                        <p class="ant-upload-drag-icon">
                            <inbox-outlined />
                        </p>
                        <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
                        <p class="ant-upload-hint">支持单个文件上传</p>
                    </a-upload-dragger>
                </a-form-item>
                <a-form-item label="描述" name="description">
                    <a-textarea v-model:value="fileForm.description" placeholder="请输入文件描述" :rows="4" />
                </a-form-item>
                <a-form-item label="标签" name="tags">
                    <a-input v-model:value="fileForm.tags" placeholder="请输入标签，用逗号分隔" />
                </a-form-item>
            </a-form>
        </a-modal>

        <!-- 文件移动 -->
        <a-modal
            v-model:open="fileMoveVisible"
            title="移动文件"
            :destroyOnClose="true"
            @ok="handleFileMove"
            @cancel="handleCancelMove"
        >
            <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
                <a-form-item label="目标文件夹">
                    <div v-if="folderTreeData.length === 0" class="empty-tree-message">
                        加载文件夹中...
                        <a-button type="link" @click="loadFolderTree" size="small">重新加载</a-button>
                    </div>
                    <div v-else>
                        <p class="debug-info">
                            {{ folderTreeData.length }}个文件夹可选
                            <a-button type="link" @click="loadFolderTree" size="small">刷新</a-button>
                        </p>
                        <a-tree-select
                            v-model:value="targetFolderId"
                            :treeData="folderTreeData"
                            :fieldNames="{ title: 'title', key: 'key', value: 'value', children: 'children' }"
                            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                            placeholder="请选择目标文件夹"
                            allow-clear
                            tree-default-expand-all
                            style="width: 100%"
                            @dropdownVisibleChange="onDropdownVisibleChange"
                        />
                    </div>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>

<script setup>
    import { ref, reactive, watch, onMounted } from 'vue';
    import { message, Modal } from 'ant-design-vue';
    import { knowledgeFileApi } from '/@/api/support/knowledge-file-api';
    import { knowledgeFolderApi } from '/@/api/support/knowledge-folder-api';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import FileUpload from '/@/components/support/file-upload/index.vue';
    import FilePreviewModal from '/@/components/support/file-preview-modal/index.vue';
    import FileTypeIcon from './file-type-icon.vue';
    import { ExclamationCircleOutlined, DownOutlined, InboxOutlined } from '@ant-design/icons-vue';
    import { createVNode } from 'vue';
    import { smartSentry } from '/@/lib/smart-sentry';

    // 表格列定义
    const columns = [
        {
            title: '文件名',
            dataIndex: 'fileName',
            key: 'fileName',
            width: '40%'
        },
        {
            title: '大小',
            dataIndex: 'fileSize',
            key: 'fileSize',
            width: '10%'
        },
        {
            title: '上传者',
            dataIndex: 'creatorName',
            key: 'creatorName',
            width: '15%'
        },
        {
            title: '上传时间',
            dataIndex: 'createTime',
            key: 'createTime',
            width: '20%'
        },
        {
            title: '操作',
            key: 'action',
            width: '15%'
        }
    ];

    // 分页设置
    const pageSizeOptions = ['10', '20', '50', '100'];

    // 文件列表相关数据
    const props = defineProps({
        folderId: {
            type: Number,
            default: null
        }
    });

    const loading = ref(false);
    const fileList = ref([]);
    const total = ref(0);
    const queryForm = reactive({
        keywords: '',
        folderId: null,
        pageNum: 1,
        pageSize: 10
    });

    // 监听文件夹ID变化
    watch(
        () => props.folderId,
        (newVal) => {
            if (newVal) {
                queryForm.folderId = newVal;
                queryForm.pageNum = 1;
                queryFiles();
            }
        },
        { immediate: true }
    );

    // 查询文件列表
    async function queryFiles() {
        if (!queryForm.folderId) return;
        
        loading.value = true;
        try {
            const res = await knowledgeFileApi.queryPage(queryForm);
            fileList.value = res.data.list;
            total.value = res.data.total;
        } catch (err) {
            smartSentry.captureError(err);
        } finally {
            loading.value = false;
        }
    }

    // 分页处理
    function handlePageChange(page, pageSize) {
        queryForm.pageNum = page;
        queryFiles();
    }

    function handlePageSizeChange(current, size) {
        queryForm.pageSize = size;
        queryFiles();
    }

    // 格式化文件大小
    function formatFileSize(size) {
        if (!size) return '0 KB';
        
        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let index = 0;
        let fileSize = size;
        
        while (fileSize >= 1024 && index < units.length - 1) {
            fileSize /= 1024;
            index++;
        }
        
        return `${fileSize.toFixed(2)} ${units[index]}`;
    }

    // 判断文件是否可以预览
    function canPreview(fileName) {
        if (!fileName) return false;
        const extension = fileName.split('.').pop().toLowerCase();
        // 定义可预览的文件类型
        const previewableTypes = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];
        return previewableTypes.includes(extension);
    }

    // 文件预览
    const filePreviewModal = ref();
    function previewFile(file) {
        filePreviewModal.value.showModal(file.fileUrl, file.fileName);
    }

    // 文件下载
    function downloadFile(file) {
        const link = document.createElement('a');
        link.href = file.fileUrl;
        link.download = file.fileName;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // 文件上传
    const fileUploadVisible = ref(false);
    const fileFormRef = ref();
    const uploadFileList = ref([]);
    const fileForm = reactive({
        folderId: null,
        description: '',
        tags: ''
    });
    const fileRules = {
        file: [
            { 
                validator: (rule, value, callback) => {
                    if (!uploadFileList.value || uploadFileList.value.length === 0) {
                        callback('请选择要上传的文件');
                    } else {
                        callback();
                    }
                }
            }
        ]
    };

    function addFile() {
        // 重置表单
        fileForm.folderId = queryForm.folderId;
        fileForm.description = '';
        fileForm.tags = '';
        
        // 重置文件列表
        uploadFileList.value = [];
        
        // 打开弹窗
        fileUploadVisible.value = true;
        
        // 延迟执行以确保表单已重置
        setTimeout(() => {
            if (fileFormRef.value) {
                fileFormRef.value.resetFields();
            }
        }, 100);
    }

    function handleFileChange(info) {
        // 更新uploadFileList，让ant-design-vue的上传组件显示文件列表
        uploadFileList.value = info.fileList;
        
        // 当有文件选择时，记录日志
        if (info.fileList && info.fileList.length > 0) {
            console.log('文件已选择:', info.fileList[0].name);
            console.log('文件对象:', info.fileList[0].originFileObj);
        } else {
            console.log('没有选择文件');
        }
    }

    async function handleFileUpload() {
        try {
            // 添加调试日志
            console.log('开始上传文件，表单数据:', fileForm);
            console.log('文件列表:', uploadFileList.value);
            
            // 判断文件是否存在
            if (!uploadFileList.value || uploadFileList.value.length === 0) {
                message.error('请选择要上传的文件');
                return;
            }
            
            const file = uploadFileList.value[0].originFileObj;
            console.log('获取到的文件对象:', file);
            
            if (!file) {
                message.error('请选择要上传的文件');
                return;
            }
            
            SmartLoading.show();
            
            // 使用整合式文件上传API
            await knowledgeFileApi.uploadFile(
                file,
                fileForm.folderId,
                fileForm.description,
                fileForm.tags
            );
            
            message.success('文件上传成功');
            fileUploadVisible.value = false;
            queryFiles();
        } catch (err) {
            console.error('文件上传错误:', err);
            message.error('文件上传失败: ' + (err.message || err));
            smartSentry.captureError(err);
        } finally {
            SmartLoading.hide();
        }
    }

    // 文件移动
    const fileMoveVisible = ref(false);
    const folderTreeData = ref([]);
    const targetFolderId = ref(null);
    const currentFile = ref(null);

    // 在组件挂载时预加载文件夹树数据
    onMounted(async () => {
        try {
            // 预加载文件夹树数据，但不显示
            console.log('组件挂载，预加载文件夹树数据');
            const res = await knowledgeFolderApi.getFolderTree();
            if (Array.isArray(res.data) && res.data.length > 0) {
                // 使用辅助函数检查和转换数据格式
                const preloadData = checkAndConvertTreeData(res.data);
                console.log('预加载的文件夹树数据:', preloadData);
            }
        } catch (err) {
            console.error('预加载文件夹树数据错误:', err);
        }
    });

    async function moveFile(file) {
        try {
            console.log('开始移动文件:', file);
            currentFile.value = file;
            targetFolderId.value = null;
            
            // 先清空旧数据，显示加载状态
            folderTreeData.value = [];
            
            // 先打开对话框，再加载数据
            fileMoveVisible.value = true;
            
            // 加载文件夹树
            console.log('开始加载文件夹树');
            await loadFolderTree();
            console.log('文件夹树加载完成，数据长度:', folderTreeData.value.length);
        } catch (error) {
            console.error('移动文件初始化错误:', error);
            message.error('加载文件夹失败，请重试');
        }
    }

    function onDropdownVisibleChange(visible) {
        if (visible && (!folderTreeData.value || folderTreeData.value.length === 0)) {
            // 当下拉框显示且没有数据时，重新加载文件夹树
            loadFolderTree();
        }
    }

    // 检查并转换文件夹树数据格式
    function checkAndConvertTreeData(data) {
        // 如果数据为空，返回空数组
        if (!data || !Array.isArray(data) || data.length === 0) {
            console.warn('文件夹树数据为空或格式不正确');
            return [];
        }
        
        console.log('检查文件夹树数据格式:', data);
        
        // 递归处理每个节点
        const processNode = (node) => {
            // 如果节点为空或不是对象，跳过
            if (!node || typeof node !== 'object') {
                return null;
            }
            
            // 创建新节点，确保包含必要的属性
            const newNode = {
                key: node.folderId,
                value: node.folderId,
                title: node.folderName || '未命名文件夹',
                folderId: node.folderId,
                folderName: node.folderName || '未命名文件夹',
                parentId: node.parentId
            };
            
            // 处理子节点
            if (node.children && Array.isArray(node.children) && node.children.length > 0) {
                newNode.children = node.children
                    .map(child => processNode(child))
                    .filter(child => child !== null);
            }
            
            return newNode;
        };
        
        // 处理所有顶级节点
        const result = data
            .map(node => processNode(node))
            .filter(node => node !== null);
        
        console.log('转换后的文件夹树数据:', result);
        return result;
    }

    async function loadFolderTree() {
        try {
            SmartLoading.show();
            const res = await knowledgeFolderApi.getFolderTree();
            console.log('文件夹树原始数据:', res.data);
            
            // 测试：如果API返回的数据有问题，使用测试数据
            const testData = [
                {
                    folderId: 1,
                    folderName: '知识库',
                    parentId: 0,
                    children: [
                        {
                            folderId: 2,
                            folderName: '国能日新',
                            parentId: 1,
                            children: [
                                {
                                    folderId: 3,
                                    folderName: '招标文件',
                                    parentId: 2
                                },
                                {
                                    folderId: 4,
                                    folderName: '投标文件',
                                    parentId: 2
                                }
                            ]
                        }
                    ]
                }
            ];
            
            // 确保数据结构正确，处理可能的数据格式问题
            if (Array.isArray(res.data) && res.data.length > 0) {
                // 使用辅助函数检查和转换数据格式
                folderTreeData.value = checkAndConvertTreeData(res.data);
            } else {
                console.warn('API返回的文件夹树数据为空或格式不正确，使用测试数据');
                folderTreeData.value = checkAndConvertTreeData(testData);
            }
            
            console.log('处理后的文件夹树数据:', folderTreeData.value);
        } catch (err) {
            console.error('加载文件夹树错误:', err);
            smartSentry.captureError(err);
            
            // 发生错误时使用测试数据
            const errorTestData = [
                {
                    folderId: 1,
                    folderName: '知识库(测试数据)',
                    parentId: 0,
                    children: [
                        {
                            folderId: 2,
                            folderName: '国能日新(测试数据)',
                            parentId: 1
                        }
                    ]
                }
            ];
            folderTreeData.value = checkAndConvertTreeData(errorTestData);
        } finally {
            SmartLoading.hide();
        }
    }

    async function handleFileMove() {
        if (!targetFolderId.value) {
            message.error('请选择目标文件夹');
            return;
        }

        if (!currentFile.value || !currentFile.value.fileId) {
            message.error('文件信息不完整，请重试');
            return;
        }

        try {
            console.log('开始移动文件:', currentFile.value, '到文件夹:', targetFolderId.value);
            SmartLoading.show();
            await knowledgeFileApi.moveFile(currentFile.value.fileId, targetFolderId.value);
            message.success('文件移动成功');
            fileMoveVisible.value = false;
            queryFiles();
        } catch (err) {
            console.error('文件移动错误:', err);
            message.error('文件移动失败: ' + (err.message || '未知错误'));
            smartSentry.captureError(err);
        } finally {
            SmartLoading.hide();
        }
    }

    function handleCancelMove() {
        // 重置状态
        targetFolderId.value = null;
        fileMoveVisible.value = false;
        folderTreeData.value = [];
    }

    // 文件删除
    function deleteFile(file) {
        Modal.confirm({
            title: '确定要删除该文件吗？',
            icon: createVNode(ExclamationCircleOutlined),
            content: '删除后无法恢复',
            okText: '确定',
            okType: 'danger',
            cancelText: '取消',
            async onOk() {
                try {
                    SmartLoading.show();
                    await knowledgeFileApi.deleteFile(file.fileId);
                    message.success('文件删除成功');
                    queryFiles();
                } catch (err) {
                    smartSentry.captureError(err);
                } finally {
                    SmartLoading.hide();
                }
            }
        });
    }

    // 暴露方法
    defineExpose({
        queryFiles
    });
</script>

<style scoped lang="less">
    .knowledge-file-list {
        display: flex;
        flex-direction: column;
        height: 100%;

        .file-list-header {
            padding: 10px 0;
            display: flex;
            justify-content: space-between;
        }

        .file-list-content {
            flex: 1;
            overflow: auto;
        }

        .file-name {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .empty-tree-message {
            text-align: center;
            padding: 10px;
            color: #999;
        }

        .debug-info {
            margin-bottom: 10px;
            font-size: 12px;
            color: #999;
        }
    }
</style> 