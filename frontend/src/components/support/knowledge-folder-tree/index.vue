<!--
  * 知识库文件夹树
-->
<template>
    <a-card class="tree-container" size="small" :bordered="false">
        <a-row>
            <a-input v-model:value.trim="keywords" placeholder="请输入文件夹名称" />
        </a-row>
        <a-row class="sort-flag-row" v-if="props.showMenu">
            <span>
                排序
                <template v-if="showSortFlag"> （越小越靠前） </template>
                ：<a-switch v-model:checked="showSortFlag" />
            </span>
            <a-button type="primary" @click="addTop" size="small" v-privilege="'knowledge:folder:add'">新建</a-button>
        </a-row>
        <a-tree
            v-if="!_.isEmpty(folderTreeData)"
            v-model:selectedKeys="selectedKeys"
            v-model:checkedKeys="checkedKeys"
            class="tree"
            :treeData="folderTreeData"
            :fieldNames="{ title: 'folderName', key: 'folderId', value: 'folderId' }"
            style="width: 100%; overflow-x: auto"
            :style="[!height ? '' : { height: `${height}px`, overflowY: 'auto' }]"
            :showLine="!props.checkable"
            :checkable="props.checkable"
            :checkStrictly="props.checkStrictly"
            :selectable="!props.checkable"
            :defaultExpandAll="true"
            @select="treeSelectChange"
        >
            <template #title="item">
                <a-popover placement="right" v-if="props.showMenu">
                    <template #content>
                        <div style="display: flex; flex-direction: column">
                            <a-button type="text" @click="addFolder(item.dataRef)" v-privilege="'knowledge:folder:add'"
                                >添加下级</a-button
                            >
                            <a-button type="text" @click="updateFolder(item.dataRef)" v-privilege="'knowledge:folder:update'"
                                >修改</a-button
                            >
                            <a-button
                                type="text"
                                v-if="item.folderId !== topFolderId"
                                @click="deleteFolder(item.folderId)"
                                v-privilege="'knowledge:folder:delete'"
                                >删除</a-button
                            >
                        </div>
                    </template>
                    {{ item.folderName }}
                    <!--显示排序字段-->
                    <template v-if="showSortFlag">
                        <span class="sort-span">({{ item.sort }})</span>
                    </template>
                </a-popover>
                <div v-else>{{ item.folderName }}</div>
            </template>
        </a-tree>
        <div class="no-data" v-else>暂无结果</div>
        <!-- 添加编辑文件夹弹窗 -->
        <KnowledgeFolderFormModal ref="folderFormModal" @refresh="refresh" />
    </a-card>
</template>
<script setup>
    import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
    import { ref } from 'vue';
    import { onUnmounted, watch } from 'vue';
    import { Modal } from 'ant-design-vue';
    import _ from 'lodash';
    import { createVNode, onMounted } from 'vue';
    import KnowledgeFolderFormModal from './knowledge-folder-form-modal.vue';
    import { knowledgeFolderApi } from '/@/api/support/knowledge-folder-api';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import mitt from 'mitt';
    import { smartSentry } from '/@/lib/smart-sentry';

    const FOLDER_PARENT_ID = 0;
    const folderEmitter = mitt();

    // ----------------------- 组件参数 ---------------------

    const props = defineProps({
        // 是否可以选中
        checkable: {
            type: Boolean,
            default: false
        },
        // 父子节点选中状态不再关联
        checkStrictly: {
            type: Boolean,
            default: false
        },
        // 树高度 超出出滚动条
        height: Number,
        // 显示菜单
        showMenu: {
            type: Boolean,
            default: true
        }
    });

    // ----------------------- 文件夹树的展示 ---------------------
    const topFolderId = ref();
    // 所有文件夹列表
    const folderList = ref([]);
    // 文件夹树形数据
    const folderTreeData = ref([]);
    // 存放文件夹id和文件夹，用于查找
    const idInfoMap = ref(new Map());
    // 是否显示排序字段
    const showSortFlag = ref(false);

    onMounted(() => {
        queryFolderTree();
    });

    // 刷新
    async function refresh() {
        await queryFolderTree();
        if (currentSelectedFolderId.value) {
            selectTree(currentSelectedFolderId.value);
        }
    }

    // 查询文件夹列表并构建文件夹树
    async function queryFolderTree() {
        SmartLoading.show();
        try {
            let res = await knowledgeFolderApi.getFolderTree();
            let data = res.data;
            folderList.value = data;
            folderTreeData.value = data;

            data.forEach((e) => {
                idInfoMap.value.set(e.folderId, e);
            });

            // 默认显示 最顶级ID为列表中返回的第一条数据的ID
            if (!_.isEmpty(folderTreeData.value) && folderTreeData.value.length > 0) {
                topFolderId.value = folderTreeData.value[0].folderId;
                selectTree(folderTreeData.value[0].folderId);
            }
        } catch (err) {
            smartSentry.captureError(err);
        } finally {
            SmartLoading.hide();
        }
    }

    // ----------------------- 树的选中 ---------------------
    const selectedKeys = ref([]);
    const checkedKeys = ref([]);
    const breadcrumb = ref([]);
    const currentSelectedFolderId = ref();
    const selectedFolderChildren = ref([]);

    folderEmitter.on('selectTree', selectTree);

    function selectTree(id) {
        selectedKeys.value = [id];
        treeSelectChange(selectedKeys.value);
    }

    function treeSelectChange(idList) {
        if (_.isEmpty(idList)) {
            breadcrumb.value = [];
            selectedFolderChildren.value = [];
            return;
        }
        let id = idList[0];
        let folder = idInfoMap.value.get(id);
        if (!folder) return;
        
        breadcrumb.value = [];
        breadcrumb.value.push(folder.folderName);
        
        currentSelectedFolderId.value = id;
    }

    // -----------------------  筛选 ---------------------
    const keywords = ref('');
    watch(
        () => keywords.value,
        () => {
            onSearch();
        }
    );

    // 筛选
    function onSearch() {
        if (!keywords.value) {
            folderTreeData.value = folderList.value;
            return;
        }
        let filteredData = folderList.value.filter(folder => {
            return folder.folderName.includes(keywords.value);
        });
        folderTreeData.value = filteredData;
    }

    // ----------------------- 表单操作：添加文件夹/修改文件夹/删除文件夹 ---------------------
    const folderFormModal = ref();

    // 添加
    function addFolder(e) {
        let data = {
            folderId: 0,
            folderName: '',
            parentId: e.folderId
        };
        currentSelectedFolderId.value = e.folderId;
        folderFormModal.value.showModal(data);
    }
    // 添加顶级
    function addTop() {
        let data = {
            folderId: 0,
            folderName: '',
            parentId: 0
        };
        folderFormModal.value.showModal(data);
    }
    // 编辑
    function updateFolder(e) {
        currentSelectedFolderId.value = e.folderId;
        folderFormModal.value.showModal(e);
    }

    // 删除
    function deleteFolder(id) {
        Modal.confirm({
            title: '提醒',
            icon: createVNode(ExclamationCircleOutlined),
            content: '确定要删除该文件夹吗?',
            okText: '删除',
            okType: 'danger',
            async onOk() {
                SmartLoading.show();
                try {
                    // 若删除的是当前的文件夹 先找到上级文件夹
                    let selectedKey = null;
                    if (!_.isEmpty(selectedKeys.value)) {
                        selectedKey = selectedKeys.value[0];
                        if (selectedKey === id) {
                            let selectInfo = folderList.value.find((e) => e.folderId === id);
                            if (selectInfo && selectInfo.parentId) {
                                selectedKey = selectInfo.parentId;
                            }
                        }
                    }
                    await knowledgeFolderApi.deleteFolder(id);
                    await queryFolderTree();
                    // 刷新选中文件夹
                    if (selectedKey) {
                        selectTree(selectedKey);
                    }
                } catch (error) {
                    smartSentry.captureError(error);
                } finally {
                    SmartLoading.hide();
                }
            },
            cancelText: '取消',
            onCancel() {}
        });
    }

    onUnmounted(() => {
        folderEmitter.all.clear();
    });

    // ----------------------- 以下是暴露的方法内容 ----------------------------
    defineExpose({
        queryFolderTree,
        selectedFolderChildren,
        breadcrumb,
        selectedKeys,
        checkedKeys,
        keywords
    });
</script>
<style scoped lang="less">
    .tree-container {
        height: 100%;
        display: flex;
        flex-direction: column;

        .tree {
            flex: 1;
            margin-top: 10px;
        }

        .no-data {
            text-align: center;
            margin-top: 20px;
            color: #999;
        }

        .sort-flag-row {
            margin-top: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sort-span {
            color: #999;
            font-size: 12px;
            margin-left: 5px;
        }
    }
</style> 