<!--
  * 知识库文件夹表单
-->
<template>
    <a-modal v-model:open="visible" :title="formState.folderId ? '编辑文件夹' : '新建文件夹'" @ok="onOk" @cancel="closeModal">
        <a-form ref="formRef" :model="formState" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
            <a-form-item label="文件夹名称" name="folderName">
                <a-input v-model:value="formState.folderName" placeholder="请输入文件夹名称" />
            </a-form-item>
            <a-form-item label="父级文件夹" name="parentId">
                <a-tree-select
                    v-model:value="formState.parentId"
                    :treeData="treeData"
                    :fieldNames="{ label: 'folderName', key: 'folderId', value: 'folderId' }"
                    show-search
                    style="width: 100%"
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    placeholder="请选择父级文件夹"
                    allow-clear
                    tree-default-expand-all
                />
            </a-form-item>
            <a-form-item label="排序" name="sort">
                <a-input-number v-model:value="formState.sort" placeholder="值越小越靠前" style="width: 100%" />
            </a-form-item>
            <a-form-item label="描述" name="description">
                <a-textarea v-model:value="formState.description" placeholder="请输入文件夹描述" :rows="4" />
            </a-form-item>
        </a-form>
    </a-modal>
</template>
<script setup>
    import { message } from 'ant-design-vue';
    import { reactive, ref } from 'vue';
    import { knowledgeFolderApi } from '/@/api/support/knowledge-folder-api';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { smartSentry } from '/@/lib/smart-sentry';

    // ----------------------- 对外暴漏 ---------------------

    defineExpose({
        showModal
    });

    // ----------------------- modal 的显示与隐藏 ---------------------
    const emits = defineEmits(['refresh']);

    const visible = ref(false);
    const treeData = ref([]);
    
    function showModal(data) {
        visible.value = true;
        updateFormData(data);
        queryFolderTree();
    }
    
    function closeModal() {
        visible.value = false;
        resetFormData();
    }

    // ----------------------- form 表单操作 ---------------------
    const formRef = ref();
    const defaultFormState = {
        folderId: undefined,
        folderName: undefined,
        parentId: undefined,
        sort: 0,
        description: undefined
    };

    let formState = reactive({
        ...defaultFormState
    });
    
    // 表单校验规则
    const rules = {
        parentId: [{ required: true, message: '上级文件夹不能为空' }],
        folderName: [
            { required: true, message: '文件夹名称不能为空' },
            { max: 50, message: '文件夹名称不能大于50个字符', trigger: 'blur' }
        ]
    };
    
    // 更新表单数据
    function updateFormData(data) {
        Object.assign(formState, defaultFormState);
        if (data) {
            Object.assign(formState, data);
        }
        visible.value = true;
    }
    
    // 重置表单数据
    function resetFormData() {
        Object.assign(formState, defaultFormState);
    }

    // 获取文件夹树
    async function queryFolderTree() {
        try {
            SmartLoading.show();
            const result = await knowledgeFolderApi.getFolderTree();
            let tree = result.data;
            
            // 如果是编辑，需要过滤掉自己和子节点
            if (formState.folderId) {
                tree = filterTreeData(tree, formState.folderId);
            }
            
            treeData.value = tree;
        } catch (err) {
            smartSentry.captureError(err);
        } finally {
            SmartLoading.hide();
        }
    }
    
    // 过滤树数据，排除自己和子节点
    function filterTreeData(tree, folderId) {
        return tree.filter(node => {
            if (node.folderId === folderId) {
                return false;
            }
            
            if (node.children && node.children.length > 0) {
                node.children = filterTreeData(node.children, folderId);
            }
            
            return true;
        });
    }

    // 表单提交
    async function onOk() {
        try {
            await formRef.value.validate();
            SmartLoading.show();
            
            if (formState.folderId) {
                await knowledgeFolderApi.updateFolder(formState);
                message.success('更新成功');
            } else {
                await knowledgeFolderApi.addFolder(formState);
                message.success('添加成功');
            }
            
            closeModal();
            emits('refresh');
        } catch (err) {
            smartSentry.captureError(err);
        } finally {
            SmartLoading.hide();
        }
    }
</script> 