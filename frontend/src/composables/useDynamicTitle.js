import { ref, watch, reactive, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import { useUserStore } from '/@/store/modules/system/user';
import { localSave } from '/@/utils/local-util';
import localKey from '/@/constants/local-storage-key-const';

// 全局的动态标题状态 - 使用reactive确保深度响应式
const dynamicTitles = reactive(new Map());

// 存储监听器的清理函数
const watcherCleanupMap = new Map();

/**
 * 动态标题组合式函数
 * 用于管理页面的动态标题，支持面包屑、标签页等组件的实时更新
 */
export function useDynamicTitle() {
    const route = useRoute();
    
    /**
     * 设置当前页面的动态标题
     * @param {string|import('vue').ComputedRef} title - 标题字符串或计算属性
     */
    function setPageTitle(title) {
        const routeName = route.name;
        if (!routeName) return;

        // 清理之前的监听器
        const existingCleanup = watcherCleanupMap.get(routeName);
        if (existingCleanup) {
            existingCleanup();
            watcherCleanupMap.delete(routeName);
        }

        // 如果是计算属性，监听其变化
        if (typeof title === 'object' && title.value !== undefined) {
            dynamicTitles.set(routeName, title.value);
            const stopWatcher = watch(title, (newTitle) => {
                dynamicTitles.set(routeName, newTitle);
                updateAllTitleReferences(newTitle);
            }, { immediate: true });

            // 存储清理函数
            watcherCleanupMap.set(routeName, stopWatcher);
        } else {
            dynamicTitles.set(routeName, title);
            updateAllTitleReferences(title);
        }
    }
    
    /**
     * 获取当前页面的动态标题
     * @returns {string} 当前页面标题
     */
    function getPageTitle() {
        const routeName = route.name;
        return dynamicTitles.get(routeName) || route.meta?.title || '';
    }
    
    /**
     * 更新所有标题引用（浏览器标题、标签页等）
     * @param {string} newTitle - 新标题
     */
    function updateAllTitleReferences(newTitle) {
        try {
            // 更新浏览器标题
            document.title = newTitle;

            // 不直接修改route.meta.title，而是通过动态标题系统来管理
            // 这样可以避免影响其他页面的标题显示

            // 更新标签页标题
            const userStore = useUserStore();
            if (userStore.tagNav) {
                const currentTag = userStore.tagNav.find(tag => tag.menuName === route.name);
                if (currentTag) {
                    currentTag.menuTitle = newTitle;
                    // 同步保存到本地存储
                    localSave(localKey.USER_TAG_NAV, JSON.stringify(userStore.tagNav));
                }
            }
        } catch (error) {
            console.error('更新标题引用时发生错误:', error);
        }
    }
    
    /**
     * 清除页面标题（页面卸载时调用）
     */
    function clearPageTitle() {
        const routeName = route.name;
        if (routeName) {
            // 清理监听器
            const cleanup = watcherCleanupMap.get(routeName);
            if (cleanup) {
                cleanup();
                watcherCleanupMap.delete(routeName);
            }
            // 清理标题
            dynamicTitles.delete(routeName);
        }
    }

    // 组件卸载时自动清理
    onUnmounted(() => {
        clearPageTitle();
    });
    
    return {
        setPageTitle,
        getPageTitle,
        clearPageTitle,
        dynamicTitles
    };
}

/**
 * 获取动态标题的组合式函数（用于布局组件）
 * @returns {object} 包含获取标题方法的对象
 */
export function useDynamicTitleReader() {
    const route = useRoute();

    function getCurrentTitle() {
        const routeName = route.name;
        // 只有当前路由有动态标题时才返回，否则返回原始的路由meta标题
        const dynamicTitle = dynamicTitles.get(routeName);
        return dynamicTitle || route.meta?.title || '';
    }

    return {
        getCurrentTitle,
        dynamicTitles
    };
}
