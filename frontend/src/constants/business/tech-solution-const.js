/**
 * 技术方案常量
 */

// 技术方案状态枚举
export const TECH_SOLUTION_STATUS_ENUM = {
  PROCESSING: {
    value: 'PROCESSING',
    desc: '处理中'
  },
  COMPLETED: {
    value: 'COMPLETED',
    desc: '已完成'
  },
  FAILED: {
    value: 'FAILED',
    desc: '失败'
  }
};

// 支持的文件类型
export const TECH_SOLUTION_FILE_TYPES = '.md,.txt,.docx';

// 提示词模板
export const TECH_SOLUTION_PROMPT_TEMPLATES = [
  {
    name: '通用技术方案',
    content: `请生成一份专业的技术方案文档，包含以下基本信息：
        - 系统名称：[请指定系统名称]
        - 项目类型：[如：能源管理系统/虚拟电厂/微电网/储能系统/集控/功率预测/功率控制/电能质量检测等]
        - 目标场景：[如：风电/光伏/储能/微电网/工业园区/集控/虚拟电厂/商业综合体等]
        - 版本信息：[如：V1.0]

        ## 文档结构要求

        ### 1. 文档封面与目录
        - 生成完整的技术方案封面，包含系统名称、版本号、编制时间
        - 制作详细的目录结构，页码对应清晰

        ### 2. 系统设计架构（第一章）
        #### 2.1 系统目标优势
        - **系统概述**：简要介绍系统的核心功能和应用场景
        - **设计目标**：明确安全性、可靠性、高效性等关键目标
        - **技术优势**：突出通用性、解耦性、完整性、可扩展性等特点

        #### 2.2 系统部署方案
        - **网络架构**：绘制系统网络拓扑图，包含工作站、服务器、交换机、各类设备连接关系
        - **软件架构**：采用分层设计，包含设备层、传输层、应用层
          - 设备层：列出所有接入设备（光伏、储能、负荷、电表、BMS、PCS、ATS、环境监测等）
          - 传输层：说明通信协议（Modbus RTU/TCP、IEC104、MQTT等）
          - 应用层：核心业务功能模块

        ### 3. 功能设计说明（第二章）
        #### 3.1 核心功能模块设计
        为每个功能模块提供详细描述：
        - **首页**：系统概览、关键指标展示、实时状态监控
        - **运行信息**：设备运行状态、功率数据、电量统计
        - **历史数据**：数据查询、趋势分析、报表生成
        - **告警信息**：故障报警、异常监测、告警分级处理
        - **设备管理**：包含BMS、PCS、ATS、环境监测等子系统
        - **智能策略**：运行策略、控制算法、优化调度
        - **系统管理**：用户权限、角色管理、系统配置

        #### 3.2 特色功能设计
        根据系统类型添加特色功能：
        - 储能系统：SOC管理、充放电策略、需量控制
        - 微电网：并离网切换、防逆流保护、削峰填谷
        - 虚拟电厂：资源聚合、市场交易、需求响应
        - 能源管理：能耗分析、碳资产管理、成本优化
        - 集控：集控中心、集控系统、集控设备
        - 功率预测：功率预测、功率预测模型、功率预测算法
        - 功率控制：功率控制、功率控制模型、功率控制算法
        - 电能质量检测：电能质量检测、电能质量检测模型、电能质量检测算法

        ### 4. 系统界面展示（第三章）
        为每个功能模块提供界面设计说明：
        - 界面布局描述
        - 关键数据展示方式
        - 操作流程说明
        - 用户交互设计

        ### 5. 平台策略（第四章）
        #### 5.1 运行策略
        - 系统自动运行逻辑
        - 设备协调控制策略
        - 优化目标和约束条件

        #### 5.2 控制策略
        - **有功控制**：功率分配、经济调度
        - **无功控制**：电压调节、功率因数优化
        - **保护策略**：故障保护、安全隔离

        #### 5.3 智能策略
        根据不同运行模式设计：
        - 并网运行：削峰填谷、需量控制、防逆流
        - 离网运行：频率电压调节、负荷管理、应急控制
        - 经济运行：成本优化、收益最大化
        - 虚拟电厂：资源聚合、市场交易、需求响应
        - 能源管理：能耗分析、碳资产管理、成本优化
        - 集控：集控中心、集控系统、集控设备
        - 功率预测：功率预测、功率预测模型、功率预测算法
        - 功率控制：功率控制、功率控制模型、功率控制算法
        - 电能质量检测：电能质量检测、电能质量检测模型、电能质量检测算法

        ### 6. 技术实现方案
        #### 6.1 关键技术
        - 数据采集技术
        - 通信协议栈
        - 数据库设计
        - 算法模型
        - 数据分析
        - 数据可视化
        - 数据存储
        - 数据安全
        - 数据备份
        - 数据恢复

        #### 6.2 系统集成
        - 第三方系统接口
        - 数据交换标准
        - 安全认证机制

        ## 内容生成要求

        ### 专业性要求
        - 使用电力系统专业术语
        - 体现行业技术标准和规范
        - 包含具体的技术参数和指标

        ### 完整性要求
        - 每个章节内容充实，避免空泛描述
        - 提供具体的技术实现方案
        - 包含详细的功能说明和操作流程

        ### 实用性要求
        - 方案具有可操作性和可实施性
        - 考虑实际应用场景和约束条件
        - 提供明确的技术路径和实现方法

        ## 输出格式
        - 采用标准的技术文档格式
        - 层次结构清晰，编号规范
        - 包含必要的图表和表格说明
        - 总页数控制在20-30页的详细程度`
  },
  {
    name: '储能系统技术方案',
    content: `请生成一份专业的储能系统技术方案文档，重点包含以下内容：

## 系统概述
- 储能系统类型：[锂电池储能/钠离子储能/液流电池储能/压缩空气储能等]
- 应用场景：[电网侧储能/用户侧储能/发电侧储能/微电网储能等]
- 系统容量：[功率规模MW/容量规模MWh]
- 技术路线：[集中式/分布式/模块化设计]

## 核心功能设计
### 2.1 储能管理系统(EMS)
- **SOC管理**：电池荷电状态监测、预测和控制
- **充放电策略**：基于电价、负荷预测的智能充放电
- **需量控制**：削峰填谷、需量管理、负荷转移
- **能量优化**：经济调度、收益最大化算法

### 2.2 电池管理系统(BMS)
- **电池监测**：单体电压、温度、电流实时监测
- **均衡控制**：主动/被动均衡策略
- **安全保护**：过充、过放、过温、短路保护
- **故障诊断**：电池健康状态评估、故障预警

### 2.3 功率变换系统(PCS)
- **双向变流**：AC/DC双向功率变换
- **并网控制**：电压、频率、功率因数调节
- **孤岛检测**：反孤岛保护、孤岛运行能力
- **谐波抑制**：THD控制、电能质量优化

## 系统架构设计
### 3.1 硬件架构
- **储能单元**：电池模组、电池簇、电池柜配置
- **变流设备**：PCS功率等级、效率指标、冗余设计
- **监控设备**：传感器配置、数据采集系统
- **辅助设备**：消防系统、空调系统、照明系统

### 3.2 软件架构
- **设备层**：BMS、PCS、电表、环境监测设备
- **通信层**：CAN、RS485、以太网通信协议
- **应用层**：EMS能量管理、监控SCADA、运维管理

## 控制策略
### 4.1 运行模式
- **并网运行**：削峰填谷、调频调压、备用容量
- **离网运行**：UPS模式、应急供电、黑启动
- **维护模式**：设备检修、系统测试、数据备份

### 4.2 优化算法
- **经济调度**：基于电价的充放电优化
- **预测控制**：负荷预测、新能源出力预测
- **多目标优化**：经济性、安全性、寿命平衡

## 安全防护
### 5.1 电气安全
- **绝缘监测**：直流系统绝缘状态监测
- **接地保护**：保护接地、工作接地设计
- **电弧检测**：直流电弧故障检测与保护

### 5.2 消防安全
- **火灾检测**：烟感、温感、气体检测
- **灭火系统**：气体灭火、水喷淋系统
- **应急处理**：紧急停机、人员疏散预案

## 性能指标
- **系统效率**：≥90%（AC-AC往返效率）
- **响应时间**：≤100ms（功率响应时间）
- **循环寿命**：≥6000次（80%DOD）
- **可用率**：≥95%（年可用率）

请根据具体项目需求调整技术参数和配置方案。`
  },
  {
    name: '微电网系统技术方案',
    content: `请生成一份专业的微电网系统技术方案文档，重点包含以下内容：

## 系统概述
- 微电网类型：[交流微电网/直流微电网/交直流混合微电网]
- 应用场景：[工业园区/商业综合体/海岛/偏远地区/数据中心等]
- 接入电源：[光伏/风电/储能/柴发/燃气轮机/燃料电池等]
- 负荷特性：[关键负荷/一般负荷/可调负荷分类]

## 核心功能设计
### 2.1 微电网控制器(MGC)
- **运行模式切换**：并网/离网无缝切换
- **功率管理**：有功/无功功率协调控制
- **电能质量**：电压/频率稳定控制
- **保护控制**：故障检测、隔离与恢复

### 2.2 能量管理系统(EMS)
- **负荷预测**：基于历史数据的负荷预测算法
- **发电预测**：光伏/风电出力预测模型
- **经济调度**：多能源协调优化调度
- **需求响应**：负荷侧管理、用电行为引导

### 2.3 分布式电源管理
- **光伏系统**：MPPT控制、防逆流保护
- **风电系统**：变桨控制、低电压穿越
- **储能系统**：充放电策略、SOC管理
- **备用电源**：柴发/燃机启停控制

## 系统架构设计
### 3.1 电气架构
- **主接线方案**：单母线/双母线/环网结构
- **电压等级**：10kV/0.4kV配电系统设计
- **保护配置**：继电保护、自动化装置
- **计量系统**：关口计量、分项计量配置

### 3.2 通信架构
- **通信网络**：工业以太网、光纤通信
- **通信协议**：IEC61850、Modbus、MQTT
- **网络安全**：防火墙、加密传输、访问控制
- **时钟同步**：GPS/北斗授时系统

## 控制策略
### 4.1 并网运行策略
- **功率控制**：有功功率平滑输出
- **电压支撑**：无功功率调节、电压稳定
- **频率响应**：一次调频、二次调频参与
- **防逆流控制**：向主网功率输出限制

### 4.2 离网运行策略
- **电压频率控制**：V/f控制、下垂控制
- **负荷管理**：重要负荷优先、分级切负荷
- **黑启动**：储能黑启动、系统重构
- **孤岛检测**：主动/被动孤岛检测方法

### 4.3 切换控制策略
- **计划性切换**：预定时间并离网切换
- **故障切换**：主网故障快速切换
- **同期并网**：电压、频率、相位同期
- **软切换**：无扰动平滑切换技术

## 保护与安全
### 5.1 继电保护
- **线路保护**：距离保护、零序保护
- **变压器保护**：差动保护、瓦斯保护
- **母线保护**：母线差动、失压保护
- **分布式电源保护**：过/欠压、过/欠频保护

### 5.2 安全防护
- **人身安全**：安全围栏、警示标识
- **设备安全**：防雷、接地、消防系统
- **网络安全**：数据加密、身份认证
- **运行安全**：操作票、工作票管理

## 性能指标
- **供电可靠性**：≥99.9%（年可用率）
- **电能质量**：电压偏差≤±5%，频率偏差≤±0.2Hz
- **切换时间**：≤100ms（并离网切换时间）
- **系统效率**：≥95%（整体能源利用效率）

请根据具体应用场景调整系统配置和技术方案。`
  },
  {
    name: '虚拟电厂技术方案',
    content: `请生成一份专业的虚拟电厂技术方案文档，重点包含以下内容：

## 系统概述
- 虚拟电厂类型：[供给侧VPP/需求侧VPP/混合型VPP]
- 聚合资源：[分布式光伏/储能/可控负荷/电动汽车/热泵等]
- 服务市场：[电能量市场/辅助服务市场/容量市场]
- 运营模式：[独立运营/代理运营/平台运营]

## 核心功能设计
### 2.1 资源聚合管理
- **资源接入**：多类型分布式资源统一接入
- **资源建模**：发电/储能/负荷特性建模
- **资源评估**：可调节能力评估、可靠性分析
- **资源优化**：聚合资源组合优化配置

### 2.2 预测与调度
- **负荷预测**：短期/中期/长期负荷预测
- **新能源预测**：光伏/风电出力预测
- **价格预测**：电力市场价格预测模型
- **优化调度**：多时间尺度协调优化调度

### 2.3 市场交易
- **交易策略**：日前/实时市场交易策略
- **风险管理**：市场风险识别与控制
- **结算管理**：电量电费结算、收益分配
- **合同管理**：购售电合同、服务合同管理

### 2.4 需求响应
- **价格响应**：基于电价的需求响应
- **激励响应**：补贴激励的负荷调节
- **紧急响应**：电网紧急状态下的快速响应
- **用户管理**：用户画像、响应能力评估

## 系统架构设计
### 3.1 技术架构
- **云平台架构**：微服务、容器化部署
- **边缘计算**：就地数据处理、实时控制
- **通信网络**：5G/4G/光纤/电力载波
- **数据安全**：区块链、加密传输、隐私保护

### 3.2 业务架构
- **资源层**：分布式电源、储能、可控负荷
- **聚合层**：资源聚合器、本地控制器
- **平台层**：VPP运营平台、交易平台
- **应用层**：调度应用、交易应用、运维应用

## 关键算法
### 4.1 预测算法
- **机器学习**：神经网络、支持向量机
- **深度学习**：LSTM、CNN、Transformer
- **集成学习**：随机森林、梯度提升
- **时间序列**：ARIMA、状态空间模型

### 4.2 优化算法
- **线性规划**：单纯形法、内点法
- **非线性规划**：序列二次规划、遗传算法
- **动态规划**：多阶段决策优化
- **强化学习**：Q-learning、Actor-Critic

### 4.3 控制算法
- **分层控制**：集中式/分布式协调控制
- **模型预测控制**：滚动优化、反馈校正
- **鲁棒控制**：不确定性处理、鲁棒优化
- **自适应控制**：参数自适应、结构自适应

## 市场机制
### 5.1 电能量市场
- **日前市场**：次日电量交易
- **实时市场**：实时电量平衡
- **中长期市场**：月度、年度电量交易
- **偏差考核**：实际与计划偏差处理

### 5.2 辅助服务市场
- **调频服务**：AGC调频、一次调频
- **调压服务**：无功调节、电压支撑
- **备用服务**：旋转备用、非旋转备用
- **黑启动服务**：系统恢复支撑能力

## 商业模式
### 6.1 收益来源
- **电量收益**：电能量市场价差收益
- **服务收益**：辅助服务市场收益
- **容量收益**：容量市场、容量补偿
- **增值服务**：能效服务、碳交易

### 6.2 成本分析
- **平台成本**：系统建设、运维成本
- **通信成本**：数据传输、网络租赁
- **交易成本**：市场准入、交易手续费
- **风险成本**：市场风险、技术风险

## 性能指标
- **聚合规模**：≥100MW（聚合容量）
- **响应时间**：≤5分钟（调度指令响应）
- **预测精度**：≥85%（日前负荷预测）
- **可用率**：≥95%（系统可用率）

请根据具体市场环境和政策要求调整方案设计。`
  },
  {
    name: '能源管理系统技术方案',
    content: `请生成一份专业的能源管理系统技术方案文档，重点包含以下内容：

## 系统概述
- 应用场景：[工业企业/商业建筑/园区/医院/学校/数据中心等]
- 管理范围：[电力/燃气/蒸汽/压缩空气/给排水等]
- 系统规模：[监测点数量/用能设备数量/管理区域面积]
- 节能目标：[能耗降低比例/碳减排目标/成本节约目标]

## 核心功能设计
### 2.1 能耗监测
- **实时监测**：电/气/水/热等能源实时监测
- **分项计量**：按区域/设备/工艺分项计量
- **数据采集**：多协议设备数据采集
- **数据存储**：历史数据存储、备份恢复

### 2.2 能效分析
- **能耗分析**：能耗趋势、同比环比分析
- **能效评估**：单位产品能耗、能效指标
- **对标分析**：行业对标、内部对标
- **异常检测**：能耗异常自动识别报警

### 2.3 节能优化
- **负荷预测**：基于历史数据的负荷预测
- **优化调度**：设备运行优化调度
- **需量管理**：电力需量控制、削峰填谷
- **节能建议**：基于数据分析的节能建议

### 2.4 碳资产管理
- **碳排放核算**：温室气体排放量计算
- **碳足迹追踪**：产品全生命周期碳足迹
- **碳交易管理**：碳配额、CCER交易管理
- **碳中和规划**：碳达峰碳中和路径规划

## 系统架构设计
### 3.1 硬件架构
- **感知层**：智能电表、流量计、温度传感器
- **传输层**：工业网关、通信模块
- **存储层**：时序数据库、关系数据库
- **应用层**：能源管理平台、移动应用

### 3.2 软件架构
- **数据采集层**：多协议数据采集服务
- **数据处理层**：数据清洗、计算、分析
- **业务逻辑层**：能耗分析、优化算法
- **展示层**：Web界面、移动APP、大屏展示

## 关键技术
### 4.1 数据采集技术
- **通信协议**：Modbus、DL/T645、OPC UA
- **无线通信**：LoRa、NB-IoT、WiFi、4G/5G
- **边缘计算**：就地数据处理、实时控制
- **数据融合**：多源数据融合、数据质量控制

### 4.2 数据分析技术
- **时间序列分析**：趋势分析、周期性分析
- **机器学习**：聚类分析、回归预测
- **深度学习**：神经网络、深度神经网络
- **优化算法**：遗传算法、粒子群算法

### 4.3 可视化技术
- **实时监控**：实时数据展示、动态图表
- **3D可视化**：三维场景、设备建模
- **移动端**：响应式设计、移动应用
- **大屏展示**：数据大屏、领导驾驶舱

## 节能策略
### 5.1 设备优化
- **设备选型**：高效设备选型建议
- **运行优化**：设备运行参数优化
- **维护策略**：预防性维护、状态检修
- **更新改造**：设备更新改造建议

### 5.2 系统优化
- **供配电优化**：变压器经济运行
- **空调系统优化**：温度设定、运行时间
- **照明系统优化**：智能照明控制
- **工艺优化**：生产工艺参数优化

### 5.3 管理优化
- **制度建设**：能源管理制度、考核制度
- **人员培训**：节能意识、操作技能
- **激励机制**：节能奖励、能耗考核
- **持续改进**：PDCA循环、持续优化

## 标准规范
### 6.1 国家标准
- **GB/T 23331**：能源管理体系要求
- **GB/T 15587**：工业企业能源管理导则
- **GB/T 2589**：综合能耗计算通则
- **GB/T 13234**：企业节能量计算方法

### 6.2 行业标准
- **JGJ/T 177**：公共建筑节能检测标准
- **CJJ/T 96**：供热计量技术规程
- **DL/T 645**：多功能电能表通信协议
- **JB/T 9329**：能源监测与管理系统技术规范

## 性能指标
- **数据采集率**：≥99%（数据采集成功率）
- **系统可用率**：≥99.5%（年系统可用率）
- **响应时间**：≤3秒（界面响应时间）
- **节能效果**：≥5%（年节能率）

请根据具体行业特点和用能情况调整技术方案。`
  },
  {
    name: '集控系统技术方案',
    content: `请生成一份专业的集控系统技术方案文档，重点包含以下内容：

## 系统概述
- 集控类型：[新能源集控/火电集控/水电集控/综合集控]
- 管理范围：[风电场/光伏电站/储能电站/变电站等]
- 接入规模：[电站数量/装机容量/电压等级]
- 控制模式：[集中监控/远程控制/无人值守]

## 核心功能设计
### 2.1 数据采集与监视
- **实时数据采集**：电气量、非电气量数据采集
- **状态监视**：设备运行状态、告警信息
- **视频监控**：现场视频监控、安防系统
- **环境监测**：气象数据、环境参数监测

### 2.2 远程控制
- **遥控操作**：断路器、隔离开关远程操作
- **参数设定**：保护定值、控制参数远程设定
- **程序控制**：自动化程序、操作票执行
- **应急控制**：紧急停机、事故处理

### 2.3 生产管理
- **发电计划**：日发电计划制定与执行
- **功率控制**：有功功率、无功功率控制
- **AGC控制**：自动发电控制、调频服务
- **AVC控制**：自动电压控制、无功优化

### 2.4 运维管理
- **设备管理**：设备台账、运行记录
- **缺陷管理**：缺陷登记、处理跟踪
- **检修管理**：检修计划、工作票管理
- **备品备件**：备件库存、采购管理

## 系统架构设计
### 3.1 网络架构
- **骨干网络**：光纤环网、冗余设计
- **接入网络**：电力载波、无线通信
- **安全防护**：网络隔离、防火墙
- **网络管理**：网络监控、故障诊断

### 3.2 系统架构
- **现地控制层**：现地控制单元LCU
- **通信接入层**：通信前置、协议转换
- **应用服务层**：SCADA、EMS、PMS
- **人机交互层**：操作员站、工程师站

### 3.3 数据架构
- **实时数据库**：高速数据存储、检索
- **历史数据库**：长期数据存储、压缩
- **关系数据库**：配置数据、业务数据
- **数据仓库**：数据挖掘、商业智能

## 关键技术
### 4.1 通信技术
- **通信协议**：IEC 61850、IEC 104、Modbus
- **通信介质**：光纤、载波、无线
- **通信安全**：加密传输、身份认证
- **时钟同步**：GPS/北斗授时、IEEE 1588

### 4.2 控制技术
- **分层控制**：集中监控、分散控制
- **冗余技术**：双机热备、负载均衡
- **实时控制**：毫秒级响应、确定性控制
- **智能控制**：专家系统、模糊控制

### 4.3 信息安全
- **网络安全**：防火墙、入侵检测
- **数据安全**：数据加密、备份恢复
- **访问控制**：身份认证、权限管理
- **安全审计**：操作日志、安全评估

## 应用系统
### 5.1 SCADA系统
- **数据采集**：遥测、遥信、遥控、遥调
- **人机界面**：图形化界面、操作画面
- **报警处理**：告警分类、确认、复归
- **历史数据**：数据存储、查询、统计

### 5.2 EMS系统
- **状态估计**：电网状态估计、拓扑分析
- **潮流计算**：电力系统潮流分析
- **安全分析**：静态安全、动态安全
- **优化调度**：经济调度、安全约束

### 5.3 PMS系统
- **设备管理**：设备档案、运行管理
- **检修管理**：检修计划、工作管理
- **缺陷管理**：缺陷记录、处理流程
- **备件管理**：库存管理、采购计划

## 安全防护
### 6.1 物理安全
- **机房安全**：门禁系统、视频监控
- **设备安全**：防雷、接地、消防
- **环境安全**：温湿度、洁净度控制
- **人员安全**：安全培训、操作规程

### 6.2 网络安全
- **边界防护**：防火墙、网闸隔离
- **入侵检测**：异常行为检测、告警
- **病毒防护**：杀毒软件、补丁管理
- **数据保护**：数据加密、访问控制

### 6.3 应用安全
- **身份认证**：用户认证、数字证书
- **权限管理**：角色权限、操作授权
- **审计跟踪**：操作日志、安全审计
- **应急响应**：安全事件响应、恢复

## 性能指标
- **系统可用率**：≥99.9%（年可用率）
- **数据传输延时**：≤100ms（端到端延时）
- **控制响应时间**：≤1秒（遥控操作响应）
- **数据采集周期**：≤1秒（重要数据采集周期）

请根据具体电站类型和管理需求调整技术方案。`
  },
  {
    name: '功率预测系统技术方案',
    content: `请生成一份专业的功率预测系统技术方案文档，重点包含以下内容：

## 系统概述
- 预测对象：[风电功率/光伏功率/负荷功率/净负荷功率]
- 预测时间尺度：[超短期/短期/中期/长期预测]
- 预测精度要求：[日前预测精度/实时预测精度]
- 应用场景：[电网调度/电力交易/运行控制]

## 核心功能设计
### 2.1 数据采集与处理
- **气象数据采集**：风速、风向、温度、湿度、辐照度
- **历史数据管理**：功率历史、气象历史、数据质量控制
- **实时数据处理**：数据清洗、异常检测、缺失值处理
- **数据融合**：多源数据融合、数据一致性检验

### 2.2 预测模型
- **物理模型**：基于物理机理的功率预测模型
- **统计模型**：时间序列、回归分析、状态空间模型
- **机器学习模型**：支持向量机、随机森林、神经网络
- **深度学习模型**：LSTM、CNN、Transformer、GAN

### 2.3 预测算法
- **单点预测**：确定性功率预测
- **概率预测**：功率预测区间、概率分布
- **集合预测**：多模型集成、不确定性量化
- **在线学习**：模型在线更新、自适应学习

### 2.4 预测评估
- **精度评估**：MAE、RMSE、MAPE等指标
- **可靠性评估**：预测区间覆盖率、可靠性指标
- **稳定性评估**：模型稳定性、鲁棒性分析
- **对比分析**：多模型对比、基准模型对比

## 系统架构设计
### 3.1 数据层
- **数据源**：SCADA系统、气象站、数值天气预报
- **数据存储**：时序数据库、关系数据库
- **数据接口**：标准化数据接口、API服务
- **数据质量**：数据验证、清洗、标准化

### 3.2 算法层
- **预处理模块**：数据预处理、特征工程
- **模型训练模块**：离线训练、模型优化
- **预测计算模块**：在线预测、实时计算
- **后处理模块**：预测修正、平滑处理

### 3.3 应用层
- **预测服务**：预测结果发布、API接口
- **可视化展示**：预测曲线、误差分析
- **报告生成**：预测报告、精度统计
- **告警服务**：异常告警、精度告警

## 关键算法
### 4.1 数据预处理
- **异常检测**：基于统计的异常检测、孤立森林
- **缺失值处理**：线性插值、样条插值、机器学习插值
- **数据标准化**：Z-score标准化、Min-Max标准化
- **特征工程**：特征选择、特征构造、降维

### 4.2 预测算法
- **时间序列模型**：ARIMA、状态空间模型、指数平滑
- **机器学习**：支持向量回归、随机森林、梯度提升
- **深度学习**：循环神经网络、卷积神经网络、注意力机制
- **集成学习**：Bagging、Boosting、Stacking

### 4.3 不确定性量化
- **概率预测**：分位数回归、高斯过程
- **区间预测**：Bootstrap、贝叶斯方法
- **集合预测**：多模型集成、权重优化
- **误差建模**：误差分布建模、条件异方差

## 技术特色
### 5.1 多尺度预测
- **超短期预测**：0-4小时，分钟级更新
- **短期预测**：1-3天，小时级预测
- **中期预测**：1-2周，日级预测
- **长期预测**：1-12月，月级预测

### 5.2 多场景适应
- **单机预测**：单台风机、单个光伏阵列
- **场站预测**：风电场、光伏电站整体
- **区域预测**：多场站聚合、区域总功率
- **电网预测**：全网新能源、净负荷预测

### 5.3 智能优化
- **自动特征选择**：基于相关性、重要性的特征选择
- **超参数优化**：网格搜索、贝叶斯优化、遗传算法
- **模型自动选择**：基于性能的模型自动选择
- **在线自适应**：概念漂移检测、模型在线更新

## 数据要求
### 6.1 气象数据
- **观测数据**：现地气象站观测数据
- **数值预报**：WRF、ECMWF等数值天气预报
- **卫星数据**：卫星云图、辐照度数据
- **雷达数据**：天气雷达、风廓线雷达

### 6.2 功率数据
- **历史功率**：至少1年历史功率数据
- **实时功率**：分钟级实时功率数据
- **设备状态**：设备运行状态、维护记录
- **电网数据**：电网约束、调度指令

## 性能指标
- **日前预测精度**：风电≤20%，光伏≤15%（RMSE/装机容量）
- **超短期预测精度**：风电≤10%，光伏≤8%（1小时RMSE）
- **预测时效性**：≤5分钟（预测计算时间）
- **系统可用率**：≥99.5%（年可用率）

请根据具体预测需求和数据条件调整技术方案。`
  },
  {
    name: '功率控制系统技术方案',
    content: `请生成一份专业的功率控制系统技术方案文档，重点包含以下内容：

## 系统概述
- 控制对象：[风电场/光伏电站/储能系统/微电网/虚拟电厂]
- 控制目标：[有功功率控制/无功功率控制/电压控制/频率控制]
- 控制模式：[自动控制/手动控制/远程控制/本地控制]
- 响应要求：[秒级响应/分钟级响应/小时级响应]

## 核心功能设计
### 2.1 有功功率控制
- **功率限制**：最大功率限制、功率爬坡限制
- **功率调节**：功率设定值跟踪、功率平滑输出
- **AGC控制**：自动发电控制、调频服务参与
- **经济调度**：多机组协调、成本最优分配

### 2.2 无功功率控制
- **电压调节**：电压设定值控制、电压稳定
- **功率因数控制**：功率因数设定、无功补偿
- **AVC控制**：自动电压控制、无功优化
- **电压支撑**：故障时电压支撑、低电压穿越

### 2.3 频率控制
- **一次调频**：频率偏差响应、下垂控制
- **二次调频**：AGC指令跟踪、频率恢复
- **惯量响应**：虚拟惯量控制、频率支撑
- **频率保护**：过频保护、欠频减载

### 2.4 协调控制
- **多目标优化**：有功无功协调、多约束优化
- **分层控制**：集中控制、分散控制、就地控制
- **预测控制**：基于预测的前馈控制
- **自适应控制**：参数自适应、结构自适应

## 系统架构设计
### 3.1 控制架构
- **集中控制层**：调度中心、集控中心
- **场站控制层**：场站控制系统、能量管理系统
- **设备控制层**：变流器控制、机组控制
- **就地控制层**：保护装置、本地控制器

### 3.2 通信架构
- **上行通信**：与调度中心、集控中心通信
- **横向通信**：场站间、设备间通信
- **下行通信**：与设备控制器通信
- **冗余设计**：双通道、多路径通信

### 3.3 软件架构
- **实时控制内核**：实时操作系统、确定性控制
- **控制算法库**：PID、MPC、模糊控制、神经网络
- **通信服务**：多协议支持、数据交换
- **人机界面**：监控画面、参数设置

## 控制算法
### 4.1 经典控制
- **PID控制**：比例积分微分控制、参数整定
- **前馈控制**：扰动补偿、预测控制
- **反馈控制**：闭环控制、稳定性分析
- **串级控制**：多回路控制、解耦控制

### 4.2 现代控制
- **状态空间控制**：状态反馈、观测器设计
- **最优控制**：LQR、LQG、动态规划
- **鲁棒控制**：H∞控制、μ综合
- **自适应控制**：模型参考自适应、自校正控制

### 4.3 智能控制
- **模糊控制**：模糊推理、模糊PID
- **神经网络控制**：BP网络、RBF网络
- **遗传算法**：参数优化、结构优化
- **强化学习**：Q学习、策略梯度

### 4.4 预测控制
- **模型预测控制**：滚动优化、反馈校正
- **广义预测控制**：CARIMA模型、递推最小二乘
- **动态矩阵控制**：阶跃响应模型、二次规划
- **非线性预测控制**：非线性模型、非线性优化

## 控制策略
### 5.1 风电控制策略
- **最大功率跟踪**：MPPT控制、风能利用系数优化
- **功率限制控制**：桨距角控制、转速控制
- **低电压穿越**：无功支撑、有功恢复
- **风电场协调**：机组协调、功率分配

### 5.2 光伏控制策略
- **最大功率跟踪**：MPPT算法、阴影处理
- **功率限制控制**：逆变器限功率、组串控制
- **电压调节**：无功功率调节、电压支撑
- **光伏电站协调**：逆变器协调、功率平衡

### 5.3 储能控制策略
- **充放电控制**：SOC管理、功率控制
- **电压频率支撑**：虚拟同步机、下垂控制
- **平滑功率输出**：功率滤波、波动抑制
- **多储能协调**：分布式储能、集中控制

## 性能要求
### 6.1 控制精度
- **有功功率控制精度**：≤±2%额定功率
- **无功功率控制精度**：≤±5%额定容量
- **电压控制精度**：≤±1%额定电压
- **频率控制精度**：≤±0.1Hz

### 6.2 响应时间
- **有功功率响应时间**：≤10秒
- **无功功率响应时间**：≤5秒
- **AGC响应时间**：≤1分钟
- **紧急控制响应时间**：≤1秒

### 6.3 稳定性指标
- **超调量**：≤10%
- **调节时间**：≤30秒
- **稳态误差**：≤2%
- **抗干扰能力**：≥20dB

## 安全保护
### 7.1 设备保护
- **过载保护**：电流过载、功率过载
- **过压保护**：交流过压、直流过压
- **欠压保护**：电压跌落、失压保护
- **频率保护**：过频保护、欠频保护

### 7.2 系统保护
- **稳定性保护**：振荡检测、阻尼控制
- **连锁保护**：设备连锁、系统连锁
- **故障隔离**：故障检测、快速隔离
- **恢复控制**：故障恢复、系统重启

## 性能指标
- **控制系统可用率**：≥99.9%
- **控制指令执行率**：≥98%
- **系统响应时间**：≤设计要求
- **控制精度**：满足电网要求

请根据具体控制需求和设备特性调整技术方案。`
  },
  {
    name: '电能质量检测系统技术方案',
    content: `请生成一份专业的电能质量检测系统技术方案文档，重点包含以下内容：

## 系统概述
- 检测对象：[电网/工业用户/新能源场站/配电网]
- 检测参数：[电压/电流/频率/谐波/闪变/不平衡度等]
- 监测点位：[变电站/配电房/用户侧/设备侧]
- 应用目标：[质量评估/故障诊断/治理指导/考核计量]

## 核心功能设计
### 2.1 实时监测
- **基本电气量**：电压、电流、功率、频率实时监测
- **电能质量参数**：谐波、间谐波、闪变、不平衡度
- **暂态现象**：电压暂降、暂升、中断、冲击
- **波形记录**：故障录波、暂态录波、连续录波

### 2.2 数据分析
- **统计分析**：95%概率值、最大值、最小值统计
- **趋势分析**：长期趋势、周期性变化、异常检测
- **相关性分析**：多参数关联、因果关系分析
- **频谱分析**：FFT分析、小波分析、希尔伯特变换

### 2.3 质量评估
- **标准符合性**：GB/T 12325、IEC 61000标准评估
- **质量等级**：电能质量等级划分、合格率统计
- **影响评估**：对设备、系统的影响评估
- **经济损失**：质量问题造成的经济损失评估

### 2.4 故障诊断
- **扰动源定位**：谐波源、闪变源、不平衡源定位
- **传播路径分析**：扰动传播路径、影响范围
- **设备状态诊断**：基于电能质量的设备状态诊断
- **预警预测**：质量恶化预警、故障预测

## 系统架构设计
### 3.1 硬件架构
- **检测终端**：电能质量监测装置、智能电表
- **通信设备**：工业交换机、光纤收发器
- **服务器设备**：数据服务器、应用服务器
- **显示设备**：监控大屏、工作站、移动终端

### 3.2 软件架构
- **数据采集层**：多协议数据采集、实时数据处理
- **数据存储层**：时序数据库、关系数据库
- **分析计算层**：电能质量分析算法、统计计算
- **应用服务层**：Web服务、API接口、报表服务

### 3.3 网络架构
- **现场网络**：RS485、以太网、光纤网络
- **传输网络**：专网、VPN、4G/5G无线
- **安全防护**：防火墙、加密传输、访问控制
- **时钟同步**：GPS/北斗授时、IEEE 1588

## 检测技术
### 4.1 采样技术
- **同步采样**：基波同步、高精度采样
- **高速采样**：高采样率、抗混叠滤波
- **多通道采样**：多路同步、相位一致性
- **自适应采样**：采样率自适应、数据压缩

### 4.2 信号处理
- **数字滤波**：FIR、IIR滤波器设计
- **频域分析**：FFT、DFT、窗函数应用
- **时频分析**：小波变换、短时傅里叶变换
- **模式识别**：机器学习、深度学习算法

### 4.3 参数计算
- **有效值计算**：真有效值、基波有效值
- **谐波分析**：谐波含量、总谐波畸变率
- **闪变计算**：短闪变、长闪变、闪变严重度
- **不平衡度**：电压不平衡度、电流不平衡度

## 检测参数
### 5.1 稳态参数
- **电压偏差**：电压偏差、电压变化
- **频率偏差**：频率偏差、频率变化率
- **谐波**：2-50次谐波含量、THD
- **间谐波**：间谐波含量、间谐波THD
- **电压不平衡度**：负序、零序不平衡度
- **电压闪变**：短闪变Pst、长闪变Plt

### 5.2 暂态参数
- **电压暂降**：暂降幅值、持续时间
- **电压暂升**：暂升幅值、持续时间
- **电压中断**：中断持续时间、中断次数
- **电压冲击**：冲击幅值、冲击持续时间
- **振荡暂态**：振荡频率、衰减时间常数

### 5.3 其他参数
- **功率质量**：功率因数、有功功率、无功功率
- **信号质量**：信噪比、总谐波失真
- **同步质量**：相位差、频率差
- **通信质量**：误码率、延时、丢包率

## 分析算法
### 6.1 传统算法
- **傅里叶变换**：DFT、FFT、加窗FFT
- **滤波算法**：数字滤波、自适应滤波
- **统计算法**：均值、方差、概率分布
- **插值算法**：线性插值、样条插值

### 6.2 现代算法
- **小波变换**：连续小波、离散小波、包络检测
- **希尔伯特变换**：瞬时幅值、瞬时频率
- **经验模态分解**：EMD、EEMD、VMD
- **稀疏表示**：压缩感知、字典学习

### 6.3 智能算法
- **机器学习**：支持向量机、随机森林
- **深度学习**：卷积神经网络、循环神经网络
- **模式识别**：特征提取、分类识别
- **异常检测**：孤立森林、一类支持向量机

## 应用功能
### 7.1 实时监控
- **实时显示**：电能质量参数实时显示
- **告警管理**：超限告警、故障告警
- **趋势显示**：参数变化趋势、历史对比
- **波形显示**：实时波形、录波回放

### 7.2 统计分析
- **日报表**：日统计报表、合格率统计
- **月报表**：月度统计、趋势分析
- **年报表**：年度总结、质量评估
- **自定义报表**：用户自定义统计周期

### 7.3 诊断分析
- **扰动源分析**：扰动源识别、定位
- **影响分析**：扰动影响范围、程度
- **治理建议**：治理方案、设备选型
- **效果评估**：治理前后对比、效果评估

## 性能指标
- **测量精度**：电压电流≤0.2%，功率≤0.5%
- **谐波测量精度**：≤5%（2-50次谐波）
- **采样频率**：≥10.24kHz（512点/周波）
- **数据存储**：≥1年详细数据，≥5年统计数据
- **系统可用率**：≥99.5%

请根据具体应用场景和检测需求调整技术方案。`
  }
]; 