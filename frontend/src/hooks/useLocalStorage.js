/**
 * 本地存储Hook
 */
export const useLocalStorage = () => {
    /**
     * 获取本地存储数据
     * @param {string} key 键
     * @returns {any} 值
     */
    const getItem = (key) => {
        try {
            const value = localStorage.getItem(key);
            return value ? JSON.parse(value) : null;
        } catch (error) {
            console.error('Error getting item from localStorage:', error);
            return null;
        }
    };

    /**
     * 设置本地存储数据
     * @param {string} key 键
     * @param {any} value 值
     */
    const setItem = (key, value) => {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('Error setting item in localStorage:', error);
        }
    };

    /**
     * 移除本地存储数据
     * @param {string} key 键
     */
    const removeItem = (key) => {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('Error removing item from localStorage:', error);
        }
    };

    /**
     * 清空本地存储数据
     */
    const clear = () => {
        try {
            localStorage.clear();
        } catch (error) {
            console.error('Error clearing localStorage:', error);
        }
    };

    return {
        getItem,
        setItem,
        removeItem,
        clear
    };
}; 