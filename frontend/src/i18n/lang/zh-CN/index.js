/*
 * 中文国际化
 * @Date:      2022-09-06 20:01:06
 */
import antd from 'ant-design-vue/es/locale/zh_CN';
import dayjs from 'dayjs/locale/zh-cn';

export default {
    'antdLocale': antd,
    'dayjsLocale': dayjs,
    'setting.title': '网站设置',
    'setting.table.yHeight': '表格高度',
    'setting.pagetag.location': '标签页位置',
    'setting.color': '主题颜色',
    'setting.menu.layout': '菜单布局',
    'setting.menu.width': '菜单宽度',
    'setting.menu.theme': '菜单主题',
    'setting.compact': '页面紧凑',
    'setting.border.radius': '页面圆角',
    'setting.page.width': '页面宽度',
    'setting.bread': '面包屑',
    'setting.flatPattern': '菜单展开模式',
    'setting.pagetag': '标签页',
    'setting.pagetag.style': '标签页样式',
    'setting.footer': '页脚',
    'setting.helpdoc': '帮助文档',
    'setting.helpdoc.expand': '帮助文档展开',
    'setting.watermark': '水印'
};
