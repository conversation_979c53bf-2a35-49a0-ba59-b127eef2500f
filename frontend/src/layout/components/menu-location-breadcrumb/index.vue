<!--
  * 面包屑
-->
<template>
    <a-breadcrumb separator=">" v-if="breadCrumbFlag" class="breadcrumb">
        <a-breadcrumb-item v-for="(item, index) in parentMenuList" :key="index">{{ item.title }}</a-breadcrumb-item>
        <a-breadcrumb-item>{{ currentPageTitle }}</a-breadcrumb-item>
    </a-breadcrumb>
</template>
<script setup>
    import { useRoute } from 'vue-router';
    import { useUserStore } from '/@/store/modules/system/user';
    import { computed, watch, ref } from 'vue';
    import { useAppConfigStore } from '/@/store/modules/system/app-config';
    import { useDynamicTitleReader } from '/@/composables/useDynamicTitle.js';

    // 是否显示面包屑
    const breadCrumbFlag = computed(() => useAppConfigStore().$state.breadCrumbFlag);

    let currentRoute = useRoute();
    const { getCurrentTitle, dynamicTitles } = useDynamicTitleReader();

    // 响应式的当前页面标题
    const currentPageTitle = computed(() => {
        // 获取当前路由的动态标题，如果没有则使用路由meta中的标题
        return getCurrentTitle();
    });

    //根据路由监听面包屑
    const parentMenuList = computed(() => {
        let currentName = currentRoute.name;
        if (!currentName || typeof currentName !== 'string') {
            return [];
        }
        let menuParentIdListMap = useUserStore().getMenuParentIdListMap;
        return menuParentIdListMap.get(currentName) || [];
    });
</script>
<style scoped lang="less">
    .breadcrumb {
        line-height: @page-tag-height;
    }
</style>
