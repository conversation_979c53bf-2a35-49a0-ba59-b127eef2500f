/**
 * 菜单导航共用逻辑
 */
import { useUserStore } from '/@/store/modules/system/user';

/**
 * 智能菜单导航函数
 * 检查是否已存在相同路径的标签页，如果存在则跳转到该标签页（保持其查询参数）
 * 如果不存在，则创建新的标签页，并清理可能存在的重复标签页
 */
export function smartMenuNavigation(menu, router) {
    const userStore = useUserStore();
    const tagNav = userStore.getTagNav || [];
    
    // 检查是否已存在相同路径的标签页
    const existingTag = tagNav.find((tag) => {
        const menuRouterList = userStore.getMenuRouterList || [];
        const menuRouter = menuRouterList.find((m) => m.menuId.toString() === tag.menuName);
        return menuRouter && menuRouter.path === menu.path;
    });

    if (existingTag) {
        // 如果存在相同路径的标签页，直接跳转到该标签页（保持其查询参数）
        router.push({ 
            name: existingTag.menuName, 
            query: existingTag.menuQuery || {} 
        });
    } else {
        // 清理可能存在的重复标签页（相同路径但不同menuName的标签页）
        const duplicateTags = tagNav.filter((tag) => {
            const menuRouterList = userStore.getMenuRouterList || [];
            const menuRouter = menuRouterList.find((m) => m.menuId.toString() === tag.menuName);
            return menuRouter && menuRouter.path === menu.path && 
                   tag.menuName !== menu.menuId.toString();
        });
        
        // 关闭重复的标签页
        duplicateTags.forEach(tag => {
            userStore.closeTagNav(tag.menuName, false);
        });
        
        // 跳转到新页面
        userStore.deleteKeepAliveIncludes(menu.menuId.toString());
        router.push({ name: menu.menuId.toString() });
    }
}
