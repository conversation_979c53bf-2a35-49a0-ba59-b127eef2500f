/*
 *  ajax请求 */
import { message, Modal } from 'ant-design-vue';
import axios from 'axios';
import { localRead } from '/@/utils/local-util';
import { useUserStore } from '/@/store/modules/system/user';
import { decryptData, encryptData } from './encrypt';
import { DATA_TYPE_ENUM } from '../constants/common-const';
import _ from 'lodash';
import LocalStorageKeyConst from '/@/constants/local-storage-key-const.js';
import { saveAs } from 'file-saver';

// token的消息头
const TOKEN_HEADER = 'Authorization';

// 创建axios对象
const smartAxios = axios.create({
    baseURL: import.meta.env.VITE_APP_API_URL
});

// 退出系统
function logout() {
    useUserStore().logout();
    location.href = '/';
}

// ================================= 请求拦截器 =================================

smartAxios.interceptors.request.use(
    (config) => {
        // 在发送请求之前消息头加入token token
        const token = localRead(LocalStorageKeyConst.USER_TOKEN);
        if (token) {
            config.headers[TOKEN_HEADER] = 'Bearer ' + token;
        } else {
            delete config.headers[TOKEN_HEADER];
        }
        return config;
    },
    (error) => {
        // 对请求错误做些什么
        return Promise.reject(error);
    }
);

// ================================= 响应拦截器 =================================

// 添加响应拦截器
smartAxios.interceptors.response.use(
    (response) => {
        // 如果是blob类型的响应，直接返回响应对象，不做进一步处理
        if (response.config.responseType === 'blob') {
            // 检查是否为错误响应（通常会有特定的Content-Type）
            const contentType = response.headers['content-type'] || response.headers['Content-Type'] || '';
            if (contentType.includes('application/json')) {
                // 可能是错误信息的JSON，需要读取内容
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => {
                        try {
                            const jsonData = JSON.parse(reader.result);
                            if (jsonData.code && jsonData.code !== 0) {
                                // 是错误信息，按普通JSON错误处理
                                message.error(jsonData.msg || '操作失败');
                                reject(jsonData);
                            } else {
                                // 不是错误，可能是正常的JSON数据，仍然返回原始响应
                                resolve(response);
                            }
                        } catch (e) {
                            // 不是JSON格式，是正常的二进制数据
                            resolve(response);
                        }
                    };
                    reader.onerror = () => resolve(response); // 读取失败也返回原始响应
                    reader.readAsText(response.data.slice(0, 1000)); // 只读取前1000字节判断
                });
            }
            return Promise.resolve(response);
        }
        
        // 根据content-type ，判断是否为 json 数据
        let contentType = response.headers['content-type'] || response.headers['Content-Type'] || '';
        if (contentType.indexOf('application/json') === -1) {
            return Promise.resolve(response);
        }

        // 如果是json数据
        if (response.data && response.data instanceof Blob) {
            return Promise.reject(response.data);
        }

        // 如果是加密数据
        if (response.data.dataType === DATA_TYPE_ENUM.ENCRYPT.value) {
            response.data.encryptData = response.data.data;
            let decryptStr = decryptData(response.data.data);
            if (decryptStr) {
                response.data.data = JSON.parse(decryptStr);
            }
        }

        const res = response.data;
        if (res.code && res.code !== 0) {
            // `token` 过期或者账号已在别处登录
            if (res.code === 30007 || res.code === 30008) {
                message.destroy();
                message.error('您没有登录，请重新登录');
                setTimeout(logout, 300);
                return Promise.reject(response);
            }

            // 等保安全的登录提醒
            if (res.code === 30010 || res.code === 30011) {
                Modal.error({
                    title: '重要提醒',
                    content: res.msg
                });
                return Promise.reject(response);
            }

            // 长时间未操作系统，需要重新登录
            if (res.code === 30012) {
                Modal.error({
                    title: '重要提醒',
                    content: res.msg,
                    onOk: logout
                });
                setTimeout(logout, 3000);
                return Promise.reject(response);
            }
            message.destroy();
            message.error(res.msg);
            return Promise.reject(response);
        } else {
            return Promise.resolve(res);
        }
    },
    (error) => {
        // 对响应错误做点什么
        if (error.message.indexOf('timeout') !== -1) {
            message.destroy();
            message.error('网络超时');
        } else if (error.message === 'Network Error') {
            message.destroy();
            message.error('网络连接错误');
        } else if (error.message.indexOf('Request') !== -1) {
            message.destroy();
            message.error('网络发生错误');
        }
        return Promise.reject(error);
    }
);

// ================================= 对外提供请求方法：通用请求，get， post, 下载download等 =================================

/**
 * get请求
 */
export const getRequest = (url, params) => {
    return request({ url, method: 'get', params });
};

/**
 * 通用请求封装
 * @param config
 */
export const request = (config) => {
    return smartAxios.request(config);
};

/**
 * post请求
 */
export const postRequest = (url, data) => {
    return request({
        data,
        url,
        method: 'post'
    });
};

// ================================= 加密 =================================

/**
 * 加密请求参数的post请求
 */
export const postEncryptRequest = (url, data) => {
    return request({
        data: { encryptData: encryptData(data) },
        url,
        method: 'post'
    });
};

/**
 * delete请求
 */
export const deleteRequest = (url, params) => {
    return request({ url, method: 'delete', params });
};

// ================================= 下载 =================================

export const postDownload = function (url, data) {
    request({
        method: 'post',
        url,
        data,
        responseType: 'blob'
    })
        .then((data) => {
            handleDownloadData(data);
        })
        .catch((error) => {
            handleDownloadError(error);
        });
};

/**
 * 文件下载
 */
export const getDownload = function (url, params) {
    request({
        method: 'get',
        url,
        params,
        responseType: 'blob'
    })
        .then((data) => {
            handleDownloadData(data);
        })
        .catch((error) => {
            handleDownloadError(error);
        });
};

function handleDownloadError(error) {
    if (error instanceof Blob) {
        const fileReader = new FileReader();
        fileReader.readAsText(error);
        fileReader.onload = () => {
            const msg = fileReader.result;
            const jsonMsg = JSON.parse(msg);
            message.destroy();
            message.error(jsonMsg.msg);
        };
    } else {
        message.destroy();
        message.error('网络发生错误', error);
    }
}

function handleDownloadData(response) {
    if (!response) {
        return;
    }

    try {
        // 直接从响应中获取二进制数据
        const binaryData = response.data;
        if (!(binaryData instanceof Blob)) {
            console.error('响应数据不是Blob类型:', typeof binaryData);
            message.error('下载失败：响应格式不正确');
            return;
        }
        
        // 确保文件大小正常
        if (binaryData.size === 0) {
            console.error('文件大小为0字节');
            message.error('下载失败：文件内容为空');
            return;
        }
        
        console.log('下载文件大小:', binaryData.size, '字节');
        
        // 获取MIME类型
        let contentType = response.headers['content-type'] || response.headers['Content-Type'] || '';
        console.log('下载响应头Content-Type:', contentType);

        // 根据文件类型确定正确的MIME类型
        if (!contentType || 
            contentType.includes('application/octet-stream') || 
            contentType.includes('binary')) {
            // 尝试根据文件扩展名推断MIME类型
            if (contentType.includes('excel') || contentType.includes('spreadsheetml') || 
                contentType.includes('openxmlformats-officedocument')) {
                contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            }
        }
        
        // 从响应头获取文件名
        let filename = '';
        const contentDisposition = response.headers['content-disposition'] || response.headers['Content-Disposition'] || '';
        console.log('下载响应头Content-Disposition:', contentDisposition);
        
        if (contentDisposition) {
            // 尝试多种文件名提取方式
            // 1. 标准正则表达式
            const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
            const matches = filenameRegex.exec(contentDisposition);
            if (matches && matches[1]) {
                filename = matches[1].replace(/['"]/g, '');
            } 
            // 2. 直接查找filename=
            else if (contentDisposition.includes('filename=')) {
                const parts = contentDisposition.split('filename=');
                if (parts.length > 1) {
                    filename = parts[1].split(';')[0].trim().replace(/"/g, '');
                }
            }
            // 3. 查找fileName= (大写N变体)
            else if (contentDisposition.includes('fileName=')) {
                const parts = contentDisposition.split('fileName=');
                if (parts.length > 1) {
                    filename = parts[1].split(';')[0].trim().replace(/"/g, '');
                }
            }
            
            console.log('从Content-Disposition提取的文件名:', filename);
            
            // URL解码文件名
            try {
                if (filename) {
                    filename = decodeURIComponent(filename);
                }
            } catch (e) {
                console.error('文件名解码失败:', e);
            }
        }
        
        // 如果没有提取到文件名，生成一个默认文件名
        if (!filename) {
            // 生成基于当前时间的文件名
            const now = new Date();
            const dateStr = now.toISOString().replace(/[:.]/g, '-').substring(0, 19);
            
            // 根据内容类型生成合适的扩展名
            let extension = '.dat';
            if (contentType.includes('excel') || contentType.includes('spreadsheetml')) {
                extension = '.xlsx';
            } else if (contentType.includes('pdf')) {
                extension = '.pdf';
            } else if (contentType.includes('word')) {
                extension = '.docx';
            } else if (contentType.includes('text/plain')) {
                extension = '.txt';
            } else if (contentType.includes('image/jpeg')) {
                extension = '.jpg';
            } else if (contentType.includes('image/png')) {
                extension = '.png';
            }
            
            filename = `下载文件_${dateStr}${extension}`;
        }
        
        console.log(`准备下载文件: ${filename}, 类型: ${contentType || '未知'}`);

        // 创建一个新的Blob对象，确保设置正确的MIME类型
        const blob = new Blob([binaryData], { 
            type: contentType || 'application/octet-stream'
        });
        
        // 使用file-saver库保存文件
        saveAs(blob, filename);
        console.log('文件下载完成');
        message.success('文件下载成功');
    } catch (error) {
        console.error('文件下载处理出错:', error);
        message.error('文件下载失败: ' + (error.message || '未知错误'));

        // 备用下载方法
        try {
            if (response && response.data) {
                const blob = new Blob([response.data], { 
                    type: 'application/octet-stream' 
                });
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = '下载文件_' + new Date().getTime() + '.dat';
    document.body.appendChild(link);
    link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
            }
        } catch (fallbackError) {
            console.error('备用下载方法也失败:', fallbackError);
        }
    }
}
