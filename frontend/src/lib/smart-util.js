/**
 * 智能工具函数库
 */
import { saveAs } from 'file-saver';
import { message } from 'ant-design-vue';

/**
 * 下载文件
 * @param {Blob} blob 文件blob对象
 * @param {string} fileName 文件名
 */
export function downloadFile(blob, fileName) {
    // 调整MIME类型，确保Excel文件使用正确的MIME类型
    let fileType = blob.type;
    // 根据文件名调整MIME类型
    if (fileName.endsWith('.xlsx') && (!fileType || !fileType.includes('openxmlformats'))) {
        fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        blob = new Blob([blob], { type: fileType });
    } else if (fileName.endsWith('.xls') && (!fileType || !fileType.includes('ms-excel'))) {
        fileType = 'application/vnd.ms-excel';
        blob = new Blob([blob], { type: fileType });
    }
    
    console.log(`下载文件: ${fileName}, 大小: ${blob.size}字节, 类型: ${fileType}`);
    
    try {
        // 主要方法：使用file-saver库下载
        saveAs(blob, fileName);
    } catch (error) {
        console.error('文件下载失败:', error);
        
        // 备用方法：使用原生API下载
        try {
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            
            // 清理
            setTimeout(() => {
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
            }, 100);
        } catch (fallbackError) {
            console.error('备用下载方法也失败:', fallbackError);
            message.error('文件下载失败，请稍后重试');
        }
    }
}

/**
 * 格式化日期
 * @param {Date|string} date 日期
 * @param {string} format 格式
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');

    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

/**
 * 防抖函数
 * @param {Function} fn 需要防抖的函数
 * @param {number} delay 延迟时间
 * @returns {Function} 防抖后的函数
 */
export function debounce(fn, delay = 300) {
    let timer = null;
    return function (...args) {
        if (timer) clearTimeout(timer);
        timer = setTimeout(() => {
            fn.apply(this, args);
        }, delay);
    };
}

/**
 * 节流函数
 * @param {Function} fn 需要节流的函数
 * @param {number} delay 延迟时间
 * @returns {Function} 节流后的函数
 */
export function throttle(fn, delay = 300) {
    let timer = null;
    let lastTime = 0;
    return function (...args) {
        const now = Date.now();
        if (now - lastTime >= delay) {
            fn.apply(this, args);
            lastTime = now;
        } else {
            if (timer) clearTimeout(timer);
            timer = setTimeout(() => {
                fn.apply(this, args);
                lastTime = Date.now();
            }, delay);
        }
    };
}

/**
 * 深拷贝
 * @param {*} obj 需要深拷贝的对象
 * @returns {*} 深拷贝后的对象
 */
export function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    const clone = Array.isArray(obj) ? [] : {};
    for (let key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            clone[key] = deepClone(obj[key]);
        }
    }
    return clone;
}

/**
 * 获取文件扩展名
 * @param {string} fileName 文件名
 * @returns {string} 文件扩展名
 */
export function getFileExtension(fileName) {
    return fileName.slice((fileName.lastIndexOf('.') - 1 >>> 0) + 2);
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
} 