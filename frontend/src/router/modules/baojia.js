import { MENU_TYPE_ENUM } from '/@/constants/system/menu-const.js';
import SmartLayout from '/@/layout/index.vue';

export const baojiaRouters = [
    {
        path: '/baojia',
        name: '_baojia',
        component: SmartLayout,
        meta: {
            title: '报价管理',
            menuType: MENU_TYPE_ENUM.CATALOG.value,
            icon: 'BarsOutlined'
        },
        children: [
            {
                path: '',
                name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
                component: () => import('/src/views/business/baojia/list.vue'),
                meta: {
                    title: '报价列表',
                    menuType: MENU_TYPE_ENUM.MENU.value,
                    icon: 'BarsOutlined'
                }
            },
            {
                path: 'add',
                name: '<PERSON><PERSON><PERSON>aAdd',
                component: () => import('/@/views/business/baojia/add.vue'),
                meta: {
                    title: '新建报价单',
                    menuType: MENU_TYPE_ENUM.MENU.value,
                    icon: 'PicLeftOutlined',
                    keepAlive: true,
                    // 指定要缓存的组件名称，与组件中defineOptions的name保持一致
                    componentName: 'BaojiaAdd'
                }
            }
        ]
    },
    {
        path: '/ai-assistant',
        name: 'AI助手',
        meta: {
            title: 'AI助手',
            icon: 'robot',
            keepAlive: true
        },
        component: () => import('/@/views/business/ai-assistant/AiAssistantPage.vue')
    }
];
