/* 业务路由 */
import { LAYOUT_ENUM } from '/@/constants/layout-const.js';

/**
 * 业务路由
 */
export const businessRouter = [
    // 业务模块-标准单
    {
        path: '/business/orderstandard',
        name: 'OrderStandard',
        component: () => import('/@/views/business/orderstandard/index.vue'),
        meta: {
            title: '标准单管理',
            layout: LAYOUT_ENUM.LEFT_SIDE_MENU
        }
    },

    // 业务模块-销售单
    {
        path: '/business/sales',
        name: 'Sales',
        component: () => import('/@/views/business/sales/index.vue'),
        meta: {
            title: '销售单管理',
            layout: LAYOUT_ENUM.LEFT_SIDE_MENU
        }
    },

    // 业务模块-物料清单
    {
        path: '/business/materiallist',
        name: 'MaterialList',
        component: () => import('/@/views/business/materiallist/index.vue'),
        meta: {
            title: '物料清单',
            layout: LAYOUT_ENUM.LEFT_SIDE_MENU
        }
    },

    // 业务模块-配件
    {
        path: '/business/spareparts',
        name: 'SpareParts',
        component: () => import('/@/views/business/spareparts/index.vue'),
        meta: {
            title: '配件',
            layout: LAYOUT_ENUM.LEFT_SIDE_MENU
        }
    },

    // 业务模块-产品类型
    {
        path: '/business/product-type',
        name: 'ProductType',
        component: () => import('/@/views/business/product-type/product-type-list.vue'),
        meta: {
            title: '产品类型',
            layout: LAYOUT_ENUM.LEFT_SIDE_MENU
        }
    },

    // 业务模块-设备采购
    {
        path: '/business/equipment-procurement',
        name: 'EquipmentProcurement',
        component: () => import('/@/views/business/equipmentprocurement/index.vue'),
        meta: {
            title: '设备采购',
            layout: LAYOUT_ENUM.LEFT_SIDE_MENU
        }
    },

    // 业务模块-AI技术方案生成
    {
        path: '/business/tech-solution',
        name: 'TechSolution',
        component: () => import('/@/views/business/techsolution/index.vue'),
        meta: {
            title: 'AI技术方案生成',
            layout: LAYOUT_ENUM.LEFT_SIDE_MENU
        }
    },
    
    // 业务模块-技术方案列表
    {
        path: '/business/tech-solution-list',
        name: 'TechSolutionList',
        component: () => import('/@/views/business/techsolution/tech-solution-list.vue'),
        meta: {
            title: '技术方案列表',
            layout: LAYOUT_ENUM.LEFT_SIDE_MENU
        }
    },

    // 业务模块-AI标书生成
    {
        path: '/business/tender',
        name: 'Tender',
        component: () => import('/@/views/business/tender/index.vue'),
        meta: {
            title: 'AI标书生成',
            layout: LAYOUT_ENUM.LEFT_SIDE_MENU
        }
    },

    // 业务模块-标书列表
    {
        path: '/business/tender-list',
        name: 'TenderList',
        component: () => import('/@/views/business/tender/tender-list.vue'),
        meta: {
            title: '标书列表',
            layout: LAYOUT_ENUM.LEFT_SIDE_MENU
        }
    }
];

export const support = {
    // 帮助文档
    helpDoc: {
        path: '/support/help-doc',
        name: 'HelpDoc',
        component: () => import('/@/views/support/help-doc/management/help-doc-manage-list.vue'),
        meta: {
            title: '帮助文档',
            layout: LAYOUT_ENUM.LEFT_SIDE_MENU
        }
    },
    
    // 知识库管理
    knowledgeBase: {
        path: '/support/knowledge',
        name: 'KnowledgeBase',
        component: () => import('/@/views/support/knowledge/index.vue'),
        meta: {
            title: '知识库管理',
            layout: LAYOUT_ENUM.LEFT_SIDE_MENU
        }
    }
}; 