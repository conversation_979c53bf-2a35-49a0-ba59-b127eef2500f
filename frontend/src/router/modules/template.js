import { MENU_TYPE_ENUM } from '/@/constants/system/menu-const.js';
import SmartLayout from '/@/layout/index.vue';

export const templateRouters = [
    {
        path: '/template',
        name: '_template',
        component: SmartLayout,
        meta: {
            title: '模板管理',
            menuType: MENU_TYPE_ENUM.CATALOG.value,
            icon: 'HomeOutlined'
        },
        children: [
            {
                path: 'list',
                name: 'TemplateList',
                component: () => import('/src/views/business/template/list.vue'),
                meta: {
                    title: '模板列表',
                    menuType: MENU_TYPE_ENUM.MENU.value,
                    icon: 'BarsOutlined'
                }
            },
            {
                path: 'add',
                name: 'TemplateAdd',
                component: () => import('/src/views/business/template/add.vue'),
                meta: {
                    title: '新建模板',
                    menuType: MENU_TYPE_ENUM.MENU.value,
                    icon: 'PicLeftOutlined',
                    keepAlive: true,
                    // 指定要缓存的组件名称，与组件中defineOptions的name保持一致
                    componentName: 'TemplateAdd'
                }
            }
        ]
    }
];
