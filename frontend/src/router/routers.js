/* 所有路由入口 */
import { homeRouters } from './system/home';
import { loginRouters } from './system/login';
import { helpDocRouters } from './support/help-doc';
import { templateRouters } from './modules/template';
import { baojiaRouters } from './modules/baojia';
import supportRouters from './routes/modules/support';
import NotFound from '/@/views/system/40X/404.vue';
import NoPrivilege from '/@/views/system/40X/403.vue';
import { businessRouter, support } from './modules/business-router';

export const routerArray = [
    ...loginRouters,
    ...homeRouters,
    ...helpDocRouters,
    ...templateRouters,
    ...baojiaRouters,
    supportRouters,
    support.helpDoc,
    support.knowledgeBase,
    { path: '/:pathMatch(.*)*', name: '404', component: NotFound },
    { path: '/403', name: '403', component: NoPrivilege }
];
