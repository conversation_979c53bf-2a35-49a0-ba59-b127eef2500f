import SmartLayout from '/@/layout/index.vue';

export default {
  path: '/support',
  name: 'Support',
  component: SmartLayout,
  meta: {
    title: '支持服务',
    icon: 'support'
  },
  children: [
    {
      path: 'document-analysis',
      name: 'DocumentAnalysis',
      component: () => import('/@/views/support/document-analysis/index.vue'),
      meta: {
        title: '文档分析',
        icon: 'file-search'
      }
    },
    {
      path: 'document-analysis/result-list',
      name: 'DocumentAnalysisResultList',
      component: () => import('/@/views/support/document-analysis/result-list/index.vue'),
      meta: {
        title: '分析结果列表',
        icon: 'file-text'
      }
    },
    {
      path: 'document-analysis/detail',
      name: 'DocumentAnalysisDetail',
      component: () => import('/@/views/support/document-analysis/detail/index.vue'),
      meta: {
        title: '分析结果详情',
        icon: 'file-text'
      }
    }
  ]
};