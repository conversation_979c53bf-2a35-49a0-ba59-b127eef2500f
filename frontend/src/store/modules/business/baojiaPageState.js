/* 报价页面状态管理 */
import { defineStore } from 'pinia';

const STORAGE_KEY = 'baojia-page-state';

export const useBaojiaPageStateStore = defineStore('baojiaPageState', {
    state: () => ({
        // 报价编辑页状态
        quotationEditState: {
            form: null,
            excelData: null,
            lastSaved: null,
            isDirty: false
        },
        // 模板编辑页状态
        templateEditState: {
            form: null,
            excelData: null,
            lastSaved: null,
            isDirty: false
        }
    }),
    
    getters: {
        // 获取报价编辑状态
        getQuotationState(state) {
            return state.quotationEditState;
        },
        
        // 获取模板编辑状态
        getTemplateState(state) {
            return state.templateEditState;
        },
        
        // 检查报价是否有未保存的更改
        hasQuotationChanges(state) {
            return state.quotationEditState.isDirty && state.quotationEditState.form !== null;
        },
        
        // 检查模板是否有未保存的更改
        hasTemplateChanges(state) {
            return state.templateEditState.isDirty && state.templateEditState.form !== null;
        }
    },
    
    actions: {
        // 保存报价编辑状态
        saveQuotationState(formData, excelData) {
            this.quotationEditState = {
                form: formData ? { ...formData } : null,
                excelData: excelData ? JSON.parse(JSON.stringify(excelData)) : null,
                lastSaved: Date.now(),
                isDirty: true
            };
            this._persistToStorage();
        },
        
        // 恢复报价编辑状态
        restoreQuotationState() {
            this._loadFromStorage();
            return this.quotationEditState;
        },
        
        // 清除报价编辑状态
        clearQuotationState() {
            this.quotationEditState = {
                form: null,
                excelData: null,
                lastSaved: null,
                isDirty: false
            };
            this._persistToStorage();
        },
        
        // 保存模板编辑状态
        saveTemplateState(formData, excelData) {
            this.templateEditState = {
                form: formData ? { ...formData } : null,
                excelData: excelData ? JSON.parse(JSON.stringify(excelData)) : null,
                lastSaved: Date.now(),
                isDirty: true
            };
            this._persistToStorage();
        },
        
        // 恢复模板编辑状态
        restoreTemplateState() {
            this._loadFromStorage();
            return this.templateEditState;
        },
        
        // 清除模板编辑状态
        clearTemplateState() {
            this.templateEditState = {
                form: null,
                excelData: null,
                lastSaved: null,
                isDirty: false
            };
            this._persistToStorage();
        },
        
        // 标记报价状态为已保存
        markQuotationSaved() {
            this.quotationEditState.isDirty = false;
            this._persistToStorage();
        },
        
        // 标记模板状态为已保存
        markTemplateSaved() {
            this.templateEditState.isDirty = false;
            this._persistToStorage();
        },
        
        // 清除所有状态
        clearAllStates() {
            this.quotationEditState = {
                form: null,
                excelData: null,
                lastSaved: null,
                isDirty: false
            };
            this.templateEditState = {
                form: null,
                excelData: null,
                lastSaved: null,
                isDirty: false
            };
            this._persistToStorage();
        },
        
        // 私有方法：持久化到sessionStorage
        _persistToStorage() {
            try {
                const stateData = {
                    quotationEditState: this.quotationEditState,
                    templateEditState: this.templateEditState
                };
                sessionStorage.setItem(STORAGE_KEY, JSON.stringify(stateData));
            } catch (error) {
                console.warn('Failed to persist baojia page state:', error);
            }
        },
        
        // 私有方法：从sessionStorage加载
        _loadFromStorage() {
            try {
                const storedData = sessionStorage.getItem(STORAGE_KEY);
                if (storedData) {
                    const parsedData = JSON.parse(storedData);
                    if (parsedData.quotationEditState) {
                        this.quotationEditState = parsedData.quotationEditState;
                    }
                    if (parsedData.templateEditState) {
                        this.templateEditState = parsedData.templateEditState;
                    }
                }
            } catch (error) {
                console.warn('Failed to load baojia page state:', error);
                // 如果加载失败，清除损坏的数据
                sessionStorage.removeItem(STORAGE_KEY);
            }
        },
        
        // 初始化store时从storage加载数据
        initializeFromStorage() {
            this._loadFromStorage();
        }
    }
});