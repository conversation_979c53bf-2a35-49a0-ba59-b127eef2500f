/*
 * 项目的配置信息
 */
import { defineStore } from 'pinia';
import { appDefaultConfig } from '/@/config/app-config';
import localStorageKeyConst from '/@/constants/local-storage-key-const';
import { smartSentry } from '/@/lib/smart-sentry';
import { localRead } from '/@/utils/local-util';

let state = {
    ...appDefaultConfig
};

let appConfigStr = localRead(localStorageKeyConst.APP_CONFIG);
let language = appDefaultConfig.language;
if (appConfigStr) {
    try {
        state = JSON.parse(appConfigStr);
        language = state.language;
    } catch (e) {
        smartSentry.captureError(e);
    }
}

/**
 * 获取初始化的语言
 */
export const getInitializedLanguage = function () {
    return language;
};

export const useAppConfigStore = defineStore('appConfig', {
    state: () => ({
        // 读取config下的默认配置
        ...state,
        // 全屏
        fullScreenFlag: false
    }),
    actions: {
        reset() {
            for (const k in appDefaultConfig) {
                this[k] = appDefaultConfig[k];
            }
        },
        showHelpDoc() {
            this.helpDocExpandFlag = true;
        },
        hideHelpDoc() {
            this.helpDocExpandFlag = false;
        },
        startFullScreen() {
            this.fullScreenFlag = true;
        },
        exitFullScreen() {
            this.fullScreenFlag = false;
        }
    }
});
