/**
 * 日期工具类
 */
import dayjs from 'dayjs';

/**
 * 格式化日期
 * @param {Date|string|number} date 日期对象、字符串或时间戳
 * @param {string} format 格式化模式，默认为'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return '';
  return dayjs(date).format(format);
}

/**
 * 获取当前日期
 * @param {string} format 格式化模式，默认为'YYYY-MM-DD'
 * @returns {string} 当前日期字符串
 */
export function getCurrentDate(format = 'YYYY-MM-DD') {
  return dayjs().format(format);
}

/**
 * 日期加减
 * @param {Date|string|number} date 日期对象、字符串或时间戳
 * @param {number} amount 加减的数量
 * @param {string} unit 单位：day,week,month,year等
 * @param {string} format 格式化模式，默认为'YYYY-MM-DD'
 * @returns {string} 计算后的日期字符串
 */
export function addDate(date, amount, unit = 'day', format = 'YYYY-MM-DD') {
  if (!date) return '';
  return dayjs(date).add(amount, unit).format(format);
}

/**
 * 计算两个日期之间的差值
 * @param {Date|string|number} date1 日期1
 * @param {Date|string|number} date2 日期2
 * @param {string} unit 单位：day,week,month,year等
 * @returns {number} 日期差值
 */
export function diffDate(date1, date2, unit = 'day') {
  if (!date1 || !date2) return 0;
  return dayjs(date1).diff(dayjs(date2), unit);
}

/**
 * 判断日期是否在两个日期之间
 * @param {Date|string|number} date 要判断的日期
 * @param {Date|string|number} startDate 开始日期
 * @param {Date|string|number} endDate 结束日期
 * @returns {boolean} 是否在两个日期之间
 */
export function isBetween(date, startDate, endDate) {
  if (!date || !startDate || !endDate) return false;
  const d = dayjs(date);
  return d.isAfter(dayjs(startDate)) && d.isBefore(dayjs(endDate));
} 