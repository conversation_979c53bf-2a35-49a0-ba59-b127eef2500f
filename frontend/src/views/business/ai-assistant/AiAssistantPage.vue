<template>
  <div class="ai-assistant-page">
    <page-header title="AI智能助手" subtitle="基于通义千问qwen3-30b大模型的智能对话助手">
      <template #extra>
        <a-button type="primary" @click="openHelpDoc">
          <template #icon><QuestionCircleOutlined /></template>
          使用说明
        </a-button>
      </template>
    </page-header>
    
    <a-card :bordered="false" class="main-card">
      <AiAssistant />
    </a-card>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';
import AiAssistant from '/@/components/business/ai-assistant/SimpleAiAssistant.vue';

export default defineComponent({
  name: 'AiAssistantPage',
  components: {
    AiAssistant,
    QuestionCircleOutlined
  },
  setup() {
    const openHelpDoc = () => {
      // 打开帮助文档，这里可以根据实际情况跳转到帮助页面
      window.open('#/support/help-doc/detail/ai-assistant', '_blank');
    };
    
    return {
      openHelpDoc
    };
  }
});
</script>

<style scoped>
.ai-assistant-page {
  background-color: #f0f2f5;
  /* padding: 0 16px 16px; */
  min-height: 100%;
}

.main-card {
  height: calc(100vh - 130px);
}

:deep(.ant-card-body) {
  height: 100%;
  padding: 0;
}
</style> 