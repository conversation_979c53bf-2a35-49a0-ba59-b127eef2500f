<!--
  * 报价单管理
-->
<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="列表类型" class="smart-query-form-item">
                <a-radio-group v-model:value="listType" @change="onListTypeChange">
                    <a-radio-button value="my">我的报价</a-radio-button>
                    <a-radio-button value="all">全部报价</a-radio-button>
                </a-radio-group>
            </a-form-item>
            <a-form-item label="关键字" class="smart-query-form-item">
                <a-input style="width: 300px" v-model:value="queryForm.keyword" placeholder="报价单名称/业务线/产品类型" />
            </a-form-item>
            <a-form-item label="报价人" class="smart-query-form-item" v-show="listType === 'all'">
                <a-select
                    v-model:value="queryForm.userId"
                    style="width: 200px"
                    placeholder="请选择报价人"
                    allowClear
                    show-search
                    :filter-option="filterEmployeeOption"
                >
                    <a-select-option v-for="employee in employeeList" :key="employee.employeeId" :value="employee.employeeId">
                        {{ employee.actualName }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="产品类型" class="smart-query-form-item">
                <a-select
                    v-model:value="queryForm.productTypeId"
                    style="width: 200px"
                    placeholder="请选择产品类型"
                    allowClear
                    show-search
                    :filter-option="filterProductTypeOption"
                    @change="onProductTypeChange"
                >
                    <a-select-option v-for="productType in productTypeList" :key="productType.id" :value="productType.id">
                        {{ productType.name }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="类型" class="smart-query-form-item">
                <a-select
                    v-model:value="queryForm.generateType"
                    style="width: 150px"
                    placeholder="请选择类型"
                    allowClear
                >
                    <a-select-option :value="1">人工</a-select-option>
                    <a-select-option :value="2">AI智能</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item class="smart-query-form-item smart-margin-left10">
                <a-button type="primary" @click="queryData">
                    <template #icon>
                        <SearchOutlined />
                    </template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->

    <a-card size="small" :bordered="false" :hoverable="true">
        <!---------- 表格操作行 begin ----------->
        <a-row class="smart-table-btn-block">
            <div class="smart-table-operate-block">
                <a-button @click="showForm" type="primary">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新建
                </a-button>
                <a-button @click="confirmBatchDelete" type="primary" danger :disabled="selectedRowKeyList.length === 0">
                    <template #icon>
                        <DeleteOutlined />
                    </template>
                    批量删除
                </a-button>
            </div>
            <div class="smart-table-setting-block">
                <TableOperator v-model="columns" :tableId="TABLE_ID_CONST.BUSINESS.TEMPLATE" :refresh="queryData" />
            </div>
        </a-row>
        <!---------- 表格操作行 end ----------->

        <!---------- 表格 begin ----------->
        <a-table
            size="small"
            :dataSource="tableData"
            :columns="columns"
            rowKey="id"
            bordered
            :loading="tableLoading"
            :pagination="false"
            :row-selection="{ 
                selectedRowKeys: selectedRowKeyList, 
                onChange: onSelectChange,
                getCheckboxProps: (record) => ({
                    disabled: !canDelete(record)
                })
            }"
        >
            <template #bodyCell="{ record, column }">
                <template v-if="column.dataIndex === 'generateType'">
                    <a-tag :color="record.generateType === 2 ? 'blue' : 'default'">
                        {{ record.generateType === 2 ? 'AI智能' : '人工' }}
                    </a-tag>
                </template>
                <template v-else-if="column.dataIndex === 'createTime'">
                    <span>{{ formatDate(record.createTime, 'YYYY-MM-DD HH:mm:ss') }}</span>
                </template>
                <template v-else-if="column.dataIndex === 'updateTime'">
                    <span>{{ formatDate(record.updateTime, 'YYYY-MM-DD HH:mm:ss') }}</span>
                </template>
                <template v-else-if="column.dataIndex === 'action'">
                    <div class="smart-table-operate">
                        <a-button v-if="canEdit(record)" @click="showForm(record)" type="link">编辑</a-button>
                        <a-button @click="onCopy(record)" type="link">复制</a-button>
                        <a-button v-if="canDelete(record)" @click="onDelete(record)" danger type="link">删除</a-button>
                    </div>
                </template>
            </template>
        </a-table>
        <!---------- 表格 end ----------->

        <div class="smart-query-table-page">
            <a-pagination
                showSizeChanger
                showQuickJumper
                show-less-items
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="queryForm.pageSize"
                v-model:current="queryForm.pageNum"
                v-model:pageSize="queryForm.pageSize"
                :total="total"
                @change="queryData"
                @showSizeChange="queryData"
                :show-total="(total) => `共${total}条`"
            />
        </div>
    </a-card>
</template>
<script setup>
    import { reactive, ref, onMounted } from 'vue';
    import { message, Modal } from 'ant-design-vue';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { templateApi } from '/@/api/business/template-api';
    import { employeeApi } from '/@/api/system/employee-api';
    import { productTypeApi } from '/@/api/business/product-type-api';
    import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import { useUserStore } from '/@/store/modules/system/user';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import { useRouter, useRoute } from 'vue-router';
    import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';
    import { formatDate } from '/@/lib/smart-util';
    import _ from 'lodash';

    // ---------------------------- 表格列 ----------------------------
    const columns = ref([
        { title: 'ID', width: 90, dataIndex: 'id', ellipsis: true },
        { title: '报价单名称', dataIndex: 'name', ellipsis: true },
        { title: '业务线', dataIndex: 'businessName', ellipsis: true },
        { title: '产品类型', dataIndex: 'productName', ellipsis: true },
        { title: '类型', width: 100, dataIndex: 'generateType', ellipsis: true },
        { title: '创建人', width: 120, dataIndex: 'createUserName', ellipsis: true },
        { title: '创建时间', width: 160, dataIndex: 'createTime', ellipsis: true },
        { title: '最后修改时间', width: 160, dataIndex: 'updateTime', ellipsis: true },
        { title: '操作', dataIndex: 'action', fixed: 'right', width: 140 }
    ]);

    // ---------------------------- 查询数据表单和方法 ----------------------------
    const queryFormState = {
        keyword: '',
        userId: null,
        generateType: undefined,
        contentType: 2,
        listScope: 'my', // 默认显示我的报价
        productTypeId: null,
        productTypeName: null,
        pageNum: 1,
        pageSize: 10
    };
    const queryForm = reactive({ ...queryFormState });
    const tableLoading = ref(false);
    const tableData = ref([]);
    const total = ref(0);
    const selectedRowKeyList = ref([]);
    const router = useRouter();
    const route = useRoute();

    // 列表类型状态
    const listType = ref('my'); // 'my' | 'all'

    // 员工列表
    const employeeList = ref([]);
    const userStore = useUserStore();

    // 产品类型列表
    const productTypeList = ref([]);

    // 员工筛选过滤函数
    function filterEmployeeOption(input, option) {
        return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    }

    // 产品类型筛选过滤函数
    function filterProductTypeOption(input, option) {
        return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    }

    // 产品类型筛选
    function onProductTypeChange(value, option) {
        queryForm.productTypeId = value;
        queryForm.productTypeName = option?.label;
        queryData();
    }

    // 操作权限控制
    function canEdit(record) {
        return listType.value === 'my' || record.createUserId === userStore.employeeId;
    }

    function canDelete(record) {
        return listType.value === 'my' || record.createUserId === userStore.employeeId;
    }

    // 列表类型切换处理
    function onListTypeChange(e) {
        listType.value = e.target.value;
        queryForm.listScope = e.target.value;
        
        if (e.target.value === 'my') {
            // 我的报价：设置为当前用户
            queryForm.userId = userStore.employeeId;
        } else if (e.target.value === 'all') {
            // 全部报价：清空用户筛选，显示所有用户的报价
            queryForm.userId = null;
        }
        
        queryData();
    }

    // 重置查询条件
    function resetQuery() {
        let pageSize = queryForm.pageSize;
        Object.assign(queryForm, queryFormState);
        queryForm.pageSize = pageSize;
        listType.value = 'my';
        queryForm.listScope = 'my';
        queryForm.userId = userStore.employeeId;
        queryData();
    }

    // 查询数据
    async function queryData() {
        tableLoading.value = true;
        try {
            let responseData = await templateApi.query(queryForm);
            tableData.value = responseData.data.list;
            total.value = responseData.data.total;
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            tableLoading.value = false;
        }
    }

    onMounted(async () => {
        // 加载员工列表
        try {
            const res = await employeeApi.queryAll();
            employeeList.value = res.data || [];
        } catch (e) {
            smartSentry.captureError(e);
            message.error('获取员工列表失败');
        }

        // 加载产品类型列表
        try {
            const res = await productTypeApi.getAllEnabled();
            productTypeList.value = res.data || [];
        } catch (e) {
            smartSentry.captureError(e);
            message.error('获取产品类型列表失败');
        }

        // 设置默认为我的报价
        listType.value = 'my';
        queryForm.listScope = 'my';
        queryForm.userId = userStore.employeeId;

        // 查询数据
        queryData();
    });

    // ---------------------------- 添加/修改 ----------------------------
    function showForm(data) {
        // 检查是否已经有打开的报价单添加/编辑页面
        const tagNav = userStore.getTagNav || [];

        // 检查是否有报价单编辑tab打开（通过route name直接匹配）
        const hasOpenAddTab = tagNav.some(tag => tag.menuName === 'BaojiaAdd');

        if (hasOpenAddTab) {
            // 如果已经有打开的tab，提示用户选择操作
            Modal.confirm({
                title: '检测到未保存数据',
                content: '您已打开报价单编辑页面，可能存在未保存的数据。请选择操作：',
                okText: '覆盖数据',
                cancelText: '取消',
                onOk() {
                    // 用户选择覆盖，先关闭现有tab，再跳转
                    userStore.closeTagNav('BaojiaAdd', false);
                    // 延迟一下再跳转，确保tab关闭完成
                    setTimeout(() => {
                        navigateToAdd(data);
                    }, 100);
                },
                onCancel() {
                    // 保留数据：不执行任何操作，保持当前状态
                }
            });
        } else {
            // 没有打开的tab，直接跳转
            navigateToAdd(data);
        }
    }

    function navigateToAdd(data) {
        if (data && data.id) {
            router.push({ path: '/baojia/add', query: { id: data.id } });
        } else {
            router.push({ path: '/baojia/add' });
        }
    }

    // ---------------------------- 单个删除 ----------------------------
    function onDelete(data) {
        Modal.confirm({
            title: '提示',
            content: '确定要删除选中的报价单吗?',
            okText: '删除',
            okType: 'danger',
            onOk() {
                requestDelete(data);
            },
            cancelText: '取消',
            onCancel() {}
        });
    }

    async function requestDelete(data) {
        SmartLoading.show();
        try {
            await templateApi.delete([data.id]);
            message.success('删除成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }

    // ---------------------------- 批量删除 ----------------------------
    function onSelectChange(selectedRowKeys) {
        selectedRowKeyList.value = selectedRowKeys;
    }

    function confirmBatchDelete() {
        if (_.isEmpty(selectedRowKeyList.value)) {
            message.warning('请选择要删除的数据');
            return;
        }

        // 检查选中的记录是否都有删除权限
        const selectedRecords = tableData.value.filter(record => selectedRowKeyList.value.includes(record.id));
        const unauthorizedRecords = selectedRecords.filter(record => !canDelete(record));
        
        if (unauthorizedRecords.length > 0) {
            message.warning('选中的数据中包含您无权删除的记录，请重新选择');
            return;
        }

        Modal.confirm({
            title: '提示',
            content: '确定要批量删除这些报价单吗?',
            okText: '删除',
            okType: 'danger',
            onOk() {
                requestBatchDelete();
            },
            cancelText: '取消',
            onCancel() {}
        });
    }

    async function requestBatchDelete() {
        try {
            SmartLoading.show();
            await templateApi.delete(selectedRowKeyList.value);
            message.success('删除成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }

    // ---------------------------- 复制报价单 ----------------------------
    async function onCopy(record) {
        SmartLoading.show();
        try {
            // 获取报价单详情
            const detailResponse = await templateApi.detail(record.id);
            const baojiaDetail = detailResponse.data;

            // 构造复制的报价单数据
            const copyData = {
                ...baojiaDetail,
                name: `${baojiaDetail.name}_复制`,
                id: undefined, // 移除ID，让后端生成新的ID
                createTime: undefined,
                updateTime: undefined,
                createUserId: undefined,
                createUserName: undefined,
                updateUserId: undefined,
                updateUserName: undefined
            };

            // 调用新增接口保存复制的报价单
            await templateApi.add(copyData);
            message.success('复制成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
            message.error('复制失败');
        } finally {
            SmartLoading.hide();
        }
    }


</script>
