<!--
  *  设备采购表单模态框
-->
<template>
    <a-drawer
        :title="formData.id ? '编辑设备采购' : '添加设备采购'"
        :width="800"
        :visible="visible"
        :body-style="{ paddingBottom: '80px' }"
        @close="onClose"
    >
        <a-form :model="formData" :rules="rules" ref="formRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-form-item label="设备名称" name="equipmentName">
                <a-input v-model:value="formData.equipmentName" placeholder="请输入设备名称" />
            </a-form-item>
            <a-form-item label="供应商名称" name="supplierName">
                <a-input v-model:value="formData.supplierName" placeholder="请输入供应商名称" />
            </a-form-item>
            <a-form-item label="投标品牌" name="biddingBrand">
                <a-input v-model:value="formData.biddingBrand" placeholder="请输入投标品牌" />
            </a-form-item>
            <a-form-item label="投标型号" name="biddingModel">
                <a-input v-model:value="formData.biddingModel" placeholder="请输入投标型号" />
            </a-form-item>
            <a-form-item label="单价(元)" name="unitPrice">
                <a-input-number v-model:value="formData.unitPrice" placeholder="请输入单价" :min="0" :precision="2" style="width: 100%" />
            </a-form-item>
            <a-form-item label="推荐采购比例" name="recommendedProcurementRatio">
                <a-input v-model:value="formData.recommendedProcurementRatio" placeholder="请输入推荐采购比例" />
            </a-form-item>
            <a-form-item label="ERP物料编码" name="erpMaterialCode">
                <a-input v-model:value="formData.erpMaterialCode" placeholder="请输入ERP物料编码" />
            </a-form-item>
            <a-form-item label="通用编码" name="universalCode">
                <a-input v-model:value="formData.universalCode" placeholder="请输入通用编码" />
            </a-form-item>
            <a-form-item label="通用编码报价(元)" name="universalCodeQuotation">
                <a-input-number v-model:value="formData.universalCodeQuotation" placeholder="请输入通用编码报价" :min="0" :precision="2" style="width: 100%" />
            </a-form-item>
            <a-form-item label="供应商种类" name="supplierType">
                <a-input v-model:value="formData.supplierType" placeholder="请输入供应商种类" />
            </a-form-item>
            <a-form-item label="质保周期(月)" name="warrantyPeriodMonths">
                <a-input-number v-model:value="formData.warrantyPeriodMonths" placeholder="请输入质保周期" :min="0" :precision="0" style="width: 100%" />
            </a-form-item>
            <a-form-item label="新型号/品牌" name="isNewModelBrand">
                <a-switch v-model:checked="formData.isNewModelBrand" />
            </a-form-item>
            <a-form-item label="通过测试" name="passedTesting">
                <a-switch v-model:checked="formData.passedTesting" />
            </a-form-item>
            <a-form-item label="可放600深机柜" name="canFit600mmDeepCabinet">
                <a-switch v-model:checked="formData.canFit600mmDeepCabinet" />
            </a-form-item>
            <a-form-item label="投标配置" name="biddingConfig">
                <a-textarea v-model:value="formData.biddingConfig" placeholder="请输入投标配置" :rows="4" />
            </a-form-item>
            <a-form-item label="招标配置" name="tenderConfig">
                <a-textarea v-model:value="formData.tenderConfig" placeholder="请输入招标配置" :rows="4" />
            </a-form-item>
            <a-form-item label="备注" name="remarks">
                <a-textarea v-model:value="formData.remarks" placeholder="请输入备注" :rows="4" />
            </a-form-item>
        </a-form>
        <div class="drawer-footer">
            <a-button @click="onClose">取消</a-button>
            <a-button type="primary" @click="onSubmit" :loading="submitLoading">确定</a-button>
        </div>
    </a-drawer>
</template>
<script setup>
    import { message } from 'ant-design-vue';
    import { reactive, ref, watch } from 'vue';
    import { equipmentProcurementApi } from '/@/api/business/equipment-procurement-api';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { smartSentry } from '/@/lib/smart-sentry';

    const emit = defineEmits(['refresh']);
    const visible = ref(false);
    const submitLoading = ref(false);
    const formRef = ref();

    // 表单数据
    const formData = reactive({
        id: undefined,
        equipmentName: undefined,
        supplierName: undefined,
        biddingBrand: undefined,
        biddingModel: undefined,
        unitPrice: undefined,
        recommendedProcurementRatio: undefined,
        erpMaterialCode: undefined,
        universalCode: undefined,
        universalCodeQuotation: undefined,
        supplierType: undefined,
        warrantyPeriodMonths: undefined,
        isNewModelBrand: false,
        passedTesting: false,
        canFit600mmDeepCabinet: false,
        biddingConfig: undefined,
        tenderConfig: undefined,
        remarks: undefined
    });

    // 表单校验规则
    const rules = {
        equipmentName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
        supplierName: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
        biddingBrand: [{ required: true, message: '请输入投标品牌', trigger: 'blur' }],
        biddingModel: [{ required: true, message: '请输入投标型号', trigger: 'blur' }],
        unitPrice: [{ required: true, message: '请输入单价', trigger: 'blur' }],
        recommendedProcurementRatio: [{ required: true, message: '请输入推荐采购比例', trigger: 'blur' }],
        erpMaterialCode: [{ required: true, message: '请输入ERP物料编码', trigger: 'blur' }],
        universalCode: [{ required: true, message: '请输入通用编码', trigger: 'blur' }],
        universalCodeQuotation: [{ required: true, message: '请输入通用编码报价', trigger: 'blur' }],
        supplierType: [{ required: true, message: '请输入供应商种类', trigger: 'blur' }],
        warrantyPeriodMonths: [{ required: true, message: '请输入质保周期', trigger: 'blur' }]
    };

    // 监听visible变化
    watch(visible, (val) => {
        if (!val) {
            resetForm();
        }
    });

    // 显示抽屉
    function showDrawer(record) {
        visible.value = true;
        if (record) {
            Object.assign(formData, record);
        }
    }

    // 关闭抽屉
    function onClose() {
        visible.value = false;
    }

    // 重置表单
    function resetForm() {
        if (formRef.value) {
            formRef.value.resetFields();
        }
        Object.keys(formData).forEach(key => {
            if (key === 'isNewModelBrand' || key === 'passedTesting' || key === 'canFit600mmDeepCabinet') {
                formData[key] = false;
            } else {
                formData[key] = undefined;
            }
        });
    }

    // 提交表单
    async function onSubmit() {
        try {
            await formRef.value.validate();
            submitLoading.value = true;
            SmartLoading.show();
            const api = formData.id ? equipmentProcurementApi.updateEquipmentProcurement : equipmentProcurementApi.addEquipmentProcurement;
            const res = await api(formData);
            if (res.code === 0) {
                message.success(formData.id ? '更新成功' : '添加成功');
                onClose();
                emit('refresh');
            } else {
                message.error(res.msg || (formData.id ? '更新失败' : '添加失败'));
            }
        } catch (error) {
            if (error.errorFields) {
                message.error('请完善表单信息');
            } else {
                smartSentry.captureError(error);
                message.error(formData.id ? '更新失败' : '添加失败');
            }
        } finally {
            submitLoading.value = false;
            SmartLoading.hide();
        }
    }

    // 暴露方法
    defineExpose({
        showDrawer
    });
</script>
<style scoped lang="less">
    .drawer-footer {
        position: absolute;
        bottom: 0;
        width: 100%;
        border-top: 1px solid #e8e8e8;
        padding: 10px 16px;
        text-align: right;
        left: 0;
        background: #fff;
        border-radius: 0 0 2px 2px;
    }
</style> 