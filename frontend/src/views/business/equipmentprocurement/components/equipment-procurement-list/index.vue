<!--
  *  设备采购列表
-->
<template>
    <a-card class="equipment-procurement-container">
        <div class="header">
            <a-typography-title :level="5">设备采购列表</a-typography-title>
            <div class="query-operate">
                <a-input-search v-model:value.trim="params.keyword" placeholder="设备名称/供应商名称/投标品牌/投标型号" @search="queryEquipmentProcurementByKeyword(true)">
                    <template #enterButton>
                        <a-button type="primary">
                            <template #icon>
                                <SearchOutlined />
                            </template>
                            查询
                        </a-button>
                    </template>
                </a-input-search>
                <a-button @click="reset" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </div>
        </div>
        <div class="btn-group">
            <a-button class="btn" type="primary" @click="showDrawer()" v-privilege="'business:equipmentProcurement:add'">添加设备采购</a-button>
            <a-button class="btn" @click="batchDelete" v-privilege="'business:equipmentProcurement:delete'">批量删除</a-button>
            <span class="smart-table-column-operate">
                <TableOperator v-model="columns" :tableId="TABLE_ID_CONST.BUSINESS.EQUIPMENT_PROCUREMENT" :refresh="queryEquipmentProcurement" />
            </span>
        </div>

        <a-table
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            size="small"
            :columns="columns"
            :data-source="tableData"
            :pagination="false"
            :loading="tableLoading"
            :scroll="{ x: 1200 }"
            row-key="id"
            bordered
        >
            <template #bodyCell="{ text, record, index, column }">
                <template v-if="column.dataIndex === 'tenderConfig' || column.dataIndex === 'biddingConfig' || column.dataIndex === 'remarks'">
                    <a-tooltip :title="text">
                        <div class="configuration-content">{{ text }}</div>
                    </a-tooltip>
                </template>
                <template v-else-if="column.dataIndex === 'unitPrice' || column.dataIndex === 'universalCodeQuotation'">
                    <span>{{ formatPrice(text) }}</span>
                </template>
                <template v-else-if="column.dataIndex === 'isNewModelBrand' || column.dataIndex === 'passedTesting' || column.dataIndex === 'canFit600mmDeepCabinet'">
                    <a-tag :color="text ? 'green' : 'red'">{{ text ? '是' : '否' }}</a-tag>
                </template>
                <template v-else-if="column.dataIndex === 'operate'">
                    <div class="smart-table-operate">
                        <a-button v-privilege="'business:equipmentProcurement:update'" type="link" size="small" @click="showDrawer(record)">编辑</a-button>
                        <a-button v-privilege="'business:equipmentProcurement:delete'" type="link" size="small" @click="deleteItem(record.id)">删除</a-button>
                    </div>
                </template>
            </template>
        </a-table>
        <div class="smart-query-table-page">
            <a-pagination
                showSizeChanger
                showQuickJumper
                show-less-items
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="params.pageSize"
                v-model:current="params.pageNum"
                v-model:pageSize="params.pageSize"
                :total="total"
                @change="queryEquipmentProcurement"
                @showSizeChange="queryEquipmentProcurement"
                :show-total="showTableTotal"
            />
        </div>
        <EquipmentProcurementFormModal ref="equipmentProcurementFormModal" @refresh="queryEquipmentProcurement" />
    </a-card>
</template>
<script setup>
    import { ExclamationCircleOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
    import { message, Modal } from 'ant-design-vue';
    import { computed, createVNode, reactive, ref, watch } from 'vue';
    import { equipmentProcurementApi } from '/@/api/business/equipment-procurement-api';
    import { PAGE_SIZE } from '/@/constants/common-const';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import EquipmentProcurementFormModal from '../equipment-procurement-form-modal/index.vue';
    import { PAGE_SIZE_OPTIONS, showTableTotal } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';

    //字段
    const columns = ref([
        {
            title: '设备名称',
            dataIndex: 'equipmentName',
            width: 150
        },
        {
            title: '供应商名称',
            dataIndex: 'supplierName',
            width: 150
        },
        {
            title: '投标品牌',
            dataIndex: 'biddingBrand',
            width: 120
        },
        {
            title: '投标型号',
            dataIndex: 'biddingModel',
            width: 120
        },
        {
            title: '单价(元)',
            dataIndex: 'unitPrice',
            width: 100
        },
        {
            title: '推荐采购比例',
            dataIndex: 'recommendedProcurementRatio',
            width: 120
        },
        {
            title: 'ERP物料编码',
            dataIndex: 'erpMaterialCode',
            width: 120
        },
        {
            title: '通用编码',
            dataIndex: 'universalCode',
            width: 120
        },
        {
            title: '通用编码报价(元)',
            dataIndex: 'universalCodeQuotation',
            width: 120
        },
        {
            title: '供应商种类',
            dataIndex: 'supplierType',
            width: 120
        },
        {
            title: '质保周期(月)',
            dataIndex: 'warrantyPeriodMonths',
            width: 120
        },
        {
            title: '新型号/品牌',
            dataIndex: 'isNewModelBrand',
            width: 100
        },
        {
            title: '通过测试',
            dataIndex: 'passedTesting',
            width: 100
        },
        {
            title: '可放600深机柜',
            dataIndex: 'canFit600mmDeepCabinet',
            width: 120
        },
        {
            title: '最后更新时间',
            dataIndex: 'lastModifiedDate',
            width: 180
        },
        {
            title: '操作',
            dataIndex: 'operate',
            width: 120,
            fixed: 'right'
        }
    ]);
    const tableData = ref([]);
    const total = ref(0);
    const tableLoading = ref(false);
    const selectedRowKeys = ref([]);
    const params = reactive({
        pageNum: 1,
        pageSize: Number(PAGE_SIZE),
        keyword: undefined
    });

    const equipmentProcurementFormModal = ref();

    // 选择行
    function onSelectChange(keys) {
        selectedRowKeys.value = keys;
    }

    // 格式化价格
    function formatPrice(price) {
        if (!price) return '0.00';
        return Number(price).toFixed(2);
    }

    // 查询设备采购列表
    async function queryEquipmentProcurement() {
        try {
            tableLoading.value = true;
            const res = await equipmentProcurementApi.queryEquipmentProcurement(params);
            if (res.code === 0) {
                tableData.value = res.data.list;
                total.value = res.data.total;
            } else {
                message.error(res.msg || '查询设备采购列表失败');
            }
        } catch (error) {
            smartSentry.captureError(error);
            message.error('查询设备采购列表失败');
        } finally {
            tableLoading.value = false;
        }
    }

    // 根据关键词查询设备采购列表
    function queryEquipmentProcurementByKeyword(resetPage = false) {
        if (resetPage) {
            params.pageNum = 1;
        }
        queryEquipmentProcurement();
    }

    // 重置查询条件
    function reset() {
        params.keyword = undefined;
        queryEquipmentProcurementByKeyword(true);
    }

    // 显示抽屉
    function showDrawer(record) {
        equipmentProcurementFormModal.value.showDrawer(record);
    }

    // 删除设备采购
    function deleteItem(id) {
        Modal.confirm({
            title: '确认删除',
            icon: createVNode(ExclamationCircleOutlined),
            content: '确定要删除该设备采购吗？',
            onOk: async () => {
                try {
                    SmartLoading.show();
                    const res = await equipmentProcurementApi.deleteEquipmentProcurement(id);
                    if (res.code === 0) {
                        message.success('删除成功');
                        queryEquipmentProcurement();
                    } else {
                        // 特殊处理数据不存在的情况
                        if (res.code === 30001 || res.code === 30002) {
                            message.warning('该数据已被删除，正在刷新列表');
                            queryEquipmentProcurement();
                        } else {
                            message.error(res.msg || '删除失败');
                        }
                    }
                } catch (error) {
                    smartSentry.captureError(error);
                    message.error('删除失败');
                } finally {
                    SmartLoading.hide();
                }
            }
        });
    }

    // 批量删除设备采购
    function batchDelete() {
        if (selectedRowKeys.value.length === 0) {
            message.warning('请选择要删除的设备采购');
            return;
        }
        Modal.confirm({
            title: '确认批量删除',
            icon: createVNode(ExclamationCircleOutlined),
            content: '确定要删除选中的设备采购吗？',
            onOk: async () => {
                try {
                    SmartLoading.show();
                    const res = await equipmentProcurementApi.batchDeleteEquipmentProcurement(selectedRowKeys.value);
                    if (res.code === 0) {
                        message.success('批量删除成功');
                        selectedRowKeys.value = [];
                        queryEquipmentProcurement();
                    } else {
                        message.error(res.msg || '批量删除失败');
                    }
                } catch (error) {
                    smartSentry.captureError(error);
                    message.error('批量删除失败');
                } finally {
                    SmartLoading.hide();
                }
            }
        });
    }

    // 初始化
    queryEquipmentProcurement();
</script>
<style scoped lang="less">
    .equipment-procurement-container {
        height: 100%;
        display: flex;
        flex-direction: column;

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;

            .query-operate {
                display: flex;
                align-items: center;
            }
        }

        .btn-group {
            display: flex;
            margin-bottom: 16px;

            .btn {
                margin-right: 8px;
            }
        }

        .configuration-content {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        :deep(.ant-card-body) {
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 16px;
            overflow: hidden;
        }

        :deep(.ant-pagination) {
            margin-top: 16px;
            text-align: right;
        }

        :deep(.ant-table-wrapper) {
            flex: 1;
            overflow: auto;
        }
    }
</style> 