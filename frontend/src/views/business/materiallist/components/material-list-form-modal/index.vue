<!--
  * 物料清单表单模态框
-->
<template>
    <a-modal
        :title="modalTitle"
        :visible="visible"
        :maskClosable="false"
        :confirmLoading="confirmLoading"
        @cancel="closeModal"
        width="700px"
        :afterClose="onClose"
    >
        <a-form
            ref="formRef"
            :model="form"
            :rules="rules"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 19 }"
            @finish="submitForm"
        >
            <a-form-item name="materialCode" label="物料编码">
                <a-input v-model:value="form.materialCode" placeholder="请输入物料编码" :maxLength="32" />
            </a-form-item>
            <a-form-item name="materialName" label="物料名称">
                <a-input v-model:value="form.materialName" placeholder="请输入物料名称" :maxLength="128" />
            </a-form-item>
            <a-form-item name="supplierName" label="供应商名称">
                <a-input v-model:value="form.supplierName" placeholder="请输入供应商名称" :maxLength="128" />
            </a-form-item>
            <a-form-item name="brand" label="品牌">
                <a-input v-model:value="form.brand" placeholder="请输入品牌" :maxLength="64" />
            </a-form-item>
            <a-form-item name="brandFuzzy" label="品牌（模糊）">
                <a-input v-model:value="form.brandFuzzy" placeholder="请输入品牌（模糊）" :maxLength="64" />
            </a-form-item>
            <a-form-item name="model" label="规格型号">
                <a-input v-model:value="form.model" placeholder="请输入规格型号" :maxLength="128" />
            </a-form-item>
            <a-form-item name="unit" label="计量单位">
                <a-input v-model:value="form.unit" placeholder="请输入计量单位" :maxLength="32" />
            </a-form-item>
            <a-form-item name="detailConfig" label="详细配置">
                <a-textarea v-model:value="form.detailConfig" placeholder="请输入详细配置" :auto-size="{ minRows: 3, maxRows: 6 }" :maxLength="1000" />
            </a-form-item>
            <a-form-item name="unitPrice" label="单价">
                <a-input-number 
                    v-model:value="form.unitPrice" 
                    placeholder="请输入单价" 
                    :min="0" 
                    :precision="2" 
                    :step="0.01"
                    style="width: 100%" 
                    addonAfter="元"
                    @change="handlePriceChange"
                />
            </a-form-item>
            <a-form-item name="supplierContact" label="供应商联系人">
                <a-input v-model:value="form.supplierContact" placeholder="请输入供应商联系人" :maxLength="32" />
            </a-form-item>
            <a-form-item name="supplierPhone" label="联系电话">
                <a-input v-model:value="form.supplierPhone" placeholder="请输入联系电话" :maxLength="32" />
            </a-form-item>
        </a-form>
        <template #footer>
            <a-button type="primary" @click="submitForm" :loading="confirmLoading">确定</a-button>
            <a-button @click="closeModal">取消</a-button>
        </template>
    </a-modal>
</template>
<script setup>
    import { message } from 'ant-design-vue';
    import { computed, reactive, ref } from 'vue';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { materialListApi } from '/@/api/business/material-list-api';
    import { smartSentry } from '/@/lib/smart-sentry';

    // ----------------------- emits ---------------------
    const emit = defineEmits(['refresh']);

    // ----------------------- 表单数据 ---------------------
    // 表单ref
    const formRef = ref();
    // 表单数据
    const form = reactive({
        id: undefined,
        materialCode: '',
        materialName: '',
        supplierName: '',
        brand: '',
        brandFuzzy: '',
        model: '',
        unit: '',
        detailConfig: '',
        unitPrice: undefined,
        supplierContact: '',
        supplierPhone: ''
    });
    // 表单校验规则
    const rules = reactive({
        materialName: [
            { required: true, message: '请输入物料名称', trigger: 'blur' },
            { max: 128, message: '物料名称不能超过128个字符', trigger: 'blur' }
        ],
        materialCode: [
            { max: 32, message: '物料编码不能超过32个字符', trigger: 'blur' }
        ],
        supplierName: [
            { max: 128, message: '供应商名称不能超过128个字符', trigger: 'blur' }
        ],
        brand: [
            { max: 64, message: '品牌不能超过64个字符', trigger: 'blur' }
        ],
        brandFuzzy: [
            { max: 64, message: '品牌（模糊）不能超过64个字符', trigger: 'blur' }
        ],
        model: [
            { max: 128, message: '规格型号不能超过128个字符', trigger: 'blur' }
        ],
        unit: [
            { max: 32, message: '计量单位不能超过32个字符', trigger: 'blur' }
        ],
        unitPrice: [
            { required: true, message: '请输入单价', trigger: 'blur' },
            { type: 'number', min: 0, message: '单价必须大于等于0', trigger: 'blur' }
        ],
        supplierContact: [
            { max: 32, message: '供应商联系人不能超过32个字符', trigger: 'blur' }
        ],
        supplierPhone: [
            { max: 32, message: '联系电话不能超过32个字符', trigger: 'blur' }
        ]
    });

    // ----------------------- 模态框数据 ---------------------
    // 模态框是否可见
    const visible = ref(false);
    // 表单确认加载状态
    const confirmLoading = ref(false);
    // 编辑模式
    const editMode = ref(false);
    // 模态框标题
    const modalTitle = computed(() => (editMode.value ? '编辑物料清单' : '添加物料清单'));

    // 显示模态框
    function showModal(record) {
        visible.value = true;
        // 重置表单
        resetForm();
        // 判断是否为编辑模式
        if (record && record.id) {
            editMode.value = true;
            // 填充表单数据
            for (const key in form) {
                if (Object.prototype.hasOwnProperty.call(form, key) && record[key] !== undefined) {
                    form[key] = record[key];
                }
            }
        } else {
            editMode.value = false;
        }
    }

    // 关闭模态框
    function closeModal() {
        visible.value = false;
    }

    // 模态框关闭后回调
    function onClose() {
        resetForm();
    }

    // 重置表单
    function resetForm() {
        if (formRef.value) {
            formRef.value.resetFields();
        }
        for (const key in form) {
            if (Object.prototype.hasOwnProperty.call(form, key)) {
                form[key] = key === 'id' ? undefined : '';
            }
        }
        form.unitPrice = undefined;
        editMode.value = false;
    }

    // 处理价格变化
    function handlePriceChange(value) {
        if (value !== null && value !== undefined) {
            form.unitPrice = Number(value.toFixed(2));
        }
    }

    // 提交表单
    function submitForm() {
        formRef.value.validate().then(async () => {
            try {
                confirmLoading.value = true;
                SmartLoading.show();
                let res;
                if (editMode.value) {
                    // 编辑模式
                    res = await materialListApi.updateMaterialList(form);
                } else {
                    // 新增模式
                    res = await materialListApi.addMaterialList(form);
                }
                if (res.ok) {
                    message.success(editMode.value ? '更新成功' : '添加成功');
                    closeModal();
                    emit('refresh');
                } else {
                    message.error(res.msg || (editMode.value ? '更新失败' : '添加失败'));
                }
            } catch (e) {
                smartSentry.captureError(e);
                message.error('系统异常，请稍后重试');
            } finally {
                confirmLoading.value = false;
                SmartLoading.hide();
            }
        });
    }

    // 导出方法
    defineExpose({
        showModal
    });
</script> 