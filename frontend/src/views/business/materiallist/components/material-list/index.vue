<!--
  *  物料清单列表
-->
<template>
    <a-card class="material-list-container">
        <div class="header">
            <a-typography-title :level="5">物料清单列表</a-typography-title>
            <div class="query-operate">
                <a-input-search v-model:value.trim="params.keyword" placeholder="物料名称/品牌/型号" @search="queryMaterialListByKeyword(true)">
                    <template #enterButton>
                        <a-button type="primary">
                            <template #icon>
                                <SearchOutlined />
                            </template>
                            查询
                        </a-button>
                    </template>
                </a-input-search>
                <a-button @click="reset" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </div>
        </div>
        <div class="btn-group">
            <a-button class="btn" type="primary" @click="showDrawer()" v-privilege="'business:materialList:add'">添加物料</a-button>
            <a-button class="btn" @click="batchDelete" v-privilege="'business:materialList:delete'">批量删除</a-button>
            <span class="smart-table-column-operate">
                <TableOperator v-model="columns" :tableId="TABLE_ID_CONST.BUSINESS.MATERIAL_LIST" :refresh="queryMaterialList" />
            </span>
        </div>

        <a-table
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            size="small"
            :columns="columns"
            :data-source="tableData"
            :pagination="false"
            :loading="tableLoading"
            :scroll="{ x: 1300 }"
            row-key="id"
            bordered
        >
            <template #bodyCell="{ text, record, index, column }">
                <template v-if="column.dataIndex === 'detailConfig'">
                    <a-tooltip :title="text">
                        <div class="detail-config-content">{{ text }}</div>
                    </a-tooltip>
                </template>
                <template v-else-if="column.dataIndex === 'unitPrice'">
                    <span>{{ formatPrice(text) }}</span>
                </template>
                <template v-else-if="column.dataIndex === 'operate'">
                    <div class="smart-table-operate">
                        <a-button v-privilege="'business:materialList:update'" type="link" size="small" @click="showDrawer(record)">编辑</a-button>
                        <a-button v-privilege="'business:materialList:delete'" type="link" size="small" @click="deleteItem(record.id)">删除</a-button>
                    </div>
                </template>
            </template>
        </a-table>
        <div class="smart-query-table-page">
            <a-pagination
                showSizeChanger
                showQuickJumper
                show-less-items
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="params.pageSize"
                v-model:current="params.pageNum"
                v-model:pageSize="params.pageSize"
                :total="total"
                @change="queryMaterialList"
                @showSizeChange="queryMaterialList"
                :show-total="showTableTotal"
            />
        </div>
        <MaterialListFormModal ref="materialListFormModal" @refresh="queryMaterialList" />
    </a-card>
</template>
<script setup>
    import { ExclamationCircleOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
    import { message, Modal } from 'ant-design-vue';
    import { computed, createVNode, reactive, ref, watch } from 'vue';
    import { materialListApi } from '/@/api/business/material-list-api';
    import { PAGE_SIZE } from '/@/constants/common-const';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import MaterialListFormModal from '../material-list-form-modal/index.vue';
    import { PAGE_SIZE_OPTIONS, showTableTotal } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';

    //字段
    const columns = ref([
        {
            title: '物料编码',
            dataIndex: 'materialCode',
            width: 120
        },
        {
            title: '物料名称',
            dataIndex: 'materialName',
            width: 150
        },
        {
            title: '供应商名称',
            dataIndex: 'supplierName',
            width: 150
        },
        {
            title: '品牌',
            dataIndex: 'brand',
            width: 120
        },
        {
            title: '规格型号',
            dataIndex: 'model',
            width: 120
        },
        {
            title: '计量单位',
            dataIndex: 'unit',
            width: 100
        },
        {
            title: '详细配置',
            dataIndex: 'detailConfig',
            width: 300
        },
        {
            title: '单价(元)',
            dataIndex: 'unitPrice',
            width: 100
        },
        {
            title: '供应商联系人',
            dataIndex: 'supplierContact',
            width: 120
        },
        {
            title: '联系电话',
            dataIndex: 'supplierPhone',
            width: 120
        },
        {
            title: '创建时间',
            dataIndex: 'createdAt',
            width: 180
        },
        {
            title: '操作',
            dataIndex: 'operate',
            width: 120,
            fixed: 'right'
        }
    ]);
    const tableData = ref([]);
    const total = ref(0);
    const tableLoading = ref(false);
    const selectedRowKeys = ref([]);
    const params = reactive({
        pageNum: 1,
        pageSize: Number(PAGE_SIZE),
        keyword: undefined
    });

    const materialListFormModal = ref();

    // 选择行
    function onSelectChange(keys) {
        selectedRowKeys.value = keys;
    }

    // 格式化价格
    function formatPrice(price) {
        if (!price) return '0.00';
        return Number(price).toFixed(2);
    }

    // 查询
    async function queryMaterialList() {
        try {
            tableLoading.value = true;
            // 确保分页参数为数字类型，且 keyword 为空字符串而不是 undefined
            const queryParams = {
                pageNum: Number(params.pageNum) || 1,
                pageSize: Number(params.pageSize) || Number(PAGE_SIZE),
                keyword: params.keyword || ''
            };
            console.log('Querying material list with params:', queryParams);
            const res = await materialListApi.queryMaterialList(queryParams);
            console.log('Query response:', res);
            
            // 使用 ok === true 判断成功，与 axios 拦截器保持一致
            if (res.ok) {
                // 正确处理数据结构：res.data 直接包含 list 和 total
                if (Array.isArray(res.data.list)) {
                    tableData.value = res.data.list;
                    total.value = res.data.total || 0;
                    console.log('Query success, data size:', res.data.list.length);
                } else {
                    console.error('Invalid response data format:', res.data);
                    message.error('数据格式错误');
                    tableData.value = [];
                    total.value = 0;
                }
            } else {
                console.error('Query failed:', res);
                tableData.value = [];
                total.value = 0;
                message.error(res.msg || '查询失败');
            }
        } catch (e) {
            console.error('Query error:', e);
            smartSentry.captureError(e);
            message.error('系统异常，请稍后重试');
            tableData.value = [];
            total.value = 0;
        } finally {
            tableLoading.value = false;
        }
    }

    // 关键词搜索
    function queryMaterialListByKeyword(research) {
        if (research) {
            params.pageNum = 1;
        }
        queryMaterialList();
    }

    // 重置
    function reset() {
        params.keyword = undefined;
        params.pageNum = 1;
        params.pageSize = Number(PAGE_SIZE);
        queryMaterialListByKeyword(true);
    }

    // 批量删除
    function batchDelete() {
        if (selectedRowKeys.value.length === 0) {
            message.warning('请选择要删除的数据');
            return;
        }
        Modal.confirm({
            title: '确认删除',
            icon: createVNode(ExclamationCircleOutlined),
            content: '确定要删除选中的物料清单吗？',
            onOk() {
                return new Promise((resolve, reject) => {
                    SmartLoading.show();
                    materialListApi
                        .batchDeleteMaterialList(selectedRowKeys.value)
                        .then((res) => {
                            if (res.ok) {
                                message.success('删除成功');
                                selectedRowKeys.value = [];
                                queryMaterialList();
                            } else {
                                message.error(res.msg || '删除失败');
                            }
                            resolve();
                        })
                        .catch((e) => {
                            reject(e);
                            smartSentry.captureError(e);
                            message.error('系统异常，请稍后重试');
                        })
                        .finally(() => {
                            SmartLoading.hide();
                        });
                });
            }
        });
    }

    // 删除单个
    function deleteItem(id) {
        Modal.confirm({
            title: '确认删除',
            icon: createVNode(ExclamationCircleOutlined),
            content: '确定要删除该物料清单吗？',
            onOk() {
                return new Promise((resolve, reject) => {
                    SmartLoading.show();
                    materialListApi
                        .deleteMaterialList(id)
                        .then((res) => {
                            if (res.ok) {
                                message.success('删除成功');
                                queryMaterialList();
                            } else {
                                message.error(res.msg || '删除失败');
                            }
                            resolve();
                        })
                        .catch((e) => {
                            reject(e);
                            smartSentry.captureError(e);
                            message.error('系统异常，请稍后重试');
                        })
                        .finally(() => {
                            SmartLoading.hide();
                        });
                });
            }
        });
    }

    // 显示抽屉
    function showDrawer(record) {
        materialListFormModal.value.showModal(record);
    }

    // 首次加载
    queryMaterialList();
</script>
<style lang="less" scoped>
    .material-list-container {
        height: 100%;
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            .query-operate {
                display: flex;
                justify-content: flex-end;
                :deep(.ant-input-search) {
                    width: 300px;
                }
            }
        }
        .btn-group {
            margin-bottom: 16px;
            display: flex;
            .btn {
                margin-right: 8px;
            }
            .smart-table-column-operate {
                margin-left: auto;
            }
        }
        .detail-config-content {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
</style> 