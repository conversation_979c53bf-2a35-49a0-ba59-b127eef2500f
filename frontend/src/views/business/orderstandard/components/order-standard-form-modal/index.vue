<!--
  * 设备下单标准表单模态框
-->
<template>
    <a-modal
        :title="modalTitle"
        :open="visible"
        :maskClosable="false"
        :confirmLoading="confirmLoading"
        @cancel="closeModal"
        width="600px"
        :afterClose="onClose"
    >
        <a-form
            ref="formRef"
            :model="form"
            :rules="rules"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 19 }"
            @finish="submitForm"
        >
            <a-form-item name="deviceName" label="设备名称">
                <a-input v-model:value="form.deviceName" placeholder="请输入设备名称" :maxLength="255" />
            </a-form-item>
            <a-form-item name="orderStandard" label="下单标准">
                <a-textarea v-model:value="form.orderStandard" placeholder="请输入下单标准" :auto-size="{ minRows: 5, maxRows: 10 }" :maxLength="2000" />
            </a-form-item>
        </a-form>
        <template #footer>
            <a-button type="primary" @click="submitForm" :loading="confirmLoading">确定</a-button>
            <a-button @click="closeModal">取消</a-button>
        </template>
    </a-modal>
</template>
<script setup>
    import { message } from 'ant-design-vue';
    import { computed, reactive, ref } from 'vue';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { orderStandardApi } from '/@/api/business/orderstandard';
    import { smartSentry } from '/@/lib/smart-sentry';

    // ----------------------- emits ---------------------
    const emit = defineEmits(['refresh']);

    // ----------------------- 表单数据 ---------------------
    // 表单ref
    const formRef = ref();
    // 表单数据
    const form = reactive({
        serialNumber: undefined,
        deviceName: '',
        orderStandard: ''
    });
    // 表单校验规则
    const rules = reactive({
        deviceName: [
            { required: true, message: '请输入设备名称', trigger: 'blur' },
            { max: 100, message: '设备名称不能超过100个字符', trigger: 'blur' }
        ],
        orderStandard: [
            { required: true, message: '请输入下单标准', trigger: 'blur' },
            { max: 500, message: '下单标准不能超过500个字符', trigger: 'blur' }
        ]
    });

    // ----------------------- 模态框数据 ---------------------
    // 模态框是否可见
    const visible = ref(false);
    // 表单确认加载状态
    const confirmLoading = ref(false);
    // 编辑模式
    const editMode = ref(false);
    // 模态框标题
    const modalTitle = computed(() => (editMode.value ? '编辑设备下单标准' : '添加设备下单标准'));

    // 显示模态框
    function showModal(record) {
        visible.value = true;
        // 重置表单
        resetForm();
        // 判断是否为编辑模式
        if (record && record.serialNumber) {
            editMode.value = true;
            // 填充表单数据
            for (const key in form) {
                if (Object.prototype.hasOwnProperty.call(form, key) && record[key] !== undefined) {
                    form[key] = record[key];
                }
            }
        } else {
            editMode.value = false;
        }
    }

    // 关闭模态框
    function closeModal() {
        visible.value = false;
    }

    // 模态框关闭后回调
    function onClose() {
        resetForm();
    }

    // 重置表单
    function resetForm() {
        if (formRef.value) {
            formRef.value.resetFields();
        }
        for (const key in form) {
            if (Object.prototype.hasOwnProperty.call(form, key)) {
                form[key] = key === 'serialNumber' ? undefined : '';
            }
        }
    }

    // 提交表单
    function submitForm() {
        formRef.value.validate().then(async () => {
            try {
                confirmLoading.value = true;
                SmartLoading.show();
                let res;
                if (editMode.value) {
                    // 编辑模式
                    res = await orderStandardApi.updateOrderStandard(form);
                } else {
                    // 新增模式
                    res = await orderStandardApi.addOrderStandard(form);
                }
                if (res.ok) {
                    message.success(editMode.value ? '更新成功' : '添加成功');
                    closeModal();
                    emit('refresh');
                } else {
                    message.error(res.msg || (editMode.value ? '更新失败' : '添加失败'));
                }
            } catch (e) {
                smartSentry.captureError(e);
                message.error('系统异常，请稍后重试');
            } finally {
                confirmLoading.value = false;
                SmartLoading.hide();
            }
        });
    }

    // 导出方法
    defineExpose({
        showModal
    });
</script> 