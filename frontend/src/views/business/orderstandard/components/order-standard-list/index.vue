<!--
  *  设备下单标准列表
-->
<template>
    <a-card class="order-standard-container">
        <div class="header">
            <a-typography-title :level="5">设备下单标准列表</a-typography-title>
            <div class="query-operate">
                <a-input-search v-model:value.trim="params.keyword" placeholder="设备名称" @search="queryOrderStandardByKeyword(true)">
                    <template #enterButton>
                        <a-button type="primary">
                            <template #icon>
                                <SearchOutlined />
                            </template>
                            查询
                        </a-button>
                    </template>
                </a-input-search>
                <a-button @click="reset" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </div>
        </div>
        <div class="btn-group">
            <a-button class="btn" type="primary" @click="showDrawer()" v-privilege="'business:orderStandard:add'">添加设备下单标准</a-button>
            <a-button class="btn" @click="batchDelete" v-privilege="'business:orderStandard:delete'">批量删除</a-button>
            <div class="smart-table-column-operate">
                <TableOperator v-model="columns" :tableId="TABLE_ID_CONST.BUSINESS.ORDER_STANDARD" :refresh="queryOrderStandard" />
            </div>
        </div>

        <a-table
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            size="small"
            :columns="columns"
            :data-source="tableData"
            :pagination="false"
            :loading="tableLoading"
            :scroll="{ x: 1200 }"
            row-key="serialNumber"
            bordered
        >
            <template #bodyCell="{ text, record, index, column }">
                <template v-if="column.dataIndex === 'orderStandard'">
                    <a-tooltip :title="text">
                        <div class="order-standard-content">{{ text }}</div>
                    </a-tooltip>
                </template>
                <template v-else-if="column.dataIndex === 'operate'">
                    <div class="smart-table-operate">
                        <a-button v-privilege="'business:orderStandard:update'" type="link" size="small" @click="showDrawer(record)">编辑</a-button>
                        <a-button v-privilege="'business:orderStandard:delete'" type="link" size="small" @click="deleteItem(record.serialNumber)">删除</a-button>
                    </div>
                </template>
            </template>
        </a-table>
        <div class="smart-query-table-page">
            <a-pagination
                showSizeChanger
                showQuickJumper
                show-less-items
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="params.pageSize"
                v-model:current="params.pageNum"
                v-model:pageSize="params.pageSize"
                :total="total"
                @change="queryOrderStandard"
                @showSizeChange="queryOrderStandard"
                :show-total="showTableTotal"
            />
        </div>
        <OrderStandardFormModal ref="orderStandardFormModal" @refresh="queryOrderStandard" />
    </a-card>
</template>
<script setup>
    import { ExclamationCircleOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
    import { message, Modal } from 'ant-design-vue';
    import { computed, createVNode, reactive, ref, watch } from 'vue';
    import { orderStandardApi } from '/@/api/business/orderstandard';
    import { PAGE_SIZE } from '/@/constants/common-const';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import OrderStandardFormModal from '../order-standard-form-modal/index.vue';
    import { PAGE_SIZE_OPTIONS, showTableTotal } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';

    //字段
    const columns = ref([
        {
            title: '序号',
            dataIndex: 'serialNumber',
            width: 80
        },
        {
            title: '设备名称',
            dataIndex: 'deviceName',
            width: 150
        },
        {
            title: '下单标准',
            dataIndex: 'orderStandard',
            width: 400
        },
        {
            title: '创建时间',
            dataIndex: 'createdAt',
            width: 180
        },
        {
            title: '操作',
            dataIndex: 'operate',
            width: 120,
            fixed: 'right'
        }
    ]);
    const tableData = ref([]);
    const total = ref(0);
    const tableLoading = ref(false);
    const selectedRowKeys = ref([]);
    const params = reactive({
        pageNum: 1,
        pageSize: Number(PAGE_SIZE),
        keyword: undefined
    });

    const orderStandardFormModal = ref();

    // 选择行
    function onSelectChange(keys) {
        selectedRowKeys.value = keys;
    }

    // 查询
    async function queryOrderStandard() {
        try {
            tableLoading.value = true;
            // 确保分页参数为数字类型，且 keyword 为空字符串而不是 undefined
            const queryParams = {
                pageNum: Number(params.pageNum) || 1,
                pageSize: Number(params.pageSize) || Number(PAGE_SIZE),
                keyword: params.keyword || ''
            };
            const res = await orderStandardApi.queryOrderStandard(queryParams);
            
            if (res.ok) {
                if (Array.isArray(res.data.list)) {
                    tableData.value = res.data.list;
                    total.value = res.data.total || 0;
                } else {
                    message.error('数据格式错误');
                    tableData.value = [];
                    total.value = 0;
                }
            } else {
                tableData.value = [];
                total.value = 0;
                message.error(res.msg || '查询失败');
            }
        } catch (e) {
            smartSentry.captureError(e);
            message.error('系统异常，请稍后重试');
            tableData.value = [];
            total.value = 0;
        } finally {
            tableLoading.value = false;
        }
    }

    // 关键词搜索
    function queryOrderStandardByKeyword(research) {
        if (research) {
            params.pageNum = 1;
        }
        queryOrderStandard();
    }

    // 重置
    function reset() {
        params.keyword = undefined;
        params.pageNum = 1;
        params.pageSize = Number(PAGE_SIZE);
        queryOrderStandardByKeyword(true);
    }

    // 批量删除
    function batchDelete() {
        if (selectedRowKeys.value.length === 0) {
            message.warning('请选择要删除的数据');
            return;
        }
        Modal.confirm({
            title: '确认删除',
            icon: createVNode(ExclamationCircleOutlined),
            content: '确定要删除选中的设备下单标准吗？',
            onOk() {
                return new Promise((resolve, reject) => {
                    SmartLoading.show();
                    orderStandardApi
                        .batchDeleteOrderStandard(selectedRowKeys.value)
                        .then((res) => {
                            if (res.ok) {
                                message.success('删除成功');
                                selectedRowKeys.value = [];
                                queryOrderStandard();
                            } else {
                                message.error(res.msg || '删除失败');
                            }
                            resolve();
                        })
                        .catch((e) => {
                            reject(e);
                            smartSentry.captureError(e);
                            message.error('系统异常，请稍后重试');
                        })
                        .finally(() => {
                            SmartLoading.hide();
                        });
                });
            }
        });
    }

    // 删除单项
    function deleteItem(id) {
        Modal.confirm({
            title: '确认删除',
            icon: createVNode(ExclamationCircleOutlined),
            content: '确定要删除该设备下单标准吗？',
            onOk() {
                return new Promise((resolve, reject) => {
                    SmartLoading.show();
                    orderStandardApi
                        .deleteOrderStandard(id)
                        .then((res) => {
                            if (res.ok) {
                                message.success('删除成功');
                                queryOrderStandard();
                            } else {
                                message.error(res.msg || '删除失败');
                            }
                            resolve();
                        })
                        .catch((e) => {
                            reject(e);
                            smartSentry.captureError(e);
                            message.error('系统异常，请稍后重试');
                        })
                        .finally(() => {
                            SmartLoading.hide();
                        });
                });
            }
        });
    }

    // 显示表单抽屉
    function showDrawer(record) {
        orderStandardFormModal.value.showModal(record);
    }

    // 首次加载
    queryOrderStandard();
</script>
<style scoped lang="less">
    .order-standard-container {
        height: 100%;
        display: flex;
        flex-direction: column;

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;

            .query-operate {
                display: flex;
                align-items: center;
            }
        }

        .btn-group {
            display: flex;
            margin-bottom: 16px;

            .btn {
                margin-right: 8px;
            }
        }

        .order-standard-content {
            max-width: 400px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        :deep(.ant-card-body) {
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 16px;
            overflow: hidden;
        }

        :deep(.ant-pagination) {
            margin-top: 16px;
            text-align: right;
        }

        :deep(.ant-table-wrapper) {
            flex: 1;
            overflow: auto;
        }
    }
</style> 