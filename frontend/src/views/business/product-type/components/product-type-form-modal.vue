<!--
  * 产品类型表单 弹窗
-->
<template>
    <a-modal v-model:open="visible" :title="formState.id ? '编辑产品类型' : '添加产品类型'" @ok="handleOk" destroyOnClose>
        <a-form ref="formRef" :model="formState" :rules="rules" layout="vertical">
            <a-form-item label="产品名称" name="name">
                <a-input v-model:value.trim="formState.name" placeholder="请输入产品名称" />
            </a-form-item>
            <a-form-item label="类型" name="type">
                <a-select v-model:value="formState.type" placeholder="请选择类型">
                    <a-select-option value="产品线">产品线</a-select-option>
                    <a-select-option value="产品类型">产品类型</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="软件著作权名称" name="softwareCopyrightName">
                <a-input v-model:value.trim="formState.softwareCopyrightName" placeholder="请输入软件著作权名称" />
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<script setup>
    import message from 'ant-design-vue/lib/message';
    import { nextTick, reactive, ref } from 'vue';
    import { productTypeApi } from '/@/api/business/product-type-api';
    import { smartSentry } from '/@/lib/smart-sentry';
    import { SmartLoading } from '/@/components/framework/smart-loading';

    // ----------------------- 对外暴漏 ---------------------
    defineExpose({
        showModal
    });

    // ----------------------- modal 的显示与隐藏 ---------------------
    const emits = defineEmits(['refresh']);

    const visible = ref(false);
    function showModal(data) {
        visible.value = true;
        updateFormData(data);
        nextTick(() => {
            // 解决弹窗错误信息显示,没有可忽略
            const domArr = document.getElementsByClassName('ant-modal');
            if (domArr && domArr.length > 0) {
                Array.from(domArr).forEach((item) => {
                    if (item.childNodes && item.childNodes.length > 0) {
                        Array.from(item.childNodes).forEach((child) => {
                            if (child.setAttribute) {
                                child.setAttribute('aria-hidden', 'false');
                            }
                        });
                    }
                });
            }
        });
    }
    function closeModal() {
        visible.value = false;
        resetFormData();
    }

    // ----------------------- form 表单操作 ---------------------
    const formRef = ref();
    const defaultProductTypeForm = {
        id: undefined,
        name: undefined,
        type: undefined,
        parentId: undefined,
        softwareCopyrightName: undefined
    };

    let formState = reactive({
        ...defaultProductTypeForm
    });

    // 表单校验规则
    const rules = {
        name: [
            { required: true, message: '产品名称不能为空' },
            { max: 100, message: '产品名称不能大于100个字符', trigger: 'blur' }
        ],
        type: [{ required: true, message: '类型不能为空' }]
    };

    // 更新表单数据
    function updateFormData(data) {
        Object.assign(formState, defaultProductTypeForm);
        if (data) {
            Object.assign(formState, data);
        }
        visible.value = true;
    }

    // 重置表单数据
    function resetFormData() {
        Object.assign(formState, defaultProductTypeForm);
    }

    async function handleOk() {
        try {
            await formRef.value.validate();
            if (formState.id) {
                updateProductType();
            } else {
                addProductType();
            }
        } catch (error) {
            message.error('参数验证错误，请仔细填写表单数据!');
        }
    }

    // ----------------------- form 表单  ajax 操作 ---------------------
    //添加产品类型ajax请求
    async function addProductType() {
        SmartLoading.show();
        try {
            await productTypeApi.addProductType(formState);
            emits('refresh');
            closeModal();
        } catch (error) {
            smartSentry.captureError(error);
        } finally {
            SmartLoading.hide();
        }
    }

    //更新产品类型ajax请求
    async function updateProductType() {
        SmartLoading.show();
        try {
            await productTypeApi.updateProductType(formState);
            emits('refresh');
            closeModal();
        } catch (error) {
            smartSentry.captureError(error);
        } finally {
            SmartLoading.hide();
        }
    }
</script> 