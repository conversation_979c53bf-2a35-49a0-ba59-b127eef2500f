<!--
  * 产品类型树形选择
-->
<template>
    <a-card class="tree-container">
        <a-row class="smart-margin-bottom10">
            <a-input v-model:value.trim="keywords" placeholder="请输入产品名称" />
        </a-row>
        <a-tree
            v-if="!_.isEmpty(productTypeTreeData)"
            v-model:selectedKeys="selectedKeys"
            v-model:checkedKeys="checkedKeys"
            class="tree"
            :treeData="productTypeTreeData"
            :fieldNames="{ title: 'name', key: 'id', value: 'id' }"
            style="width: 100%; overflow-x: auto"
            :style="[!height ? '' : { height: `${height}px`, overflowY: 'auto' }]"
            :checkable="props.checkable"
            :checkStrictly="props.checkStrictly"
            :selectable="!props.checkable"
            :defaultExpandAll="true"
            @select="treeSelectChange"
        >
            <template #title="item">
                <div>{{ item.name }}</div>
            </template>
        </a-tree>
        <div class="no-data" v-else>暂无结果</div>
    </a-card>
</template>

<script setup>
    import { onMounted, onUnmounted, ref, watch } from 'vue';
    import _ from 'lodash';
    import { productTypeApi } from '/@/api/business/product-type-api';
    import { smartSentry } from '/@/lib/smart-sentry';

    const PRODUCT_TYPE_PARENT_ID = 0;

    // ----------------------- 组件参数 ---------------------
    const props = defineProps({
        // 是否可以选中
        checkable: {
            type: Boolean,
            default: false
        },
        // 父子节点选中状态不再关联
        checkStrictly: {
            type: Boolean,
            default: false
        },
        // 树高度 超出出滚动条
        height: Number,
        // 显示菜单
        showMenu: {
            type: Boolean,
            default: false
        },
        // 默认值
        defaultValue: {
            type: [String, Number],
            default: null
        },
        // 默认值标志
        defaultValueFlag: {
            type: Boolean,
            default: true
        },
        // 宽度
        width: {
            type: String,
            default: '100%'
        }
    });

    // ----------------------- 产品类型树的展示 ---------------------
    const topProductTypeId = ref();
    // 所有产品类型列表
    const productTypeList = ref([]);
    // 产品类型树形数据
    const productTypeTreeData = ref([]);
    // 存放产品类型id和产品类型，用于查找
    const idInfoMap = ref(new Map());

    onMounted(() => {
        queryProductTypeTree();
    });

    // 刷新
    async function refresh() {
        await queryProductTypeTree();
        if (currentSelectedProductTypeId.value) {
            selectTree(currentSelectedProductTypeId.value);
        }
    }

    // 查询产品类型列表并构建 产品类型树
    async function queryProductTypeTree() {
        try {
            let res = await productTypeApi.queryAllProductType();
            let data = res.data;

            data.forEach((e) => {
                idInfoMap.value.set(e.id, e);
            });

            productTypeList.value = data;
            productTypeTreeData.value = buildProductTypeTree(data, PRODUCT_TYPE_PARENT_ID);

            // 默认显示 最顶级ID为列表中返回的第一条数据的ID
            if (!_.isEmpty(productTypeTreeData.value) && productTypeTreeData.value.length > 0) {
                topProductTypeId.value = productTypeTreeData.value[0].id;
            }
        } catch (e) {
            smartSentry.captureError(e);
        }
    }

    // 构建产品类型树
    function buildProductTypeTree(data, parentId) {
        let children = data.filter((e) => e.parentId === parentId) || [];
        if (!_.isEmpty(children)) {
            children.forEach((e) => {
                e.children = buildProductTypeTree(data, e.id);
            });
            return children;
        }
        return null;
    }

    // ----------------------- 树形选择 ---------------------
    const selectedKeys = ref([]);
    const checkedKeys = ref([]);
    const currentSelectedProductTypeId = ref();

    // 树形选择改变
    function treeSelectChange(selectedKeys, info) {
        if (info.selected) {
            currentSelectedProductTypeId.value = info.node.id;
            emit('update:value', info.node.id);
            emit('change', info.node.id);
        }
    }

    // 选中树形
    function selectTree(id) {
        currentSelectedProductTypeId.value = id;
        selectedKeys.value = [id];
        emit('update:value', id);
        emit('change', id);
    }

    // ----------------------- 对外暴漏 ---------------------
    defineExpose({
        refresh,
        selectTree
    });

    // ----------------------- 事件 ---------------------
    const emit = defineEmits(['update:value', 'change']);

    // ----------------------- 监听 ---------------------
    watch(
        () => props.defaultValue,
        (newVal) => {
            if (newVal && props.defaultValueFlag) {
                selectTree(newVal);
            }
        }
    );
</script>

<style scoped lang="less">
    .tree-container {
        .tree {
            margin-top: 10px;
        }
        .no-data {
            text-align: center;
            color: #999;
            padding: 20px 0;
        }
    }
</style> 