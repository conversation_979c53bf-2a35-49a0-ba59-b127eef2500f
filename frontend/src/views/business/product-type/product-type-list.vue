<!--
  * 产品类型列表
-->
<template>
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="产品名称" class="smart-query-form-item">
                <a-input style="width: 300px" v-model:value="keywords" placeholder="请输入产品名称" />
            </a-form-item>

            <a-form-item class="smart-query-form-item smart-margin-left10">
                <a-button-group>
                    <a-button v-privilege="'business:productType:query'" type="primary" @click="onSearch">
                        <template #icon>
                            <SearchOutlined />
                        </template>
                        查询
                    </a-button>
                    <a-button v-privilege="'business:productType:query'" @click="resetQuery">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        重置
                    </a-button>
                </a-button-group>
                <a-button v-privilege="'business:productType:add'" type="primary" @click="addProductType" class="smart-margin-left20">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新建
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>

    <a-card size="small" :bordered="true">
        <a-table
            size="small"
            bordered
            :loading="tableLoading"
            rowKey="id"
            :columns="columns"
            :data-source="productTypeTreeData"
            :defaultExpandAllRows="false"
            :defaultExpandedRowKeys="defaultExpandedRowList"
            :pagination="false"
        >
            <template #bodyCell="{ record, column }">
                <template v-if="column.dataIndex === 'action'">
                    <div class="smart-table-operate">
                        <a-button @click="addProductType(record)" v-privilege="'business:productType:add'" type="link">添加下级</a-button>
                        <a-button @click="updateProductType(record)" v-privilege="'business:productType:update'" type="link">编辑</a-button>
                        <a-button
                            danger
                            v-if="record.id !== topProductTypeId"
                            v-privilege="'business:productType:delete'"
                            @click="deleteProductType(record.id)"
                            type="link"
                            >删除</a-button
                        >
                    </div>
                </template>
            </template>
        </a-table>
        <!-- 添加编辑产品类型弹窗 -->
        <ProductTypeFormModal ref="productTypeFormModal" @refresh="queryProductTypeTree" />
    </a-card>
</template>

<script setup>
    import { onMounted, reactive, ref, watch, createVNode } from 'vue';
    import { productTypeApi } from '/@/api/business/product-type-api';
    import { Modal, message } from 'ant-design-vue';
    import { ExclamationCircleOutlined, SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons-vue';
    import _ from 'lodash';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import ProductTypeFormModal from './components/product-type-form-modal.vue';
    import { smartSentry } from '/@/lib/smart-sentry';

    const PRODUCT_TYPE_PARENT_ID = 0;

    // -----------------------  筛选 ---------------------
    const keywords = ref('');

    // ----------------------- 产品类型树的展示 ---------------------
    const tableLoading = ref(false);

    const topProductTypeId = ref();
    // 所有产品类型列表
    const productTypeList = ref([]);
    // 产品类型树形数据
    const productTypeTreeData = ref([]);
    // 存放产品类型id和产品类型，用于查找
    const idInfoMap = ref(new Map());
    // 默认展开的行
    const defaultExpandedRowList = reactive([]);

    const columns = ref([
        {
            title: '产品名称',
            dataIndex: 'name',
            key: 'name'
        },
        {
            title: '类型',
            dataIndex: 'type',
            key: 'type',
            width: 120
        },
        {
            title: '软件著作权名称',
            dataIndex: 'softwareCopyrightName',
            key: 'softwareCopyrightName',
            width: 200
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 150
        },
        {
            title: '更新时间',
            dataIndex: 'updateTime',
            width: 150
        },
        {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            width: 200
        }
    ]);

    onMounted(() => {
        queryProductTypeTree();
    });

    // 查询产品类型列表并构建 产品类型树
    async function queryProductTypeTree() {
        try {
            tableLoading.value = true;
            let res = await productTypeApi.queryAllProductType();
            let data = res.data;

            data.forEach((e) => {
                idInfoMap.value.set(e.id, e);
            });

            productTypeList.value = data;
            productTypeTreeData.value = buildProductTypeTree(data, PRODUCT_TYPE_PARENT_ID);

            // 默认显示 最顶级ID为列表中返回的第一条数据的ID
            if (!_.isEmpty(productTypeTreeData.value) && productTypeTreeData.value.length > 0) {
                topProductTypeId.value = productTypeTreeData.value[0].id;
            }

            defaultExpandedRowList.value = [];
            defaultExpandedRowList.push(topProductTypeId.value);
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            tableLoading.value = false;
        }
    }

    // 构建产品类型树
    function buildProductTypeTree(data, parentId) {
        let children = data.filter((e) => e.parentId === parentId) || [];
        if (!_.isEmpty(children)) {
            children.forEach((e) => {
                e.children = buildProductTypeTree(data, e.id);
            });
            return children;
        }
        return null;
    }

    // 重置
    function resetQuery() {
        keywords.value = '';
        onSearch();
    }

    // 搜索
    function onSearch() {
        if (!keywords.value) {
            productTypeTreeData.value = buildProductTypeTree(productTypeList.value, PRODUCT_TYPE_PARENT_ID);
            return;
        }
        let originData = productTypeList.value.concat();
        if (!originData) {
            return;
        }
        // 筛选出名称符合的产品类型
        let filterProductType = originData.filter((e) => e.name.indexOf(keywords.value) > -1);
        let filterProductTypeList = [];
        // 循环筛选出的产品类型 构建产品类型树
        filterProductType.forEach((e) => {
            recursionFilterProductType(filterProductTypeList, e.id, false);
        });
        productTypeTreeData.value = buildProductTypeTree(filterProductTypeList, PRODUCT_TYPE_PARENT_ID);
    }

    // 根据ID递归筛选产品类型
    function recursionFilterProductType(resList, id, unshift) {
        let info = idInfoMap.value.get(id);
        if (!info || resList.some((e) => e.id === id)) {
            return;
        }
        if (unshift) {
            resList.unshift(info);
        } else {
            resList.push(info);
        }
        if (info.parentId && info.parentId !== 0) {
            recursionFilterProductType(resList, info.parentId, unshift);
        }
    }

    // ----------------------- 表单操作：添加产品类型/修改产品类型/删除产品类型 ---------------------
    const productTypeFormModal = ref();
    // 添加
    function addProductType(e) {
        let data = {
            id: 0,
            name: '',
            type: '',
            parentId: e.id || null
        };
        productTypeFormModal.value.showModal(data);
    }
    // 编辑
    function updateProductType(e) {
        productTypeFormModal.value.showModal(e);
    }

    // 删除
    function deleteProductType(id) {
        Modal.confirm({
            title: '确定要删除该产品类型吗？',
            icon: createVNode(ExclamationCircleOutlined),
            content: '删除后不可恢复',
            onOk: async () => {
                SmartLoading.show();
                try {
                    const res = await productTypeApi.deleteProductType(id);
                    if (res && res.code === 0) {
                        message.success('删除成功');
                        queryProductTypeTree();
                    } else {
                        // 特殊处理数据不存在的情况
                        if (res && res.code === 30002) {
                            message.warning('该数据已被删除，正在刷新列表');
                            queryProductTypeTree();
                        } else {
                            message.error((res && res.msg) || '删除失败');
                        }
                    }
                } catch (e) {
                    smartSentry.captureError(e);
                    message.error('删除操作失败，请稍后重试');
                } finally {
                    SmartLoading.hide();
                }
            }
        });
    }
</script>

<style scoped lang="less">
    .smart-query-form {
        margin-bottom: 10px;
        padding: 24px 24px 0;
        background: #fff;
    }
    .smart-query-form-row {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
    }
    .smart-query-form-item {
        margin-bottom: 24px;
        margin-right: 0;
        display: flex;
        align-items: center;
    }
    .smart-margin-left10 {
        margin-left: 10px;
    }
    .smart-margin-left20 {
        margin-left: 20px;
    }
    .smart-table-operate {
        display: flex;
        align-items: center;
        gap: 8px;
    }
</style> 