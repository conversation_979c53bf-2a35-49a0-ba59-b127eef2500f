<!--
  * 销售信息表单模态框
-->
<template>
    <a-modal
        :title="modalTitle"
        :open="visible"
        :maskClosable="false"
        :confirmLoading="confirmLoading"
        @cancel="closeModal"
        width="600px"
        :afterClose="onClose"
    >
        <a-form
            ref="formRef"
            :model="form"
            :rules="rules"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 19 }"
            @finish="submitForm"
        >
            <a-form-item name="salesName" label="销售姓名">
                <a-input v-model:value="form.salesName" placeholder="请输入销售姓名" :maxLength="50" />
            </a-form-item>
            <a-form-item name="mobilePhone" label="手机号码">
                <a-input v-model:value="form.mobilePhone" placeholder="请输入手机号码" :maxLength="20" />
            </a-form-item>
            <a-form-item name="email" label="邮箱地址">
                <a-input v-model:value="form.email" placeholder="请输入邮箱地址" :maxLength="100" />
            </a-form-item>
            <a-form-item name="position" label="职务">
                <a-input v-model:value="form.position" placeholder="请输入职务" :maxLength="50" />
            </a-form-item>
            <a-form-item name="region" label="负责区域">
                <a-input v-model:value="form.region" placeholder="请输入负责区域" :maxLength="100" />
            </a-form-item>
            <a-form-item name="employmentStatus" label="在职情况">
                <a-select v-model:value="form.employmentStatus" placeholder="请选择在职情况">
                    <a-select-option value="在职">在职</a-select-option>
                    <a-select-option value="离职">离职</a-select-option>
                    <a-select-option value="休假">休假</a-select-option>
                    <a-select-option value="试用期">试用期</a-select-option>
                </a-select>
            </a-form-item>
        </a-form>
        <template #footer>
            <a-button type="primary" @click="submitForm" :loading="confirmLoading">确定</a-button>
            <a-button @click="closeModal">取消</a-button>
        </template>
    </a-modal>
</template>

<script setup>
    import { ref, reactive, computed } from 'vue';
    import { message } from 'ant-design-vue';
    import { salesInfoApi } from '/@/api/business/sales-info-api';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { smartSentry } from '/@/lib/smart-sentry';
    import dayjs from 'dayjs';

    const emit = defineEmits(['success']);
    const formRef = ref();
    const visible = ref(false);
    const confirmLoading = ref(false);
    const isEdit = ref(false);

    const form = reactive({
        id: undefined,
        salesName: undefined,
        mobilePhone: undefined,
        email: undefined,
        position: undefined,
        region: undefined,
        employmentStatus: '在职'
    });

    const rules = {
        salesName: [{ required: true, message: '请输入销售姓名' }],
        mobilePhone: [
            { required: true, message: '请输入手机号码' },
            { pattern: /^1[3-9]\d{9}$/, message: '手机号码格式不正确' }
        ],
        email: [{ type: 'email', message: '邮箱格式不正确' }],
        employmentStatus: [{ required: true, message: '请选择在职情况' }]
    };

    const modalTitle = computed(() => {
        return isEdit.value ? '编辑销售信息' : '添加销售信息';
    });

    // 显示模态框
    function showModal(record) {
        visible.value = true;
        isEdit.value = !!record;
        if (record) {
            Object.assign(form, {
                ...record,
                recordDate: record.recordDate ? dayjs(record.recordDate) : undefined
            });
        }
    }

    // 关闭模态框
    function closeModal() {
        visible.value = false;
    }

    // 关闭后回调
    function onClose() {
        formRef.value?.resetFields();
        Object.assign(form, {
            id: undefined,
            salesName: undefined,
            mobilePhone: undefined,
            email: undefined,
            position: undefined,
            region: undefined,
            employmentStatus: '在职',
            recordDate: undefined
        });
    }

    // 提交表单
    function submitForm() {
        formRef.value.validate().then(async () => {
            try {
                confirmLoading.value = true;
                SmartLoading.show();
                // 不传递recordDate字段，由后端自动生成
                const submitData = { ...form };
                delete submitData.recordDate;
                const api = isEdit.value ? salesInfoApi.updateSalesInfo : salesInfoApi.addSalesInfo;
                const res = await api(submitData);
                if (res.ok) {
                    message.success(isEdit.value ? '更新成功' : '添加成功');
                    visible.value = false;
                    emit('success');
                } else {
                    message.error(res.msg || (isEdit.value ? '更新失败' : '添加失败'));
                }
            } catch (e) {
                smartSentry.captureError(e);
                message.error('系统异常，请稍后重试');
            } finally {
                confirmLoading.value = false;
                SmartLoading.hide();
            }
        }).catch(error => {
            console.error('表单验证失败:', error);
        });
    }

    defineExpose({
        showModal
    });
</script> 