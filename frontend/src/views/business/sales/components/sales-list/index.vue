<!--
  * 销售信息列表
-->
<template>
    <a-card class="sales-info-container">
        <div class="header">
            <a-typography-title :level="5">销售管理</a-typography-title>
            <div class="query-operate">
                <a-input-search
                    v-model:value="params.keyword"
                    placeholder="姓名/手机号/邮箱/区域"
                    @search="querySalesInfoByKeyword(true)"
                >
                    <template #enterButton>
                        <a-button type="primary">
                            <template #icon>
                                <SearchOutlined />
                            </template>
                            查询
                        </a-button>
                    </template>
                </a-input-search>
                <a-button @click="reset" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </div>
        </div>
        <div class="btn-group">
            <a-button class="btn" type="primary" @click="showDrawer()" v-privilege="'business:sales:add'">添加销售</a-button>
            <a-button class="btn" @click="batchDelete" v-privilege="'business:sales:delete'">批量删除</a-button>
            <a-button class="btn" @click="exportSalesInfo" v-privilege="'business:sales:export'">
                <template #icon>
                    <DownloadOutlined />
                </template>
                导出列表
            </a-button>
        </div>
        <a-table
            :columns="columns.filter(col => col.visible)"
            :data-source="tableData"
            :pagination="pagination"
            @change="handleTableChange"
            :row-selection="{
                selectedRowKeys: selectedRowKeys,
                onChange: onSelectChange,
                type: 'checkbox',
                fixed: true
            }"
            :row-key="record => record.id"
            :loading="loading"
        >
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'action'">
                    <a-space>
                        <a @click="showDrawer(record)" v-privilege="'business:sales:update'">编辑</a>
                        <a-divider type="vertical" />
                        <a-popconfirm
                            title="确定要删除该销售信息吗？"
                            @confirm="deleteItem(record.id)"
                            v-privilege="'business:sales:delete'"
                        >
                            <a>删除</a>
                        </a-popconfirm>
                    </a-space>
                </template>
            </template>
        </a-table>
        <SalesFormModal ref="salesFormModal" @success="querySalesInfo" />
    </a-card>
</template>

<script setup>
    import { ref, reactive, onMounted } from 'vue';
    import { message, Modal } from 'ant-design-vue';
    import { SearchOutlined, ReloadOutlined, DownloadOutlined } from '@ant-design/icons-vue';
    import { salesInfoApi } from '/@/api/business/sales-info-api';
    import { TABLE_ID_CONST } from '/@/constants/table-const';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { smartSentry } from '/@/lib/smart-sentry';
    import SalesFormModal from '../sales-form-modal/index.vue';
    import TableOperator from '/@/components/framework/table-operator/index.vue';
    import { downloadFile } from '/@/lib/smart-util';

    const salesFormModal = ref();
    const loading = ref(false);
    const selectedRowKeys = ref([]);
    const tableData = ref([]);
    const params = reactive({
        keyword: undefined,
        pageNum: 1,
        pageSize: 10
    });

    const pagination = reactive({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条记录`
    });

    const columns = ref([
        {
            title: '销售姓名',
            dataIndex: 'salesName',
            width: 120,
            visible: true
        },
        {
            title: '手机号码',
            dataIndex: 'mobilePhone',
            width: 120,
            visible: true
        },
        {
            title: '邮箱地址',
            dataIndex: 'email',
            width: 180,
            visible: true
        },
        {
            title: '职务',
            dataIndex: 'position',
            width: 120,
            visible: true
        },
        {
            title: '负责区域',
            dataIndex: 'region',
            width: 120,
            visible: true
        },
        {
            title: '在职情况',
            dataIndex: 'employmentStatus',
            width: 100,
            visible: true
        },
        {
            title: '记录日期',
            dataIndex: 'recordDate',
            width: 120,
            visible: true
        },
        {
            title: '操作',
            dataIndex: 'action',
            width: 150,
            fixed: 'right',
            visible: true
        }
    ]);

    // 查询销售信息
    function querySalesInfo() {
        loading.value = true;
        salesInfoApi
            .querySalesInfo(params)
            .then((res) => {
                if (res.ok) {
                    tableData.value = res.data.list;
                    pagination.total = res.data.total;
                    pagination.current = params.pageNum;
                    pagination.pageSize = params.pageSize;
                    // 清空选中项
                    selectedRowKeys.value = [];
                } else {
                    message.error(res.msg || '查询失败');
                }
            })
            .catch((e) => {
                smartSentry.captureError(e);
                message.error('系统异常，请稍后重试');
            })
            .finally(() => {
                loading.value = false;
            });
    }

    // 关键词搜索
    function querySalesInfoByKeyword(research) {
        if (research) {
            params.pageNum = 1;
        }
        querySalesInfo();
    }

    // 重置
    function reset() {
        params.keyword = undefined;
        params.pageNum = 1;
        params.pageSize = 10;
        selectedRowKeys.value = []; // 重置选中项
        querySalesInfoByKeyword(true);
    }

    // 批量删除
    function batchDelete() {
        if (selectedRowKeys.value.length === 0) {
            message.warning('请选择要删除的数据');
            return;
        }
        Modal.confirm({
            title: '确认删除',
            content: `确定要删除选中的 ${selectedRowKeys.value.length} 条销售信息吗？`,
            onOk() {
                return new Promise((resolve, reject) => {
                    SmartLoading.show();
                    salesInfoApi
                        .batchDeleteSalesInfo(selectedRowKeys.value)
                        .then((res) => {
                            if (res.ok) {
                                message.success('删除成功');
                                selectedRowKeys.value = [];
                                querySalesInfo();
                            } else {
                                message.error(res.msg || '删除失败');
                            }
                            resolve();
                        })
                        .catch((e) => {
                            reject(e);
                            smartSentry.captureError(e);
                            message.error('系统异常，请稍后重试');
                        })
                        .finally(() => {
                            SmartLoading.hide();
                        });
                });
            }
        });
    }

    // 删除单项
    function deleteItem(id) {
        SmartLoading.show();
        salesInfoApi
            .deleteSalesInfo(id)
            .then((res) => {
                if (res.ok) {
                    message.success('删除成功');
                    querySalesInfo();
                } else {
                    message.error(res.msg || '删除失败');
                }
            })
            .catch((e) => {
                smartSentry.captureError(e);
                message.error('系统异常，请稍后重试');
            })
            .finally(() => {
                SmartLoading.hide();
            });
    }

    // 显示表单模态框
    function showDrawer(record) {
        salesFormModal.value.showModal(record);
    }

    // 表格选择
    function onSelectChange(keys, selectedRows) {
        selectedRowKeys.value = keys;
        console.log('selectedRowKeys:', keys);
        console.log('selectedRows:', selectedRows);
    }

    // 表格变化
    function handleTableChange(pagination) {
        params.pageNum = pagination.current;
        params.pageSize = pagination.pageSize;
        pagination.current = pagination.current;
        pagination.pageSize = pagination.pageSize;
        querySalesInfo();
    }

    // 导出销售信息
    function exportSalesInfo() {
        Modal.confirm({
            title: '导出确认',
            content: '是否导出当前筛选条件下的所有销售信息？',
            onOk() {
                return new Promise((resolve, reject) => {
                    SmartLoading.show();
                    console.log('导出请求参数:', params);
                    salesInfoApi
                        .exportSalesInfo(params)
                        .then((response) => {
                            console.log('导出响应状态:', response.status);
                            try {
                                downloadFile(response.data, '销售信息列表.xlsx');
                                message.success('导出成功');
                            } catch (err) {
                                console.error('下载文件失败:', err);
                                message.error('导出失败: ' + err.message);
                            }
                            resolve();
                        })
                        .catch((e) => {
                            console.error('导出异常:', e);
                            reject(e);
                            smartSentry.captureError(e);
                            message.error('系统异常，请稍后重试');
                        })
                        .finally(() => {
                            SmartLoading.hide();
                        });
                });
            }
        });
    }

    // 首次加载
    querySalesInfo();
</script>

<style scoped lang="less">
    .sales-info-container {
        height: 100%;
        display: flex;
        flex-direction: column;

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;

            .query-operate {
                display: flex;
                align-items: center;
            }
        }

        .btn-group {
            display: flex;
            margin-bottom: 16px;

            .btn {
                margin-right: 8px;
            }
        }
    }
</style> 