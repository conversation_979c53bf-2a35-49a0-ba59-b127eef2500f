<!--
  * 备品备件表单模态框
-->
<template>
    <a-modal
        :title="modalTitle"
        :visible="visible"
        :maskClosable="false"
        :confirmLoading="confirmLoading"
        @cancel="closeModal"
        width="600px"
        :afterClose="onClose"
    >
        <a-form
            ref="formRef"
            :model="form"
            :rules="rules"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 19 }"
            @finish="submitForm"
        >
            <a-form-item name="deviceName" label="设备名称">
                <a-input v-model:value="form.deviceName" placeholder="请输入设备名称" :maxLength="100" />
            </a-form-item>
            <a-form-item name="brand" label="投标品牌">
                <a-input v-model:value="form.brand" placeholder="请输入投标品牌" :maxLength="50" />
            </a-form-item>
            <a-form-item name="model" label="投标型号">
                <a-input v-model:value="form.model" placeholder="请输入投标型号" :maxLength="50" />
            </a-form-item>
            <a-form-item name="configuration" label="投标配置">
                <a-textarea v-model:value="form.configuration" placeholder="请输入投标配置" :auto-size="{ minRows: 3, maxRows: 6 }" :maxLength="500" />
            </a-form-item>
            <a-form-item name="unitPrice" label="单价">
                <a-input-number 
                    v-model:value="form.unitPrice" 
                    placeholder="请输入单价" 
                    :min="0" 
                    :precision="2" 
                    :step="0.01"
                    style="width: 100%" 
                    addonAfter="元"
                    @change="handlePriceChange"
                />
            </a-form-item>
        </a-form>
        <template #footer>
            <a-button type="primary" @click="submitForm" :loading="confirmLoading">确定</a-button>
            <a-button @click="closeModal">取消</a-button>
        </template>
    </a-modal>
</template>
<script setup>
    import { message } from 'ant-design-vue';
    import { computed, reactive, ref } from 'vue';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { sparePartsApi } from '/@/api/business/spare-parts-api';
    import { smartSentry } from '/@/lib/smart-sentry';

    // ----------------------- emits ---------------------
    const emit = defineEmits(['refresh']);

    // ----------------------- 表单数据 ---------------------
    // 表单ref
    const formRef = ref();
    // 表单数据
    const form = reactive({
        id: undefined,
        deviceName: '',
        brand: '',
        model: '',
        configuration: '',
        unitPrice: undefined
    });
    // 表单校验规则
    const rules = reactive({
        deviceName: [
            { required: true, message: '请输入设备名称', trigger: 'blur' },
            { max: 100, message: '设备名称不能超过100个字符', trigger: 'blur' }
        ],
        brand: [
            { max: 50, message: '投标品牌不能超过50个字符', trigger: 'blur' }
        ],
        model: [
            { max: 50, message: '投标型号不能超过50个字符', trigger: 'blur' }
        ],
        configuration: [
            { max: 500, message: '投标配置不能超过500个字符', trigger: 'blur' }
        ],
        unitPrice: [
            { required: true, message: '请输入单价', trigger: 'blur' },
            { type: 'number', min: 0, message: '单价必须大于等于0', trigger: 'blur' }
        ]
    });

    // ----------------------- 模态框数据 ---------------------
    // 模态框是否可见
    const visible = ref(false);
    // 表单确认加载状态
    const confirmLoading = ref(false);
    // 编辑模式
    const editMode = ref(false);
    // 模态框标题
    const modalTitle = computed(() => (editMode.value ? '编辑备品备件' : '添加备品备件'));

    // 显示模态框
    function showModal(record) {
        visible.value = true;
        // 重置表单
        resetForm();
        // 判断是否为编辑模式
        if (record && record.id) {
            editMode.value = true;
            // 填充表单数据
            for (const key in form) {
                if (Object.prototype.hasOwnProperty.call(form, key) && record[key] !== undefined) {
                    form[key] = record[key];
                }
            }
        } else {
            editMode.value = false;
        }
    }

    // 关闭模态框
    function closeModal() {
        visible.value = false;
    }

    // 模态框关闭后回调
    function onClose() {
        resetForm();
    }

    // 重置表单
    function resetForm() {
        if (formRef.value) {
            formRef.value.resetFields();
        }
        for (const key in form) {
            if (Object.prototype.hasOwnProperty.call(form, key)) {
                form[key] = key === 'id' ? undefined : '';
            }
        }
        form.unitPrice = undefined;
    }

    // 处理价格变化
    function handlePriceChange(value) {
        if (value !== null && value !== undefined) {
            form.unitPrice = Number(value.toFixed(2));
        }
    }

    // 提交表单
    function submitForm() {
        formRef.value.validate().then(async () => {
            try {
                confirmLoading.value = true;
                SmartLoading.show();
                let res;
                if (editMode.value) {
                    // 编辑模式
                    res = await sparePartsApi.updateSpareParts(form);
                } else {
                    // 新增模式
                    res = await sparePartsApi.addSpareParts(form);
                }
                if (res.ok) {
                    message.success(editMode.value ? '更新成功' : '添加成功');
                    closeModal();
                    emit('refresh');
                } else {
                    message.error(res.msg || (editMode.value ? '更新失败' : '添加失败'));
                }
            } catch (e) {
                smartSentry.captureError(e);
                message.error('系统异常，请稍后重试');
            } finally {
                confirmLoading.value = false;
                SmartLoading.hide();
            }
        });
    }

    // 导出方法
    defineExpose({
        showModal
    });
</script> 