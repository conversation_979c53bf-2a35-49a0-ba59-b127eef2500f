<!--
  *  备品备件列表
-->
<template>
    <a-card class="spare-parts-container">
        <div class="header">
            <a-typography-title :level="5">备品备件列表</a-typography-title>
            <div class="query-operate">
                <a-input-search v-model:value.trim="params.keyword" placeholder="设备名称/品牌/型号" @search="querySparePartsByKeyword(true)">
                    <template #enterButton>
                        <a-button type="primary">
                            <template #icon>
                                <SearchOutlined />
                            </template>
                            查询
                        </a-button>
                    </template>
                </a-input-search>
                <a-button @click="reset" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </div>
        </div>
        <div class="btn-group">
            <a-button class="btn" type="primary" @click="showDrawer()" v-privilege="'business:spareParts:add'">添加备品备件</a-button>
            <a-button class="btn" @click="batchDelete" v-privilege="'business:spareParts:delete'">批量删除</a-button>
            <span class="smart-table-column-operate">
                <TableOperator v-model="columns" :tableId="TABLE_ID_CONST.BUSINESS.SPARE_PARTS" :refresh="querySpareParts" />
            </span>
        </div>

        <a-table
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            size="small"
            :columns="columns"
            :data-source="tableData"
            :pagination="false"
            :loading="tableLoading"
            :scroll="{ x: 1200 }"
            row-key="id"
            bordered
        >
            <template #bodyCell="{ text, record, index, column }">
                <template v-if="column.dataIndex === 'configuration'">
                    <a-tooltip :title="text">
                        <div class="configuration-content">{{ text }}</div>
                    </a-tooltip>
                </template>
                <template v-else-if="column.dataIndex === 'unitPrice'">
                    <span>{{ formatPrice(text) }}</span>
                </template>
                <template v-else-if="column.dataIndex === 'operate'">
                    <div class="smart-table-operate">
                        <a-button v-privilege="'business:spareParts:update'" type="link" size="small" @click="showDrawer(record)">编辑</a-button>
                        <a-button v-privilege="'business:spareParts:delete'" type="link" size="small" @click="deleteItem(record.id)">删除</a-button>
                    </div>
                </template>
            </template>
        </a-table>
        <div class="smart-query-table-page">
            <a-pagination
                showSizeChanger
                showQuickJumper
                show-less-items
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="params.pageSize"
                v-model:current="params.pageNum"
                v-model:pageSize="params.pageSize"
                :total="total"
                @change="querySpareParts"
                @showSizeChange="querySpareParts"
                :show-total="showTableTotal"
            />
        </div>
        <SparePartsFormModal ref="sparePartsFormModal" @refresh="querySpareParts" />
    </a-card>
</template>
<script setup>
    import { ExclamationCircleOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
    import { message, Modal } from 'ant-design-vue';
    import { computed, createVNode, reactive, ref, watch } from 'vue';
    import { sparePartsApi } from '/@/api/business/spare-parts-api';
    import { PAGE_SIZE } from '/@/constants/common-const';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import SparePartsFormModal from '../spare-parts-form-modal/index.vue';
    import { PAGE_SIZE_OPTIONS, showTableTotal } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';

    //字段
    const columns = ref([
        {
            title: '设备名称',
            dataIndex: 'deviceName',
            width: 150
        },
        {
            title: '投标品牌',
            dataIndex: 'brand',
            width: 120
        },
        {
            title: '投标型号',
            dataIndex: 'model',
            width: 120
        },
        {
            title: '投标配置',
            dataIndex: 'configuration',
            width: 300
        },
        {
            title: '单价(元)',
            dataIndex: 'unitPrice',
            width: 100
        },
        {
            title: '最后更新时间',
            dataIndex: 'updatedAt',
            width: 180
        },
        {
            title: '操作',
            dataIndex: 'operate',
            width: 120,
            fixed: 'right'
        }
    ]);
    const tableData = ref([]);
    const total = ref(0);
    const tableLoading = ref(false);
    const selectedRowKeys = ref([]);
    const params = reactive({
        pageNum: 1,
        pageSize: Number(PAGE_SIZE),
        keyword: undefined
    });

    const sparePartsFormModal = ref();

    // 选择行
    function onSelectChange(keys) {
        selectedRowKeys.value = keys;
    }

    // 格式化价格
    function formatPrice(price) {
        if (!price) return '0.00';
        return Number(price).toFixed(2);
    }

    // 查询
    async function querySpareParts() {
        try {
            tableLoading.value = true;
            // 确保分页参数为数字类型，且 keyword 为空字符串而不是 undefined
            const queryParams = {
                pageNum: Number(params.pageNum) || 1,
                pageSize: Number(params.pageSize) || Number(PAGE_SIZE),
                keyword: params.keyword || ''
            };
            console.log('Querying spare parts with params:', queryParams);
            const res = await sparePartsApi.querySpareParts(queryParams);
            console.log('Query response:', res);
            
            // 使用 ok === true 判断成功，与 axios 拦截器保持一致
            if (res.ok) {
                // 正确处理数据结构：res.data 直接包含 list 和 total
                if (Array.isArray(res.data.list)) {
                    tableData.value = res.data.list;
                    total.value = res.data.total || 0;
                    console.log('Query success, data size:', res.data.list.length);
                } else {
                    console.error('Invalid response data format:', res.data);
                    message.error('数据格式错误');
                    tableData.value = [];
                    total.value = 0;
                }
            } else {
                console.error('Query failed:', res);
                tableData.value = [];
                total.value = 0;
                message.error(res.msg || '查询失败');
            }
        } catch (e) {
            console.error('Query error:', e);
            smartSentry.captureError(e);
            message.error('系统异常，请稍后重试');
            tableData.value = [];
            total.value = 0;
        } finally {
            tableLoading.value = false;
        }
    }

    // 关键词搜索
    function querySparePartsByKeyword(research) {
        if (research) {
            params.pageNum = 1;
        }
        querySpareParts();
    }

    // 重置
    function reset() {
        params.keyword = undefined;
        params.pageNum = 1;
        params.pageSize = Number(PAGE_SIZE);
        querySparePartsByKeyword(true);
    }

    // 批量删除
    function batchDelete() {
        if (selectedRowKeys.value.length === 0) {
            message.warning('请选择要删除的数据');
            return;
        }
        Modal.confirm({
            title: '确认删除',
            icon: createVNode(ExclamationCircleOutlined),
            content: '确定要删除选中的备品备件吗？',
            onOk() {
                return new Promise((resolve, reject) => {
                    SmartLoading.show();
                    sparePartsApi
                        .batchDeleteSpareParts(selectedRowKeys.value)
                        .then((res) => {
                            if (res.ok) {
                                message.success('删除成功');
                                selectedRowKeys.value = [];
                                querySpareParts();
                            } else {
                                message.error(res.msg || '删除失败');
                            }
                            resolve();
                        })
                        .catch((e) => {
                            reject(e);
                            smartSentry.captureError(e);
                            message.error('系统异常，请稍后重试');
                        })
                        .finally(() => {
                            SmartLoading.hide();
                        });
                });
            }
        });
    }

    // 删除单项
    function deleteItem(id) {
        Modal.confirm({
            title: '确认删除',
            icon: createVNode(ExclamationCircleOutlined),
            content: '确定要删除该备品备件吗？',
            onOk() {
                return new Promise((resolve, reject) => {
                    SmartLoading.show();
                    sparePartsApi
                        .deleteSpareParts(id)
                        .then((res) => {
                            if (res.ok) {
                                message.success('删除成功');
                                querySpareParts();
                            } else {
                                message.error(res.msg || '删除失败');
                            }
                            resolve();
                        })
                        .catch((e) => {
                            reject(e);
                            smartSentry.captureError(e);
                            message.error('系统异常，请稍后重试');
                        })
                        .finally(() => {
                            SmartLoading.hide();
                        });
                });
            }
        });
    }

    // 显示表单抽屉
    function showDrawer(record) {
        sparePartsFormModal.value.showModal(record);
    }

    // 首次加载
    querySpareParts();
</script>
<style scoped lang="less">
    .spare-parts-container {
        height: 100%;
        display: flex;
        flex-direction: column;

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;

            .query-operate {
                display: flex;
                align-items: center;
            }
        }

        .btn-group {
            display: flex;
            margin-bottom: 16px;

            .btn {
                margin-right: 8px;
            }
        }

        .configuration-content {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        :deep(.ant-card-body) {
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 16px;
            overflow: hidden;
        }

        :deep(.ant-pagination) {
            margin-top: 16px;
            text-align: right;
        }

        :deep(.ant-table-wrapper) {
            flex: 1;
            overflow: auto;
        }
    }
</style> 