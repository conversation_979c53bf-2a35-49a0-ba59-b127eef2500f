<!--
  * 技术方案生成表单
-->
<template>
  <div class="tech-solution-form">
    <a-card title="技术文档上传" class="form-card" :bordered="false">
      <template #title>
        <div class="card-title">
          <UploadOutlined />
          <span>技术文档上传</span>
        </div>
      </template>
      <div class="upload-area">
        <FileUpload
          ref="fileUploadRef"
          v-model:value="form.fileIds"
          :defaultFileList="fileList"
          :accept="acceptFileTypes"
          :folder="FILE_FOLDER_TYPE_ENUM.COMMON.value"
          buttonText="上传文档"
          listType="text"
          :maxSize="20"
          @change="handleFileChange"
        />
        <div class="file-tips">
          <InfoCircleOutlined />
          <span>支持上传 .md, .txt, .docx 格式文件，单个文件大小不超过20MB</span>
        </div>
      </div>
    </a-card>

    <a-card class="form-card" :bordered="false">
      <template #title>
        <div class="card-title">
          <FormOutlined />
          <span>AI 提示词</span>
        </div>
      </template>
      <div class="prompt-area">
        <a-textarea
          v-model:value="form.prompt"
          placeholder="请详细描述您需要解决的技术问题、期望的方案格式、关键技术要求等。"
          :auto-size="{ minRows: 6, maxRows: 10 }"
          :maxLength="2000"
          show-count
          class="prompt-textarea"
        />
        <div class="prompt-templates">
          <span class="template-title">提示词模板：</span>
          <a-dropdown>
            <a-button type="primary" size="small" ghost>
              选择模板
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleTemplateSelect">
                <a-menu-item v-for="(template, index) in TECH_SOLUTION_PROMPT_TEMPLATES" :key="index">
                  {{ template.name }}
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </a-card>

    <div class="form-actions">
      <a-button 
        type="primary" 
        :loading="generating" 
        @click="generateSolution" 
        :disabled="!isFormValid"
        size="large"
        class="generate-button"
      >
        <template #icon><ThunderboltOutlined /></template>
        {{ generating ? '生成中...' : '生成方案' }}
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { message } from 'ant-design-vue';
import { 
  InfoCircleOutlined, 
  DownOutlined, 
  UploadOutlined, 
  FormOutlined,
  ThunderboltOutlined
} from '@ant-design/icons-vue';
import { FILE_FOLDER_TYPE_ENUM } from '/@/constants/support/file-const';
import { TECH_SOLUTION_PROMPT_TEMPLATES, TECH_SOLUTION_FILE_TYPES } from '/@/constants/business/tech-solution-const';
import FileUpload from '/@/components/support/file-upload/index.vue';
import { techSolutionApi } from '/@/api/business/tech-solution-api';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { smartSentry } from '/@/lib/smart-sentry';

// 组件状态
const fileUploadRef = ref(null);
const fileList = ref([]);
const generating = ref(false);
const acceptFileTypes = TECH_SOLUTION_FILE_TYPES;

// 表单数据
const form = reactive({
  prompt: '',
  fileIds: '',
  title: '技术方案'
});

// 计算属性：表单是否有效
const isFormValid = computed(() => {
  return form.prompt && form.fileIds;
});

// 文件上传变化处理
function handleFileChange(files) {
  fileList.value = files;
  // 更新 fileIds，用逗号分隔的文件ID字符串
  if (files && files.length > 0) {
    form.fileIds = files.map(file => file.fileId).join(',');
  } else {
    form.fileIds = '';
  }
}

// 选择提示词模板
function handleTemplateSelect(e) {
  const template = TECH_SOLUTION_PROMPT_TEMPLATES[e.key];
  form.prompt = template.content;
  message.success(`已应用模板：${template.name}`);
}

// 生成技术方案
async function generateSolution() {
  if (!isFormValid.value) {
    message.warning('请填写所有必填项');
    return;
  }

  try {
    generating.value = true;
    SmartLoading.show();

    // 调用生成API，不再传递apiKey参数
    const res = await techSolutionApi.generateTechSolution({
      prompt: form.prompt,
      fileIds: form.fileIds,
      title: form.title
    });

    if (res.ok) {
      message.success('技术方案生成成功');
      // 触发事件，通知父组件更新
      emit('solution-generated', res.data);
    } else {
      message.error(res.msg || '生成失败');
    }
  } catch (e) {
    console.error('生成方案出错:', e);
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    generating.value = false;
    SmartLoading.hide();
  }
}

// 清空表单
function resetForm() {
  form.prompt = '';
  form.fileIds = '';
  form.title = '技术方案';
  fileList.value = [];
  if (fileUploadRef.value) {
    fileUploadRef.value.clear();
  }
}

// 定义组件对外暴露的方法
defineExpose({
  resetForm,
  generateSolution
});

// 定义组件的事件
const emit = defineEmits(['solution-generated']);
</script>

<style scoped lang="less">
.tech-solution-form {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding-right: 16px;

  .form-card {
    margin-bottom: 16px;
    border-radius: 8px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    
    &:hover {
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    }
    
    :deep(.ant-card-head) {
      min-height: 48px;
      padding: 0 16px;
      border-bottom: 1px solid #f0f0f0;
      
      .ant-card-head-title {
        padding: 12px 0;
      }
    }
    
    :deep(.ant-card-body) {
      padding: 16px;
    }
  }
  
  .card-title {
    display: flex;
    align-items: center;
    font-weight: 500;
    
    .anticon {
      margin-right: 8px;
      color: #1890ff;
    }
  }

  .upload-area {
    .file-tips {
      margin-top: 10px;
      color: #999;
      font-size: 12px;
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background-color: #f9f9f9;
      border-radius: 4px;

      .anticon {
        margin-right: 6px;
        color: #1890ff;
      }
    }
  }

  .prompt-area {
    .prompt-textarea {
      border-radius: 6px;
      resize: none;
      
      &:focus {
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
    
    .prompt-templates {
      margin-top: 12px;
      align-items: center;
      justify-content: flex-start;

      .template-title {
        margin-right: 8px;
        color: #666;
      }
    }
  }

  .form-actions {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    
    .generate-button {
      min-width: 140px;
      font-weight: 500;
      border-radius: 6px;
      height: 40px;
      transition: all 0.3s;
      
      &:not(:disabled):hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25);
      }
    }
  }
}
</style>