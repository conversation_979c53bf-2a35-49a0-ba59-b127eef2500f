<!--
  * 技术方案历史记录
-->
<template>
  <div class="tech-solution-history">
    <a-modal
      v-model:open="visible"
      title="历史方案记录"
      width="800px"
      :footer="null"
      @cancel="handleCancel"
    >
      <div class="history-content">
        <div class="history-header">
          <a-input-search
            v-model:value="params.keyword"
            placeholder="搜索方案标题"
            style="width: 250px"
            @search="handleSearch"
          />
          <a-button type="primary" danger @click="handleBatchDelete" :disabled="!selectedRowKeys.length">
            批量删除
          </a-button>
        </div>

        <a-table
          :columns="columns"
          :data-source="tableData"
          :pagination="false"
          :loading="loading"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          row-key="id"
          size="small"
          style="margin-top: 16px"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(record.status)">{{ getStatusText(record.status) }}</a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'createTime'">
              {{ text }}
            </template>
            <template v-else-if="column.dataIndex === 'action'">
              <a-space>
                <a @click="handleView(record)">查看</a>
                <a-divider type="vertical" />
                <a-popconfirm
                  title="确定要删除这条记录吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="handleDelete(record.id)"
                >
                  <a>删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>

        <div class="smart-query-table-page">
          <a-pagination
            showSizeChanger
            showQuickJumper
            show-less-items
            :pageSizeOptions="PAGE_SIZE_OPTIONS"
            :defaultPageSize="params.pageSize"
            v-model:current="params.pageNum"
            v-model:pageSize="params.pageSize"
            :total="total"
            @change="queryHistory"
            @showSizeChange="queryHistory"
            :show-total="showTableTotal"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { createVNode } from 'vue';
import { techSolutionApi } from '/@/api/business/tech-solution-api';
import { TECH_SOLUTION_STATUS_ENUM } from '/@/constants/business/tech-solution-const';
import { PAGE_SIZE, PAGE_SIZE_OPTIONS, showTableTotal } from '/@/constants/common-const';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { smartSentry } from '/@/lib/smart-sentry';

// 组件状态
const visible = ref(false);
const loading = ref(false);
const tableData = ref([]);
const total = ref(0);
const selectedRowKeys = ref([]);

// 查询参数
const params = reactive({
  pageNum: 1,
  pageSize: Number(PAGE_SIZE),
  keyword: ''
});

// 表格列定义
const columns = [
  {
    title: '标题',
    dataIndex: 'title',
    width: 200
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
    width: 120
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120
  }
];

// 获取状态颜色
function getStatusColor(status) {
  switch (status) {
    case TECH_SOLUTION_STATUS_ENUM.PROCESSING.value:
      return 'processing';
    case TECH_SOLUTION_STATUS_ENUM.COMPLETED.value:
      return 'success';
    case TECH_SOLUTION_STATUS_ENUM.FAILED.value:
      return 'error';
    default:
      return 'default';
  }
}

// 获取状态文本
function getStatusText(status) {
  switch (status) {
    case TECH_SOLUTION_STATUS_ENUM.PROCESSING.value:
      return TECH_SOLUTION_STATUS_ENUM.PROCESSING.desc;
    case TECH_SOLUTION_STATUS_ENUM.COMPLETED.value:
      return TECH_SOLUTION_STATUS_ENUM.COMPLETED.desc;
    case TECH_SOLUTION_STATUS_ENUM.FAILED.value:
      return TECH_SOLUTION_STATUS_ENUM.FAILED.desc;
    default:
      return '未知状态';
  }
}

// 显示模态框
function showModal() {
  visible.value = true;
  queryHistory();
}

// 关闭模态框
function handleCancel() {
  visible.value = false;
}

// 选择行变化
function onSelectChange(keys) {
  selectedRowKeys.value = keys;
}

// 搜索
function handleSearch() {
  params.pageNum = 1;
  queryHistory();
}

// 查询历史记录
async function queryHistory() {
  try {
    loading.value = true;
    const res = await techSolutionApi.queryTechSolution({
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      keyword: params.keyword
    });

    if (res.ok) {
      tableData.value = res.data.list || [];
      total.value = res.data.total || 0;
    } else {
      message.error(res.msg || '查询失败');
    }
  } catch (e) {
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    loading.value = false;
  }
}

// 查看记录
function handleView(record) {
  emit('view-solution', record);
  handleCancel();
}

// 删除记录
async function handleDelete(id) {
  try {
    SmartLoading.show();
    const res = await techSolutionApi.deleteTechSolution(id);
    if (res.ok) {
      message.success('删除成功');
      queryHistory();
    } else {
      message.error(res.msg || '删除失败');
    }
  } catch (e) {
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    SmartLoading.hide();
  }
}

// 批量删除
function handleBatchDelete() {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的记录');
    return;
  }

  Modal.confirm({
    title: '确认删除',
    icon: createVNode(ExclamationCircleOutlined),
    content: '确定要删除选中的技术方案记录吗？',
    onOk() {
      return new Promise((resolve, reject) => {
        SmartLoading.show();
        techSolutionApi
          .batchDeleteTechSolution(selectedRowKeys.value)
          .then((res) => {
            if (res.ok) {
              message.success('删除成功');
              selectedRowKeys.value = [];
              queryHistory();
            } else {
              message.error(res.msg || '删除失败');
            }
            resolve();
          })
          .catch((e) => {
            reject(e);
            smartSentry.captureError(e);
            message.error('系统异常，请稍后重试');
          })
          .finally(() => {
            SmartLoading.hide();
          });
      });
    }
  });
}

// 定义组件对外暴露的方法和属性
defineExpose({
  showModal
});

// 定义组件的事件
const emit = defineEmits(['view-solution']);
</script>

<style scoped lang="less">
.tech-solution-history {
  .history-content {
    min-height: 400px;
    
    .history-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
    }
  }
}
</style> 