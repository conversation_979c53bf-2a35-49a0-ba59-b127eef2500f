<!--
  * 技术方案结果展示
-->
<template>
  <div class="tech-solution-result">
    <!-- 结果为空时的提示 -->
    <div v-if="!solution || !solution.id" class="empty-result">
      <a-empty description="请在左侧填写表单并点击「生成方案」按钮" />
    </div>
    
    <!-- 结果展示 -->
    <div v-else class="result-content">
      <div class="result-header">
        <div class="result-title">
          <a-typography-title :level="5">{{ solution.title || '技术方案' }}</a-typography-title>
          <a-tag 
            :color="solution.status === 'COMPLETED' ? 'success' : (solution.status === 'FAILED' ? 'error' : 'processing')"
            class="status-tag"
          >
            {{ getStatusText(solution.status) }}
          </a-tag>
        </div>
        <div class="result-actions">
          <a-tooltip title="目录">
            <a-button type="text" @click="toggleToc" :disabled="!solution.content" shape="circle">
              <template #icon><UnorderedListOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="复制内容">
            <a-button type="text" @click="copyContent" :disabled="!solution.content" shape="circle">
              <template #icon><CopyOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="下载Markdown">
            <a-button type="text" @click="downloadMarkdown" :disabled="!solution.content" shape="circle">
              <template #icon><DownloadOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="保存修改">
            <a-button type="text" @click="saveContent" :disabled="!isEdited || !solution.content" shape="circle">
              <template #icon><SaveOutlined /></template>
            </a-button>
          </a-tooltip>
        </div>
      </div>
      
      <!-- 目录导航 -->
      <div v-if="showToc && solution.content" class="markdown-toc">
        <div class="toc-title">
          <BookOutlined />
          <span>目录导航</span>
        </div>
        <div class="toc-content" v-html="tocHtml"></div>
      </div>
      
      <!-- 文档内容 -->
      <div class="markdown-content">
        <!-- 加载中 -->
        <div v-if="solution.status === 'PROCESSING'" class="processing-content">
          <a-spin :spinning="true" tip="AI正在生成方案，请稍候...">
            <div class="loading-content">
              <a-progress :percent="70" status="active" stroke-color="#1890ff" />
              <div class="loading-text">
                <div class="loading-message">分析文档中，这可能需要一点时间...</div>
              </div>
            </div>
          </a-spin>
        </div>
        
        <!-- 失败提示 -->
        <div v-else-if="solution.status === 'FAILED'" class="failed-content">
          <a-result
            status="error"
            title="生成失败"
            sub-title="技术方案生成失败，请重试或联系管理员"
          >
            <template #extra>
              <a-button type="primary" @click="retryGenerate">
                重新生成
              </a-button>
            </template>
          </a-result>
        </div>
        
        <!-- 成功结果 - 富文本编辑器区域 -->
        <div v-else-if="solution.content" class="rich-editor-content">
          <WangEditor 
            v-model="editorContent" 
            :height="500"
            @update:modelValue="handleEditorChange"
            ref="richEditor"
          />
        </div>
        
        <!-- 无内容 -->
        <div v-else class="empty-content">
          <a-empty description="暂无内容" />
        </div>
      </div>

      <!-- 编辑状态提示 -->
      <div v-if="isEdited && solution.content" class="edit-status">
        <a-alert type="info" show-icon banner>
          <template #message>
            <div class="edit-tip">
              <span>内容已修改，请点击<SaveOutlined class="save-icon" />按钮保存更改</span>
              <a-button type="primary" size="small" @click="saveContent">
                保存修改
              </a-button>
            </div>
          </template>
        </a-alert>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { 
  CopyOutlined, 
  DownloadOutlined, 
  UnorderedListOutlined, 
  SaveOutlined,
  BookOutlined
} from '@ant-design/icons-vue';
import MarkdownIt from 'markdown-it';
import DOMPurify from 'dompurify';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css';
import { smartSentry } from '/@/lib/smart-sentry';
import { techSolutionApi } from '/@/api/business/tech-solution-api';
import { SmartLoading } from '/@/components/framework/smart-loading';
import WangEditor from '/@/components/framework/wangeditor/index.vue';
import '/@/assets/styles/markdown-theme.css';

// 初始化markdown-it
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(str, { language: lang }).value;
      } catch (e) {
        console.error('代码高亮失败:', e);
      }
    }
    return ''; // 使用外部默认转义
  }
});

// 组件属性
const props = defineProps({
  solution: {
    type: Object,
    default: () => ({})
  }
});

// 组件事件
const emit = defineEmits(['retry', 'content-updated']);

// 状态
const showToc = ref(false);
const isEdited = ref(false);
const originalContent = ref('');
const editorContent = ref('');
const richEditor = ref(null);

// 监听solution变化，重置编辑状态和富文本内容
watch(() => props.solution, (newVal) => {
  if (newVal) {
    // 更新内容状态
    if (newVal.content) {
      originalContent.value = newVal.content;
      // 将Markdown内容转换为HTML并设置到编辑器
      try {
        const rawHtml = md.render(newVal.content);
        const safeHtml = DOMPurify.sanitize(rawHtml);
        nextTick(() => {
          editorContent.value = safeHtml;
          isEdited.value = false;
        });
      } catch (error) {
        console.error('HTML渲染错误:', error);
        editorContent.value = `<div class="markdown-error">渲染错误: ${error.message}</div>`;
      }
    }
  }
}, { deep: true, immediate: true });

// 生成目录HTML
const tocHtml = computed(() => {
  if (!props.solution || !props.solution.content) return '';
  
  try {
    // 创建临时DOM解析渲染后的HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = DOMPurify.sanitize(md.render(props.solution.content));
    
    // 获取所有标题元素
    const headings = tempDiv.querySelectorAll('h1, h2, h3, h4');
    if (headings.length === 0) return '<p>没有可用的目录</p>';
    
    // 构建目录HTML
    let tocContent = '<ul class="toc-list">';
    headings.forEach((heading, index) => {
      // 为每个标题添加ID，如果没有的话
      if (!heading.id) {
        heading.id = `heading-${index}`;
      }
      
      const tagName = heading.tagName.toLowerCase();
      const level = parseInt(tagName.charAt(1)) - 1;
      const indentClass = `indent-${level}`;
      
      tocContent += `<li class="${indentClass}"><a href="#${heading.id}">${heading.textContent}</a></li>`;
    });
    tocContent += '</ul>';
    
    return tocContent;
  } catch (error) {
    console.error('目录生成错误:', error);
    return '<p>目录生成失败</p>';
  }
});

// 获取状态文本
function getStatusText(status) {
  switch(status) {
    case 'PROCESSING': return '生成中';
    case 'COMPLETED': return '已完成';
    case 'FAILED': return '生成失败';
    default: return status;
  }
}

// 切换目录显示
function toggleToc() {
  showToc.value = !showToc.value;
}

// 重试生成
function retryGenerate() {
  emit('retry');
}

// 处理编辑器内容变更
function handleEditorChange(html) {
  isEdited.value = html !== originalContent.value;
}

// 保存修改后的内容
async function saveContent() {
  if (!isEdited.value || !props.solution || !props.solution.id) return;
  
  try {
    SmartLoading.show();
    
    // 获取富文本编辑器的HTML内容
    const htmlContent = richEditor.value?.getHtml() || '';
    
    // 调用API保存内容
    const res = await techSolutionApi.updateTechSolutionContent(props.solution.id, htmlContent);
    
    if (res.ok) {
      originalContent.value = htmlContent;
      isEdited.value = false;
      message.success('内容保存成功');
      
      // 触发内容更新事件
      emit('content-updated', res.data);
    } else {
      message.error(res.msg || '保存失败');
    }
  } catch (e) {
    smartSentry.captureError(e);
    message.error('保存失败，请稍后重试');
  } finally {
    SmartLoading.hide();
  }
}

// 复制内容
function copyContent() {
  if (props.solution && props.solution.content) {
    navigator.clipboard.writeText(props.solution.content)
      .then(() => {
        message.success('内容已复制到剪贴板');
      })
      .catch(() => {
        message.error('复制失败，请手动复制');
      });
  }
}

// 下载Markdown
function downloadMarkdown() {
  if (props.solution && props.solution.content) {
    const element = document.createElement('a');
    const file = new Blob([props.solution.content], { type: 'text/markdown' });
    element.href = URL.createObjectURL(file);
    element.download = `${props.solution.title || '技术方案'}.md`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  }
}
</script>

<style scoped lang="less">
.tech-solution-result {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .empty-result {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(to bottom, #ffffff, #f5f8ff);
    border-radius: 8px;
  }
  
  .result-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

    .result-header {
      padding: 16px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .result-title {
        display: flex;
        align-items: center;
        
        :deep(.ant-typography) {
          margin-bottom: 0;
          margin-right: 10px;
          color: #0050b3;
        }
        
        .status-tag {
          font-weight: 500;
          border-radius: 4px;
        }
      }
      
      .result-actions {
        display: flex;
        gap: 4px;
        
        :deep(.ant-btn) {
          color: #1890ff;
          
          &:hover {
            color: #40a9ff;
            background-color: rgba(24, 144, 255, 0.1);
          }
          
          &:disabled {
            color: rgba(0, 0, 0, 0.25);
          }
        }
      }
    }
    
    .markdown-toc {
      padding: 12px 16px;
      background: #f8faff;
      border-bottom: 1px solid #e6f7ff;
      max-height: 200px;
      overflow-y: auto;
      
      .toc-title {
        font-weight: 500;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        color: #0050b3;
        
        .anticon {
          margin-right: 6px;
        }
      }
      
      .toc-content {
        :deep(.toc-list) {
          margin: 0;
          padding: 0;
          list-style: none;
          
          li {
            margin: 4px 0;
            
            &.indent-0 { margin-left: 0; }
            &.indent-1 { margin-left: 16px; }
            &.indent-2 { margin-left: 32px; }
            &.indent-3 { margin-left: 48px; }
            
            a {
              color: #1890ff;
              text-decoration: none;
              
              &:hover {
                text-decoration: underline;
              }
            }
          }
        }
      }
    }
    
    .markdown-content {
      flex: 1;
      padding: 20px;
      overflow: auto;
      background-color: #fff;
      position: relative;
      
      .processing-content, .failed-content, .empty-content {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .loading-content {
          width: 85%;
          max-width: 500px;
          
          .loading-text {
            margin-top: 16px;
            text-align: center;
            
            .loading-message {
              margin-top: 12px;
              color: rgba(0, 0, 0, 0.45);
              font-size: 14px;
            }
          }
        }
      }
      
      .rich-editor-content {
        height: 100%;
        overflow: hidden;
        
        :deep(.w-e-scroll) {
          min-height: 400px;
        }
      }
    }
    
    .edit-status {
      border-top: 1px solid #e6f7ff;
      
      :deep(.ant-alert) {
        border: none;
        border-radius: 0 0 8px 8px;
        
        .edit-tip {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          
          .save-icon {
            margin: 0 4px;
            color: #1890ff;
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .result-content {
      .result-header {
        flex-wrap: wrap;
        
        .result-title {
          margin-bottom: 12px;
        }
      }
    }
  }
}
</style> 