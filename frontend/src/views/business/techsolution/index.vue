<!--
  * AI技术方案生成
-->
<template>
  <div class="tech-solution-container">
    <a-card class="tech-solution-header">
      <div class="header-content">
        <div class="header-title">
          <a-typography-title :level="4">AI技术方案生成</a-typography-title>
        </div>
      </div>
    </a-card>

    <div class="tech-solution-content">
      <!-- 左侧表单 -->
      <div class="form-container">
        <TechSolutionForm ref="formRef" @solution-generated="handleSolutionGenerated" />
      </div>

      <!-- 右侧结果 -->
      <div class="result-container">
        <TechSolutionResult :solution="currentSolution" @retry="handleRetry" />
      </div>
    </div>

    <!-- 历史记录模态框保留，但不在主界面显示按钮 -->
    <TechSolutionHistory ref="historyRef" @view-solution="handleViewSolution" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import TechSolutionForm from './components/tech-solution-form/index.vue';
import TechSolutionResult from './components/tech-solution-result/index.vue';
import TechSolutionHistory from './components/tech-solution-history/index.vue';
import { techSolutionApi } from '/@/api/business/tech-solution-api';
import { message } from 'ant-design-vue';
import { smartSentry } from '/@/lib/smart-sentry';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { useRouter } from 'vue-router';

// 获取router实例
const router = useRouter();

// 组件引用
const formRef = ref(null);
const historyRef = ref(null);

// 当前选中的技术方案
const currentSolution = ref({});

// 处理方案生成
function handleSolutionGenerated(solution) {
  currentSolution.value = solution;
}

// 处理查看历史方案
async function handleViewSolution(solution) {
  try {
    SmartLoading.show();
    const res = await techSolutionApi.getTechSolutionDetail(solution.id);
    if (res.ok) {
      currentSolution.value = res.data;
    } else {
      message.error(res.msg || '获取方案详情失败');
    }
  } catch (e) {
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    SmartLoading.hide();
  }
}

// 处理重试
function handleRetry() {
  if (formRef.value) {
    formRef.value.generateSolution();
  }
}
</script>

<style scoped lang="less">
.tech-solution-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  max-width: 1200px; /* 限制最大宽度 */
  margin: 0 auto; /* 居中显示 */
  
  .tech-solution-header {
    margin-bottom: 12px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-title {
        :deep(.ant-typography) {
          margin-bottom: 0;
        }
      }
    }
  }
  
  .tech-solution-content {
    flex: 1;
    display: flex;
    gap: 16px;
    overflow: hidden;
    
    .form-container {
      width: 30%; /* 从40%减少到30% */
      min-width: 350px;
      max-width: 450px;
      overflow: hidden;
    }
    
    .result-container {
      flex: 1;
      overflow: hidden;
      padding: 0 20px; /* 增加内边距 */
      border-radius: 8px;
      background-color: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }
  }
  
  @media (max-width: 992px) {
    .tech-solution-content {
      flex-direction: column;
      
      .form-container {
        width: 100%;
        max-width: 100%;
        height: 50%;
        min-height: 400px;
      }
      
      .result-container {
        height: 50%;
        padding: 0; /* 小屏幕时移除内边距 */
      }
    }
  }
}
</style> 