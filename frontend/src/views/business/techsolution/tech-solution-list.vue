<!--
  * 技术方案列表
-->
<template>
  <div class="tech-solution-list-container">
    <a-card class="solution-list-card">
      <!-- 头部搜索和操作区域 -->
      <div class="list-header">
        <div class="search-area">
          <a-input-search
            v-model:value="searchParam.keyword"
            placeholder="请输入标题或提示词搜索"
            style="width: 250px"
            @search="handleSearch"
          />
          <a-select
            v-model:value="searchParam.status"
            style="width: 150px; margin-left: 8px"
            placeholder="选择状态"
            @change="handleSearch"
          >
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option value="PROCESSING">处理中</a-select-option>
            <a-select-option value="COMPLETED">已完成</a-select-option>
            <a-select-option value="FAILED">失败</a-select-option>
          </a-select>
          <a-range-picker
            v-model:value="dateRange"
            style="margin-left: 8px"
            @change="handleDateRangeChange"
            :placeholder="['开始日期', '结束日期']"
          />
        </div>
        <div class="operation-area">
          <a-button
            danger
            :disabled="selectedRowKeys.length === 0"
            @click="handleBatchDelete"
          >
            <template #icon><delete-outlined /></template>
            批量删除
          </a-button>
        </div>
      </div>

      <!-- 表格 -->
      <a-table
        :columns="columns"
        :data-source="solutionList"
        :pagination="pagination"
        :loading="loading"
        :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
        row-key="id"
        @change="handleTableChange"
        :scroll="{ x: 'max-content' }"
        table-layout="auto"
      >
        <!-- 标题列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'title'">
            <a @click="handleViewSolution(record)">{{ record.title }}</a>
          </template>

          <!-- 状态列 -->
          <template v-if="column.dataIndex === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <!-- 创建时间列 -->
          <template v-if="column.dataIndex === 'createTime'">
            {{ formatDate(record.createTime) }}
          </template>

          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <div class="action-buttons">
              <a-button type="link" @click="handleViewSolution(record)">查看</a-button>
              <a-button 
                type="link" 
                @click="handleEditSolution(record)"
                :disabled="record.status === 'PROCESSING'"
              >编辑</a-button>
              <a-popconfirm
                title="确定要删除该技术方案吗?"
                @confirm="handleDelete(record)"
                ok-text="确定"
                cancel-text="取消"
              >
                <a-button type="link" danger>删除</a-button>
              </a-popconfirm>
            </div>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 查看方案弹窗 -->
    <a-modal
      v-model:visible="viewModalVisible"
      title="技术方案详情"
      width="1200px"
      :footer="null"
    >
      <div v-if="currentSolution.id">
        <div class="solution-header">
          <h2>{{ currentSolution.title }}</h2>
          <a-button type="primary" @click="handleExportWord">导出Word</a-button>
        </div>
        <a-descriptions :column="2">
          <a-descriptions-item label="创建时间">{{ formatDate(currentSolution.createTime) }}</a-descriptions-item>
          <a-descriptions-item label="创建人">{{ currentSolution.creatorName }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(currentSolution.status)">
              {{ getStatusText(currentSolution.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ formatDate(currentSolution.updateTime) }}</a-descriptions-item>
        </a-descriptions>
        <a-divider />
        <h3>方案内容</h3>
        <div class="solution-content" v-html="sanitizeHtml(formatContent(currentSolution.content))"></div>
      </div>
    </a-modal>

    <!-- 编辑方案弹窗 -->
    <a-modal
      v-model:visible="editModalVisible"
      title="编辑技术方案"
      width="1200px"
      @ok="handleSaveEdit"
      ok-text="保存"
      cancel-text="取消"
    >
      <div v-if="currentSolution.id">
        <a-form :model="editForm" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
          <a-form-item label="标题" name="title">
            <a-input v-model:value="editForm.title" placeholder="请输入标题" />
          </a-form-item>
          <a-form-item label="内容" name="content">
            <WangEditor v-model="editForm.content" :height="1150" />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { DeleteOutlined, DownloadOutlined } from '@ant-design/icons-vue';
import { techSolutionApi } from '/@/api/business/tech-solution-api';
import { smartSentry } from '/@/lib/smart-sentry';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { formatDate as formatDateUtil } from '/@/utils/date-util';
import { marked } from 'marked';
import WangEditor from '/@/components/framework/wangeditor/index.vue';
import DOMPurify from 'dompurify';

// 表格列定义
const columns = [
  {
    title: '标题',
    dataIndex: 'title',
    sorter: true,
    ellipsis: true,
    minWidth: 200
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    filters: [
      { text: '处理中', value: 'PROCESSING' },
      { text: '已完成', value: 'COMPLETED' },
      { text: '失败', value: 'FAILED' }
    ]
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 220,
    fixed: 'right'
  }
];

// 状态数据
const loading = ref(false);
const solutionList = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: total => `共 ${total} 条记录`
});
const selectedRowKeys = ref([]);
const searchParam = reactive({
  keyword: '',
  status: '',
  startTime: null,
  endTime: null
});
const dateRange = ref([]);

// 详情查看相关
const viewModalVisible = ref(false);
const currentSolution = ref({});

// 编辑相关
const editModalVisible = ref(false);
const editForm = reactive({
  id: null,
  title: '',
  prompt: '',
  content: ''
});

// 初始化加载
onMounted(() => {
  fetchSolutionList();
});

// 获取方案列表
async function fetchSolutionList() {
  try {
    loading.value = true;
    
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      searchParam: searchParam.keyword,
      status: searchParam.status,
      startTime: searchParam.startTime,
      endTime: searchParam.endTime
    };
    
    const res = await techSolutionApi.queryTechSolution(params);
    if (res.ok) {
      solutionList.value = res.data.records;
      pagination.total = res.data.total;
      pagination.current = res.data.pageNum;
      pagination.pageSize = res.data.pageSize;
    } else {
      message.error(res.msg || '获取方案列表失败');
    }
  } catch (e) {
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    loading.value = false;
  }
}

// 搜索处理
function handleSearch() {
  pagination.current = 1;
  fetchSolutionList();
}

// 日期范围变更
function handleDateRangeChange(dates) {
  if (dates && dates.length === 2) {
    searchParam.startTime = dates[0] ? dates[0].startOf('day').valueOf() : null;
    searchParam.endTime = dates[1] ? dates[1].endOf('day').valueOf() : null;
  } else {
    searchParam.startTime = null;
    searchParam.endTime = null;
  }
  handleSearch();
}

// 表格变更
function handleTableChange(pag, filters, sorter) {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  
  // 处理筛选
  if (filters.status && filters.status.length) {
    searchParam.status = filters.status[0];
  } else {
    searchParam.status = '';
  }
  
  // 处理排序
  const sortMap = {
    title: 'title',
    createTime: 'createTime'
  };
  
  if (sorter.field && sorter.order) {
    searchParam.sortField = sortMap[sorter.field];
    searchParam.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc';
  } else {
    searchParam.sortField = null;
    searchParam.sortOrder = null;
  }
  
  fetchSolutionList();
}

// 选择行变更
function onSelectChange(keys) {
  selectedRowKeys.value = keys;
}

// 查看方案详情
async function handleViewSolution(record) {
  try {
    SmartLoading.show();
    const res = await techSolutionApi.getTechSolutionDetail(record.id);
    if (res.ok) {
      currentSolution.value = res.data;
      viewModalVisible.value = true;
    } else {
      message.error(res.msg || '获取方案详情失败');
    }
  } catch (e) {
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    SmartLoading.hide();
  }
}

// 编辑方案
async function handleEditSolution(record) {
  try {
    // 为确保获取最新的完整数据，先通过API获取详情
    SmartLoading.show();
    const res = await techSolutionApi.getTechSolutionDetail(record.id);
    
    if (res.ok) {
      const detailData = res.data;
      
      // 设置表单基本信息
      editForm.id = detailData.id;
      editForm.title = detailData.title;
      editForm.prompt = detailData.prompt;
      
      // 转换Markdown内容为HTML，并设置到编辑器
      try {
        // 使用marked将Markdown转换为HTML
        const rawHtml = marked(detailData.content || '');
        // 使用DOMPurify净化HTML，防止XSS攻击
        const safeHtml = DOMPurify.sanitize(rawHtml, { USE_PROFILES: { html: true } });
        // 设置到编辑表单
        editForm.content = safeHtml;
      } catch (error) {
        console.error('HTML渲染错误:', error);
        message.warning('内容格式转换失败，可能影响编辑效果');
        // 出错时仍使用原始内容
        editForm.content = detailData.content || '';
      }
      
      // 显示编辑弹窗
      editModalVisible.value = true;
    } else {
      message.error(res.msg || '获取方案详情失败');
    }
  } catch (e) {
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    SmartLoading.hide();
  }
}

// 保存编辑
async function handleSaveEdit() {
  try {
    SmartLoading.show();
    
    // 构建更新参数
    const params = {
      id: editForm.id,
      content: editForm.content
    };
    
    const res = await techSolutionApi.updateTechSolutionContent(editForm.id, editForm.content);
    if (res.ok) {
      message.success('更新成功');
      editModalVisible.value = false;
      
      // 刷新列表
      fetchSolutionList();
      
      // 如果当前正在查看的方案是被编辑的方案，刷新当前方案数据
      if (currentSolution.value && currentSolution.value.id === editForm.id) {
        const detailRes = await techSolutionApi.getTechSolutionDetail(editForm.id);
        if (detailRes.ok) {
          currentSolution.value = detailRes.data;
        }
      }
    } else {
      message.error(res.msg || '更新失败');
    }
  } catch (e) {
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    SmartLoading.hide();
  }
}

// 删除方案
async function handleDelete(record) {
  try {
    SmartLoading.show();
    const res = await techSolutionApi.deleteTechSolution(record.id);
    if (res.ok) {
      message.success('删除成功');
      fetchSolutionList();
    } else {
      message.error(res.msg || '删除失败');
    }
  } catch (e) {
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    SmartLoading.hide();
  }
}

// 批量删除
function handleBatchDelete() {
  if (selectedRowKeys.value.length === 0) {
    return;
  }
  
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个方案吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        SmartLoading.show();
        const res = await techSolutionApi.batchDeleteTechSolution(selectedRowKeys.value);
        if (res.ok) {
          message.success('批量删除成功');
          selectedRowKeys.value = [];
          fetchSolutionList();
        } else {
          message.error(res.msg || '批量删除失败');
        }
      } catch (e) {
        smartSentry.captureError(e);
        message.error('系统异常，请稍后重试');
      } finally {
        SmartLoading.hide();
      }
    }
  });
}

// 格式化日期
function formatDate(date) {
  if (!date) return '';
  return formatDateUtil(date, 'YYYY-MM-DD HH:mm');
}

// 获取状态文字
function getStatusText(status) {
  const statusMap = {
    'PROCESSING': '处理中',
    'COMPLETED': '已完成',
    'FAILED': '失败'
  };
  return statusMap[status] || status;
}

// 获取状态颜色
function getStatusColor(status) {
  const colorMap = {
    'PROCESSING': 'blue',
    'COMPLETED': 'green',
    'FAILED': 'red'
  };
  return colorMap[status] || '';
}

// 格式化内容
function formatContent(content) {
  if (!content) return '';
  try {
    // 尝试将内容解析为Markdown并转换为HTML
    return marked(content);
  } catch (e) {
    // 如果解析失败，直接返回原始内容
    return content;
  }
}

// 安全地格式化内容
function sanitizeHtml(html) {
  if (!html) return '';
  return DOMPurify.sanitize(html, { USE_PROFILES: { html: true } });
}

// 导出Word文档
function handleExportWord() {
  try {
    SmartLoading.show();
    
    // 获取当前方案数据
    const solution = currentSolution.value;
    if (!solution) {
      message.error('未找到方案数据');
      SmartLoading.hide();
      return;
    }
    
    // 创建一个简单的HTML内容
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${solution.title || '技术方案'}</title>
        <style>
          body { font-family: SimSun, sans-serif; margin: 20px; }
          h1 { text-align: center; font-size: 18pt; }
          h2 { margin-top: 20px; border-bottom: 1px solid #ddd; padding-bottom: 5px; font-size: 14pt; }
          h3 { font-size: 12pt; }
          table { width: 100%; border-collapse: collapse; margin: 15px 0; }
          table, th, td { border: 1px solid #000; }
          th, td { padding: 8px; text-align: left; }
        </style>
      </head>
      <body>
        <h1>${solution.title || '技术方案'}</h1>
        
        <table>
          <tr>
            <td width="15%"><strong>创建时间</strong></td>
            <td width="35%">${formatDate(solution.createTime) || ''}</td>
            <td width="15%"><strong>创建人</strong></td>
            <td width="35%">${solution.creatorName || ''}</td>
          </tr>
          <tr>
            <td><strong>状态</strong></td>
            <td>${getStatusText(solution.status) || ''}</td>
            <td><strong>更新时间</strong></td>
            <td>${formatDate(solution.updateTime) || ''}</td>
          </tr>
        </table>
        
        <h2>方案内容</h2>
        <div>${solution.content ? marked(solution.content) : ''}</div>
      </body>
      </html>
    `;
    
    // 创建Blob对象
    const blob = new Blob([htmlContent], { type: 'application/msword' });
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${solution.title || '技术方案'}.doc`;
    
    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    message.success('导出成功');
    SmartLoading.hide();
  } catch (error) {
    console.error('导出Word失败:', error);
    smartSentry.captureError(error);
    message.error('导出失败，请稍后重试');
    SmartLoading.hide();
  }
}
</script>

<style scoped lang="less">
.tech-solution-list-container {
  padding: 16px;
  
  .solution-list-card {
    margin-bottom: 20px;
    
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .search-area {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;
      }
      
      .operation-area {
        display: flex;
        align-items: center;
      }
    }
  }
  
  // 操作按钮样式
  .action-buttons {
    display: flex;
    flex-wrap: nowrap;
    white-space: nowrap;
    
    :deep(.ant-btn) {
      padding: 0 8px;
    }
  }
  
  // 弹窗样式
  .solution-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h2 {
      margin-bottom: 0;
    }
  }
  
  .prompt-content {
    background-color: #f8f8f8;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 16px;
    white-space: pre-wrap;
  }
  
  .solution-content {
    background-color: #fff;
    padding: 16px;
    border: 1px solid #eee;
    border-radius: 4px;
    max-height: 500px;
    overflow-y: auto;
    
    :deep(p) {
      margin-bottom: 16px;
      line-height: 1.6;
    }
    
    :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
      margin-top: 20px;
      margin-bottom: 12px;
      font-weight: 600;
    }
    
    :deep(ul), :deep(ol) {
      padding-left: 20px;
      margin-bottom: 16px;
    }
    
    :deep(li) {
      margin-bottom: 6px;
    }
    
    :deep(pre) {
      background-color: #f6f8fa;
      padding: 12px;
      border-radius: 4px;
      overflow: auto;
      margin-bottom: 16px;
    }
    
    :deep(img) {
      max-width: 100%;
      height: auto;
    }
    
    :deep(a) {
      color: #1890ff;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
    
    :deep(blockquote) {
      padding: 0 1em;
      color: #6a737d;
      border-left: 0.25em solid #dfe2e5;
      margin: 0 0 16px 0;
    }
    
    :deep(table) {
      border-collapse: collapse;
      margin-bottom: 16px;
      width: 100%;
      
      th, td {
        border: 1px solid #dfe2e5;
        padding: 8px;
      }
      
      th {
        background-color: #f6f8fa;
      }
    }
  }
}
</style> 