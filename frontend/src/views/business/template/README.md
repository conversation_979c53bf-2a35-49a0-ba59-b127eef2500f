# 模板管理组件技术维护说明（面向 LLM/AI 助手）

> 本文档专为 LLM/AI 代码助手后续自动化维护、升级、重构、修复本目录下代码时使用。内容聚焦于技术细节、代码风格、设计约定、已知限制与易错点，帮助 LLM 理解"为什么这样写""不能怎么改"，避免常见误区。

---

## 一、文档适用范围
- 仅适用于 `frontend/src/views/business/template/` 及其子组件、工具文件。
- 主要面向 LLM/AI 代码助手，不面向业务开发者或最终用户。
- 关注点为技术实现、数据流、组件交互、文件处理、扩展与维护。

## 二、整体架构与技术选型
- 前端框架：Vue3 + Ant Design Vue
- Excel 在线编辑：采用 [UniverJS](https://univer.work/)
- Excel 文件解析/导出：采用 [ExcelJS](https://github.com/exceljs/exceljs)
- 主要数据流：本地 xlsx 文件 → ExcelJS 解析 → 转为 UniverJS 数据结构 → UniverJS 编辑 → 导出时再转回 xlsx
- 组件间通信：以 props、ref、事件为主，避免全局状态

## 三、目录结构与文件分工
```
frontend/src/views/business/template/
├── index.vue                # 页面主组件，组织表单、表格、弹窗、数据流
├── readme.md                # 本技术维护说明
└── components/
    ├── form/
    │   └── index.vue        # 弹窗表单，集成 Excel 在线编辑器
    └── excel/
        ├── index.vue        # Excel 在线编辑器核心，封装 UniverJS
        ├── importExcelToUniverJS.js         # xlsx → UniverJS 数据结构转换
        ├── exportUniverSheetWithExcelJS.js  # UniverJS → xlsx 导出
        ├── excelStyleUtils.js               # Excel 样式处理工具
        ├── API_NAMING_GUIDE.md              # 命名规范
        ├── CUSTOM_CONTEXT_MENU_GUIDE.md      # 右键菜单扩展指南
        ├── docs.md                          # 详细开发文档
        ├── prompt.md                        # 交互提示
        └── 单位.md                           # 单位处理说明
```

## 四、代码风格与设计约定
- 组件通信优先使用 props、ref、事件，避免全局依赖
- 统一使用 `reactive`/`ref` 管理响应式数据，避免直接操作 DOM
- 异步操作需加 loading 状态与异常捕获，统一用 SmartLoading/message/Modal
- 命名风格：驼峰式为主，常量全大写
- 组件拆分粒度：主页面/弹窗/编辑器/工具分离，便于维护

## 五、核心技术细节
### 1. UniverJS 在线编辑器
- 仅支持 json 数据结构初始化与渲染，不直接操作 xlsx
- 通过 `initJson` prop 传递初始数据，编辑后通过 exposed API 获取 workbook 实例
- 组件卸载时需主动 dispose，避免内存泄漏
- 支持动态切换数据，需 watch props 变化
- 仅暴露必要 API，避免父组件直接操作内部 DOM

### 2. ExcelJS 文件处理
- 仅在导入/导出时用到 ExcelJS，编辑过程全程用 UniverJS
- `importExcelToUniverJS.js` 负责 xlsx → UniverJS 数据结构，需处理样式、图片、合并单元格等
- `exportUniverSheetWithExcelJS.js` 负责 UniverJS → xlsx，注意兼容性与数据丢失风险
- 样式处理复杂，部分高级样式/公式可能丢失，需在文档和代码注释中明确

### 3. xlsx 文件流转
- 文件导入：input[type=file] 选择 xlsx → 解析为 json → 渲染到编辑器
- 文件导出：获取当前 workbook json → 转为 xlsx → 触发下载
- 全流程前端完成，无后端依赖

## 六、已知限制与易错点
- UniverJS/ExcelJS 版本兼容性有限，升级需谨慎，建议锁定依赖版本
- ExcelJS 对部分 Excel 特性支持不完善，复杂样式/公式/图片可能丢失
- UniverJS 仅支持 json 数据，不能直接加载 xlsx，需中转
- 编辑器实例需手动销毁，避免内存泄漏
- 组件间通信仅限 props/ref/事件，避免直接操作子组件内部数据
- 文件过大时前端解析/渲染性能有限，建议限制文件大小

## 七、扩展建议与维护注意事项
- 扩展功能时优先复用现有工具方法，避免重复造轮子
- 新增 Excel 相关功能建议先查阅 docs.md、API_NAMING_GUIDE.md
- 右键菜单、样式、单元格扩展建议参考 CUSTOM_CONTEXT_MENU_GUIDE.md、excelStyleUtils.js
- 维护时如需升级依赖，务必全量测试导入/导出/编辑全流程
- 重要变更建议补充本 readme 说明，便于 LLM/AI 后续理解

## 八、API命名与注释规范

为避免 UniverJS 与 ExcelJS 两套 API 混淆，提升代码可维护性，需严格遵循如下命名与注释规范：

### 1. 变量命名约定
- **UniverJS 相关变量**：统一使用 `univer` 前缀，如 `univerAPI`、`univerWorkbook`、`univerRange`。
- **ExcelJS 相关变量**：统一使用 `excel` 前缀，如 `excelWorkbook`、`excelWorksheet`、`excelCell`。
- 变量命名应能一眼区分所用库，避免 `workbook`、`cell` 等无前缀命名。

### 2. 注释标注层级
- **函数级注释**：明确参数类型、数据流向，标明所用库。
- **代码块注释**：重要逻辑前注明"UniverJS API"或"ExcelJS API"。
- **操作级注释**：具体 API 操作前标注库来源。

**示例：**
```js
// UniverJS API - 获取活动工作簿
const univerWorkbook = univerAPI.getActiveWorkbook();
// ExcelJS API - 创建工作簿
const excelWorkbook = new ExcelJS.Workbook();
```

### 3. 文件级别规范
- `excelStyleUtils.js`：仅处理 ExcelJS 样式，参数统一 `excel` 前缀，注释标明"ExcelJS API"。
- `exportUniverSheetWithExcelJS.js`：源数据用 `univer` 前缀，目标数据用 `excel` 前缀，注释标明数据流向。
- 组件/工具文件顶部应简要说明用途及所依赖库。

### 4. 关键API区别对照
| 操作类型   | UniverJS API                  | ExcelJS API                        | 说明           |
|------------|-------------------------------|-------------------------------------|----------------|
| 创建工作簿 | univerAPI.createWorkbook()    | new ExcelJS.Workbook()              | 创建方式不同   |
| 获取单元格 | univerSheet.getRange('A1')    | excelWorksheet.getCell('A1')        | 方法名不同     |
| 设置值     | univerRange.setValues(data)   | excelCell.value = data              | 批量 vs 单个   |
| 设置样式   | univerRange.setFontWeight()   | excelCell.font = { bold: true }     | 方法 vs 属性   |
| 合并单元格 | univerRange.merge()           | excelWorksheet.mergeCells('A1:B2')  | 对象 vs 方法   |

### 5. 最佳实践
- **数据流向清晰**：变量前缀体现数据来源与目标，转换过程注释清楚。
- **错误处理**：每次调用外部库 API 时加 try-catch，错误日志注明库名。
- **类型检查**：操作前判断对象类型，避免 API 调用混淆。

**示例：**
```js
// 数据流向清晰
function convertUniverToExcel(univerCellData) {
    const univerValue = univerCellData.v;
    const excelCell = excelWorksheet.getCell('A1');
    excelCell.value = univerValue;
}

// 错误处理
try {
    const univerData = univerSheet.getData();
} catch (e) {
    console.error('UniverJS数据获取失败:', e);
}

// 类型检查
if (excelCell instanceof ExcelJS.Cell) {
    // ExcelJS单元格操作
}
```

### 6. 维护建议
- 新增功能时严格遵循命名与注释规范，明确区分所用库
- 代码审查时重点检查变量前缀与注释准确性
- 调试时根据变量前缀快速定位问题归属
- 文档和代码示例需同步更新，保持一致性

## 九、自定义右键菜单实现规范

UniverJS 支持通过 Facade API 实现自定义右键菜单，推荐如下技术实现与维护方式：

### 1. 技术架构与分组
- 右键菜单位置（ContextMenuPosition）：如 mainArea、colHeader、rowHeader、footerTabs、footerMenu
- 菜单分组（ContextMenuGroup）：如 quick、format、layout、data、others

### 2. 推荐实现方式（Facade API）
- 使用 univerAPI.createMenu 创建菜单项，设置 id、title、icon、action、order 等属性
- 通过 appendTo('contextMenu.others') 或指定位置将菜单添加到右键菜单系统
- 支持子菜单（createSubmenu）、分隔符、图标、顺序控制

**示例：**
```js
// 创建单个菜单项
const customMenu = univerAPI.createMenu({
    id: 'custom-menu-id',
    title: '自定义菜单',
    tooltip: '菜单提示',
    icon: 'IconName',
    action: () => { /* 处理逻辑 */ },
    order: 1
});
customMenu.appendTo('contextMenu.others');

// 创建子菜单
const subMenu1 = univerAPI.createMenu({ id: 'sub1', title: '子菜单1', action: () => {} });
const mainSubmenu = univerAPI.createSubmenu({ id: 'main-sub', title: '主菜单' });
mainSubmenu.addSubmenu(subMenu1).appendTo('contextMenu.others');
```

### 3. 支持的功能特性
- 单个菜单项、子菜单、多级菜单结构
- 菜单分组与位置控制
- 图标、顺序、分隔符、工具提示
- 免费版本 Facade API 全部可用，无需 Pro

### 4. 注意事项
- 菜单需在 UniverJS 初始化完成后添加
- 菜单 action 内避免耗时操作，注意错误处理
- 菜单标题、提示需清晰，提升用户体验
- 兼容性需在主流浏览器下测试

### 5. 扩展建议
- 支持动态菜单（根据选区/上下文显示）
- 权限控制、国际化、快捷键、菜单状态（启用/禁用）
- 业务扩展建议优先通过 Facade API 实现，避免直接操作底层 DOM

**总结：**
自定义右键菜单推荐通过 UniverJS Facade API 实现，结构清晰、易于维护和扩展，适合 LLM/AI 自动化理解和二次开发。

## 十、Univer 导出 Excel 技术细节与实现要点

本节总结基于 UniverJS + ExcelJS 实现前端 Excel 导出功能的关键技术细节，便于 LLM/AI 维护和扩展。

### 1. 需求与目标
- 目标：将 UniverJS 实例中的表格数据、样式、图片等完整导出为标准 .xlsx 文件，纯前端实现。
- 技术栈：UniverJS（数据源）、ExcelJS（文件生成）、现代浏览器 JS 环境。

### 2. 主导出函数接口
```js
/**
 * @async
 * @function exportUniverSheetWithExcelJS
 * @description 将当前 Univer 工作簿内容导出为 .xlsx 文件。
 * @param {object} univerInstance - Univer 实例对象。
 * @param {string} [outputFileName='UniverExport.xlsx'] - 导出的文件名。
 * @returns {Promise<void>} - 文件生成并下载后 resolve，出错时 reject。
 */
async function exportUniverSheetWithExcelJS(univerInstance, outputFileName = 'UniverExport.xlsx') { /* ... */ }
```
- 数据源：通过 Univer 实例 API 获取 workbook、worksheets、cellData、样式、合并、图片等。

### 3. 主要功能点
- 支持多工作表导出，保留名称与顺序
- 支持文本、数字、布尔、日期/时间、公式及其结果
- 支持合并单元格、列宽、行高
- 支持字体、字号、颜色、粗体、斜体、删除线、下划线、背景色、对齐、自动换行、边框
- 支持单元格图片导出（位置、尺寸、格式）

### 4. 关键实现注意事项
- **坐标系统**：Univer 为 0-based，ExcelJS 为 1-based，转换需注意
- **单位处理**：列宽/行高需用精确比例转换（如 8.2px/char，0.75 行高）
- **颜色格式**：需处理两库间颜色格式差异
- **日期处理**：确保日期/时间格式兼容
- **图片导出**：需适配 UniverJS 图片数据结构，支持 BASE64/URL，正确设置位置与尺寸
- **错误处理**：对关键对象和数据做有效性检查，异常需有明确提示

### 5. 图片与尺寸处理优化
- 图片导出需检测 cellData.p.drawings，适配 drawingType/source/transform 等字段
- 支持 PNG/JPEG/GIF，保持图片在 Excel 中的正确位置和尺寸
- 尺寸转换采用测量数据优化算法，确保导出后与页面显示一致
```js
function convertWithMeasuredData(pixelValue, type, isDefault = false) {
    if (type === 'rowHeight') return pixelValue * 0.75;
    if (type === 'columnWidth') return pixelValue / 8.2;
}
```

### 6. 维护与扩展建议
- 依赖接口和数据结构如有变动，需同步调整导出逻辑
- 图片、样式等高级特性如需扩展，优先查阅 UniverJS/ExcelJS 官方文档
- 重要修复和优化建议在本节补充说明，便于 LLM/AI 后续理解

## 十一、图片导入与开发提示

- ExcelJS 支持的图片数据结构示例：
```json
{"image_0kx6l_1750672834506":{
  "src":"data:image/png;base64,...",
  "fromCol":0,"fromColOff":0,"fromRow":0,"fromRowOff":0,
  "toCol":1,"toColOff":"60.93","toRow":2,"toRowOff":"2.93",
  "originWidth":"131.93","originHeight":"48.93",
  ...
}}
```
- 处理图片导入时，建议参考 exceljs 源码和 Univer 源码，确保图片格式、位置、尺寸等字段正确映射。
- 开发与测试应在真实业务场景下完成，保持代码和界面简洁、稳定，避免污染最终产品。

## 十二、单位与尺寸转换原理

### 1. 单位体系
- **EMU (English Metric Unit)**：Excel/OpenXML 的底层物理单位，1英寸=914400 EMU，1pt=12700 EMU。
- **pt (Point/磅)**：打印/字体常用单位，1pt=1/72英寸，Excel 行高、字体大小常用。
- **px (Pixel/像素)**：Web 渲染最小单位，1英寸≈96px（标准DPI）。UniverJS 内部主要用 px。
- **char (字符宽度)**：Excel 列宽专用单位，定义为"0-9数字中最宽字符的像素宽度"，随字体变化。

### 2. 关键换算公式
- **行高**
  - pt → px：`px = pt * (96 / 72)`
  - px → pt：`pt = px * (72 / 96)`
- **列宽**
  - px → char：`char = px / 8.2`（经验值，随字体略有变化）
  - char → px：`px = char * 8.2`
- **EMU 与 px**
  - px → EMU：`EMU = px * 9525`
  - EMU → px：`px = EMU / 9525`

### 3. 图片尺寸与定位
- Excel/OpenXML 用 EMU 存储图片锚点，UniverJS 用 px 存储图片位置和大小。
- 图片导入/导出时需遍历锚点、列宽、行高，精确换算位置和尺寸。

### 4. 常见陷阱与建议
- 列宽单位最易混淆，务必用 EMU 作为中间桥梁，避免直接 px↔char 换算。
- 字体、DPI 变化会影响 px/char 换算精度，建议用测量数据校准。
- 图片锚点需遍历所有跨越的行列，累加宽高，避免定位偏差。
- 任何单位换算建议在代码和注释中明确标注，便于 LLM/AI 理解和维护。
