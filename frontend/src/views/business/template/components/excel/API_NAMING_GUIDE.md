# Excel导出功能 API命名规范指南

## 概述

为了避免UniverJS和ExcelJS两个库的API混淆，提高代码可维护性，本项目采用了严格的变量命名规范和注释标注。

## 变量命名规范

### 1. UniverJS相关变量
所有UniverJS相关的变量使用`univer`前缀：

```javascript
// ✅ 正确的UniverJS变量命名
let univerAPI = null;                    // UniverJS API实例
let univerWorkbook = null;               // UniverJS工作簿
const univerActiveSheet = univerAPI.getActiveWorkbook().getActiveSheet();
const univerRange = univerActiveSheet.getRange('A1:B2');
const univerCellData = { v: 'value', t: 1, f: '=SUM(A1:A5)' };

// ❌ 错误的命名（容易混淆）
let api = null;
let workbook = null;
let activeSheet = null;
```

### 2. ExcelJS相关变量
所有ExcelJS相关的变量使用`excel`前缀：

```javascript
// ✅ 正确的ExcelJS变量命名
const excelWorkbook = new ExcelJS.Workbook();
const excelWorksheet = excelWorkbook.addWorksheet('Sheet1');
const excelCell = excelWorksheet.getCell('A1');
const excelRow = excelWorksheet.getRow(1);
const excelColumn = excelWorksheet.getColumn('A');

// ❌ 错误的命名（容易混淆）
const workbook = new ExcelJS.Workbook();
const worksheet = workbook.addWorksheet('Sheet1');
const cell = worksheet.getCell('A1');
```

## 注释标注规范

### 1. 函数级别注释
在函数文档中明确标注参数类型：

```javascript
/**
 * 设置ExcelJS单元格值 - UniverJS到ExcelJS数据转换
 * @param {ExcelJS.Cell} excelCell - ExcelJS 单元格对象
 * @param {object} univerCellData - UniverJS 单元格数据 {v: 值, t: CellValueType, f: 公式}
 */
function setCellValue(excelCell, univerCellData) {
    // 函数实现
}
```

### 2. 代码块级别注释
在重要代码块添加库标识注释：

```javascript
// UniverJS API - 获取活动工作簿
const univerActiveWorkbook = univerAPI.getActiveWorkbook();

// ExcelJS API - 创建工作簿
const excelWorkbook = new ExcelJS.Workbook();

// UniverJS到ExcelJS数据转换
await processCellData(excelWorksheet, univerSheetData, univerWorkbookSnapshot);
```

### 3. 操作级别注释
在具体操作前标注使用的库：

```javascript
// UniverJS API - 设置单元格值
univerRange.setValues(data);

// ExcelJS API - 设置单元格样式
excelCell.font = { bold: true };

// UniverJS数据提取
const univerValue = univerCellData.v;
```

## 文件级别规范

### 1. excelStyleUtils.js
- **用途**：专门用于ExcelJS对象的样式设置
- **变量命名**：所有参数使用`excel`前缀
- **注释标注**：明确标注"ExcelJS API"

```javascript
/**
 * ExcelJS样式工具模块
 * 专门用于ExcelJS库的单元格样式设置函数
 * ⚠️ 注意：本模块仅适用于ExcelJS对象，不适用于UniverJS对象
 */
export function setCellAlignment(excelCell, alignment) {
    // ExcelJS API - 设置单元格对齐属性
    excelCell.alignment = alignment;
}
```

### 2. exportUniverSheetWithExcelJS.js
- **用途**：UniverJS到ExcelJS的数据转换
- **变量命名**：源数据使用`univer`前缀，目标数据使用`excel`前缀
- **注释标注**：明确标注数据流向

```javascript
/**
 * UniverJS 到 ExcelJS 数据转换导出工具模块
 * 库使用说明：
 * - UniverJS: 用于读取源数据（工作簿、工作表、单元格数据）
 * - ExcelJS: 用于创建目标Excel文件（工作簿、工作表、单元格样式）
 */
export async function exportUniverSheetWithExcelJS(univerAPI, outputFileName) {
    // UniverJS API - 获取活动工作簿
    const univerWorkbook = univerAPI.getActiveWorkbook();
    
    // ExcelJS API - 创建工作簿
    const excelWorkbook = new ExcelJS.Workbook();
    
    // UniverJS到ExcelJS数据转换
    await processWorksheet(excelWorkbook, univerSheetData, univerSheetId, univerWorkbookSnapshot);
}
```

### 3. index.vue
- **用途**：主要使用UniverJS API创建示例数据
- **变量命名**：主要使用`univer`前缀
- **注释标注**：明确标注"UniverJS API"

```javascript
// UniverJS API实例，用于在组件的不同方法之间共享
let univerAPI = null;
let univerWorkbook = null;

// UniverJS API - 获取当前活动的工作簿和工作表
const univerActiveWorkbook = univerAPI.getActiveWorkbook();
const univerActiveSheet = univerActiveWorkbook.getActiveSheet();

// UniverJS API - 设置单元格数据
const univerRange = univerActiveSheet.getRange('A1:B2');
univerRange.setValues(data);
```

## 关键区别对照表

| 操作类型 | UniverJS API | ExcelJS API | 说明 |
|---------|-------------|-------------|-----|
| 创建工作簿 | `univerAPI.createWorkbook()` | `new ExcelJS.Workbook()` | 不同的创建方式 |
| 获取单元格 | `univerSheet.getRange('A1')` | `excelWorksheet.getCell('A1')` | 方法名不同 |
| 设置值 | `univerRange.setValues(data)` | `excelCell.value = data` | 批量vs单个 |
| 设置样式 | `univerRange.setFontWeight('bold')` | `excelCell.font = { bold: true }` | 方法vs属性 |
| 合并单元格 | `univerRange.merge()` | `excelWorksheet.mergeCells('A1:B2')` | 对象vs方法 |

## 最佳实践

### 1. 数据流向清晰
```javascript
// ✅ 清晰的数据流向
function convertUniverToExcel(univerCellData) {
    // UniverJS数据提取
    const univerValue = univerCellData.v;
    const univerType = univerCellData.t;
    
    // ExcelJS对象创建
    const excelCell = excelWorksheet.getCell('A1');
    
    // 数据转换
    excelCell.value = univerValue;
}
```

### 2. 错误处理标注
```javascript
try {
    // UniverJS API调用
    const univerData = univerSheet.getData();
} catch (error) {
    console.error('UniverJS数据获取失败:', error);
}

try {
    // ExcelJS API调用
    excelWorkbook.xlsx.writeBuffer();
} catch (error) {
    console.error('ExcelJS文件生成失败:', error);
}
```

### 3. 类型检查
```javascript
// ✅ 明确的类型检查
if (excelCell instanceof ExcelJS.Cell) {
    // ExcelJS单元格操作
}

if (univerRange && typeof univerRange.setValues === 'function') {
    // UniverJS范围操作
}
```

## 维护建议

1. **新增功能时**：严格遵循命名规范，明确标注使用的库
2. **代码审查时**：检查变量命名是否符合规范，注释是否清晰
3. **调试时**：根据变量前缀快速定位问题所在的库
4. **文档更新时**：保持API使用示例的准确性

通过这套命名规范，可以有效避免两个库的API混淆，提高代码的可读性和可维护性。
