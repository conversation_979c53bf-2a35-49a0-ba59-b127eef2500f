# UniverJS 自定义右键菜单实现指南

## 概述

本文档详细说明了如何在 UniverJS Excel 组件中实现自定义右键菜单功能。基于对 UniverJS 源码的深入分析，我们确认免费版本完全支持自定义右键菜单功能。

## 技术架构

### 1. UniverJS 右键菜单系统

UniverJS 的右键菜单系统基于以下核心概念：

- **ContextMenuPosition**: 右键菜单位置
  - `contextMenu.mainArea`: 主区域
  - `contextMenu.colHeader`: 列头
  - `contextMenu.rowHeader`: 行头
  - `contextMenu.footerTabs`: 底部标签
  - `contextMenu.footerMenu`: 底部菜单

- **ContextMenuGroup**: 菜单分组
  - `contextMenu.quick`: 快速操作（图标形式）
  - `contextMenu.format`: 格式化
  - `contextMenu.layout`: 布局
  - `contextMenu.data`: 数据
  - `contextMenu.others`: 其他

### 2. 实现方法

#### 方法一：Facade API（推荐）

使用 UniverJS 提供的 Facade API，这是最简单和推荐的方法：

```javascript
// 创建单个菜单项
const customMenu = univerAPI.createMenu({
    id: 'custom-menu-id',
    title: '自定义菜单',
    tooltip: '菜单提示',
    icon: 'IconName', // 可选
    action: () => {
        // 菜单点击处理逻辑
        console.log('自定义菜单被点击');
    },
    order: 1 // 可选，控制显示顺序
});

// 添加到右键菜单
customMenu.appendTo('contextMenu.others');
```

#### 方法二：子菜单实现

```javascript
// 创建子菜单项
const subMenu1 = univerAPI.createMenu({
    id: 'sub-menu-1',
    title: '子菜单1',
    action: () => { /* 处理逻辑 */ }
});

const subMenu2 = univerAPI.createMenu({
    id: 'sub-menu-2',
    title: '子菜单2',
    action: () => { /* 处理逻辑 */ }
});

// 创建主菜单
const mainSubmenu = univerAPI.createSubmenu({
    id: 'main-submenu',
    title: '主菜单',
    tooltip: '包含子菜单的主菜单',
    icon: 'IconName'
});

// 添加子菜单
mainSubmenu
    .addSubmenu(subMenu1)
    .addSeparator()
    .addSubmenu(subMenu2)
    .appendTo('contextMenu.others');
```

#### 方法三：指定位置显示

```javascript
// 仅在主区域显示的菜单
customMenu.appendTo(['contextMenu.mainArea', 'contextMenu.others']);

// 在所有位置显示的菜单
customMenu.appendTo('contextMenu.others');
```

## 实际应用示例

### 1. 报价组功能

我们实现了三个实用的自定义右键菜单项：

1. **添加报价组**: 在当前位置插入包含标题、数据行、小计行的报价组
2. **报价工具**: 包含多个子菜单的工具集
3. **格式化报价表**: 自动格式化选中区域为报价表样式

### 2. 核心实现代码

```javascript
const initCustomContextMenu = async () => {
    if (!univerAPI) return;

    // 1. 单个菜单项
    const customMenu1 = univerAPI.createMenu({
        id: 'custom-quote-group',
        title: '添加报价组',
        tooltip: '在当前位置添加一个报价组',
        icon: 'AddIcon',
        action: () => handleAddQuoteGroup(),
        order: 1
    });
    customMenu1.appendTo('contextMenu.others');

    // 2. 子菜单
    const subMenu1 = univerAPI.createMenu({
        id: 'custom-insert-3-rows',
        title: '插入3行（标题+数据+小计）',
        action: () => handleInsert3Rows()
    });

    const customSubmenu = univerAPI.createSubmenu({
        id: 'custom-quote-tools',
        title: '报价工具',
        tooltip: '报价相关的快捷操作',
        icon: 'ToolsIcon',
        order: 2
    });

    customSubmenu
        .addSubmenu(subMenu1)
        .appendTo('contextMenu.others');

    // 3. 特定位置菜单
    const customMenu2 = univerAPI.createMenu({
        id: 'custom-format-quote',
        title: '格式化报价表',
        action: () => handleFormatQuoteTable()
    });
    customMenu2.appendTo(['contextMenu.mainArea', 'contextMenu.others']);
};
```

## 功能特性

### ✅ 支持的功能

1. **单个菜单项**: 简单的点击操作
2. **子菜单**: 支持多级菜单结构
3. **菜单分组**: 可以添加到不同的分组
4. **位置控制**: 可以指定在哪些位置显示
5. **图标支持**: 支持 UniverJS 内置图标
6. **排序控制**: 通过 order 属性控制显示顺序
7. **分隔符**: 支持在子菜单中添加分隔符
8. **工具提示**: 支持鼠标悬停提示

### ✅ 免费版本确认

通过源码分析确认：
- 右键菜单功能属于核心 UI 功能
- Facade API 完全免费可用
- 不依赖任何 Pro 版本功能
- 支持所有基础的菜单操作

## 使用方法

### 1. 初始化

在 UniverJS 初始化完成后调用：

```javascript
onMounted(async () => {
    // UniverJS 初始化...
    
    // 初始化自定义右键菜单
    await initCustomContextMenu();
});
```

### 2. 测试

1. 运行项目
2. 点击"🎯 测试右键菜单"按钮
3. 在红色测试区域右键点击
4. 查看"其他"分组中的自定义菜单项

### 3. 自定义

根据业务需求修改菜单项：
- 修改菜单标题和图标
- 调整菜单处理逻辑
- 添加新的菜单项
- 调整菜单分组和位置

## 注意事项

1. **API 可用性**: 确保在 UniverJS 完全初始化后再添加菜单
2. **错误处理**: 添加适当的错误处理机制
3. **性能考虑**: 避免在菜单处理函数中执行耗时操作
4. **用户体验**: 提供清晰的菜单标题和工具提示
5. **兼容性**: 测试在不同浏览器中的兼容性

## 扩展建议

1. **动态菜单**: 根据当前选中内容动态显示/隐藏菜单项
2. **权限控制**: 根据用户权限控制菜单可见性
3. **国际化**: 支持多语言菜单标题
4. **快捷键**: 为常用菜单项添加快捷键支持
5. **菜单状态**: 根据上下文显示菜单项的启用/禁用状态

## 总结

UniverJS 的自定义右键菜单功能强大且易用，免费版本提供了完整的 Facade API 支持。通过本实现方案，可以轻松为 Excel 组件添加业务相关的快捷操作，大大提升用户体验。
