# 需求：Univer 数据导出至 Excel (使用 ExcelJS)

## prompt

请根据需求文档实现Excel导出功能。具体要求如下：

1. **分阶段实现**：将导出功能拆分为多个独立的阶段，每个阶段实现一个完整的子功能模块

2. **实现流程**：
    - 每完成一个阶段后，我会进行手动功能测试
    - 你无需编写单元测试代码，我会通过实际使用来验证功能
    - 功能测试通过后，我会在需求文档中标记该功能为"已实现"
    - 已标记为"已实现"的功能无需重复开发
    - 不要单独为某个功能创建单独的测试代码或文档，而应该将修复后的功能集成到现有的通用测试代码中通过现有的完整功能测试来验证

3. **技术要求**：
    - 使用UniverJS免费版本实现Excel导出功能
    - 采用纯前端方案，避免使用UniverJS Pro功能
    - 如需Excel文件处理，使用ExcelJS库
    - 保持中文注释和错误提示
    - 可参考源码位置：univer/packages/sheets/src/facade
    - 可参考源码位置：exceljs-master

4. **开始前准备**：
    - 首先查看当前需求文档，了解导出功能的具体要求
    - 检查已标记为"已实现"的功能，避免重复开发
    - 制定详细的分阶段实现计划

请先查看需求文档，然后提供分阶段实现计划。

## 1. 项目背景与目标

*   **背景：** 用户在其 Vue 应用中使用了 Univer (一个下一代文档、电子表格和演示文稿的开源协作套件)，目前需要将 Univer 电子表格中的数据和尽可能多的格式特性导出为标准的 `.xlsx` Excel 文件。
*   **核心目标：** 开发一个独立的 JavaScript 工具函数/模块，该模块能够接收一个 Univer 实例，并使用 `exceljs` 库将其内容（包括数据、选定的样式和结构，**图片导出是必要功能**）转换为一个可下载的 `.xlsx` 文件。
*   **主要技术栈：**
    *   **数据源：** Univer (通过 Univer 实例 API 访问)
    *   **Excel生成库：** `exceljs`
    *   **运行环境：** 现代浏览器 JavaScript 环境

## 2. 功能需求列表

以下是本次开发必须实现的功能点：

### 核心数据与结构 (P0)：

*   **1.** 导出所有工作表，并保留其原始名称和顺序。
*   **2.** 导出单元格的文本值。
*   **3.** 导出单元格的数字值。
*   **4.** 导出单元格的布尔值。
*   **5.** 导出单元格的日期/时间值。
*   **6.** 导出单元格内的公式。
*   **7.** 导出公式的计算结果。
*   **8.** 支持合并单元格。
*   **9.** 支持设置列宽。
*   **10.** 支持设置行高。

### 重要样式 (P1)：

*   **11.** 导出字体名称。
*   **12.** 导出字号。
*   **13.** 导出文字颜色。
*   **14.** 导出粗体样式。
*   **15.** 导出斜体样式。
*   **16.** 导出删除线样式。
*   **17.** 导出下划线样式。
*   **18.** 导出单元格背景色。
*   **19.** 导出水平对齐方式。
*   **20.** 导出垂直对齐方式。
*   **21.** 导出自动换行设置。
*   **23.** 导出单元格的独立边框（上、下、左、右）。
*   **24.** 完整支持单元格的全部边框样式。

### 高级特性 (P2)：

*   **25.** **图片导出 (必要功能)**：支持将工作表中的图片及其位置和尺寸导出。

## 3. 模块/函数接口设计

*   **主导出函数：**
    ```javascript
    /**
     * @async
     * @function exportUniverSheetWithExcelJS
     * @description 将当前 Univer 工作簿内容导出为 .xlsx 文件。
     * @param {object} univerInstance - Univer 实例对象。
     * @param {string} [outputFileName='UniverExport.xlsx'] - 导出的文件名。
     * @returns {Promise<void>} - 在文件生成并尝试下载后 resolve，或在出错时 reject。
     */
    async function exportUniverSheetWithExcelJS(univerInstance, outputFileName = 'UniverExport.xlsx') {
        // 实现逻辑
    }
    ```
*   **预期的数据源：**
    *   通过 Univer 实例 API 获取当前工作簿 (`Workbook`)。
    *   通过工作簿 API 获取所有工作表 (`Worksheet`) 实例的列表。
    *   对于每个工作表，需要通过其 API 获取以下信息：
        *   工作表名称。
        *   单元格数据矩阵 (包括值、公式等)。
        *   单元格样式信息 (通过样式ID关联到具体的字体、颜色、对齐等定义)。
        *   列宽配置。
        *   行高配置。
        *   合并单元格的范围。
        *   工作表中的图片等绘图对象。

## 4. 关键实现点与注意事项

*   **坐标系统：** 处理 Univer (0-based) 与 `exceljs` (1-based) 之间的坐标差异。
*   **单位处理：** 需要处理 Univer 和 Excel 之间关于列宽和行高所使用的单位差异。
*   **颜色格式：** 需要处理不同库之间颜色表示法的转换。
*   **日期处理：** 确保 Univer 中的日期/时间数据能被正确转换为 Excel 可识别的格式。
*   **图片导出：**
    *   **数据源探查：** 关键任务是研究 Univer 的 API，确定如何访问图片数据（如 Base64 或 URL）、位置和尺寸信息。
    *   **数据获取：** 如果图片源是 URL，需要实现异步获取图片数据的逻辑。
    *   **定位与尺寸：** 需将 Univer 中的图片定位和尺寸信息转换为 `exceljs` 的 `addImage` 方法所需的参数。
*   **库的引入：** 确保 `exceljs` 在项目中可用。
*   **错误处理：** 增加对 `univerInstance` 和 `exceljs` 等关键对象的有效性检查。

## 5. 开发步骤与迭代计划

1.  **环境搭建与主函数框架。** ✅ **已实现**
2.  **核心数据与结构实现 (P0功能)。** ✅ **已实现**
3.  **基础样式实现 (P1功能，除边框)。** ✅ **已实现**
4.  **边框样式实现。** ✅ **已实现**
5.  **图片导出重点攻关 (P2功能)。** ✅ **已实现并修复**

## 6. 图片导出功能修复说明

### 修复内容：
- **API弃用问题修复**：将`getSnapshot()`方法替换为`save()`方法，消除弃用警告
- **图片检测增强**：添加多种图片检测方法，包括浮动图片、单元格内嵌图片、工作表级图片等
- **单元格绘制对象检测**：根据实际数据结构修复图片检测逻辑，正确识别cellData.p.drawings中的图片
- **数据结构适配**：适配UniverJS实际的图片数据结构（drawingType: 0, source, imageSourceType等）
- **错误处理优化**：改进图片处理的错误处理和用户反馈机制
- **格式兼容性**：添加图片格式验证，确保ExcelJS支持的格式正确处理
- **单元格尺寸修复**：恢复标准的行高列宽转换比例，修复尺寸显示异常
- **调试信息优化**：减少冗余日志，提供更清晰的处理状态反馈

### 技术实现：
- 使用UniverJS免费版本的图片处理API
- 结合ExcelJS实现纯前端图片导出功能
- 支持PNG/JPEG/GIF等常见图片格式
- 保持图片在导出Excel中的正确位置、尺寸和比例
- 集成到现有测试代码中，通过完整功能测试验证

### 数据结构适配：
根据实际的UniverJS数据结构进行了精确适配：
```javascript
cellData[row][col] = {
  p: {
    drawings: {
      "drawingId": {
        drawingType: 0,  // 0表示图片类型
        source: "data:image/png;base64,iVBORw0KGgo...",
        imageSourceType: "BASE64",
        docTransform: {
          size: { width: 29, height: 22.01279317697228 }
        },
        transform: { width: 29, height: 22.01279317697228 }
      }
    },
    drawingsOrder: ["drawingId"]
  }
}
```

### 验证方式：
通过现有的"📊 完整功能测试"按钮创建包含图片的测试数据，然后使用"💾 导出 Excel"按钮验证图片导出功能。修复后应能正确检测和导出单元格中的图片。

## 7. 单元格尺寸精确转换优化

### 优化背景：
基于实际测量数据发现Excel导出存在尺寸差异：
- **默认尺寸测试**：页面显示89x25px，Excel导出81x29px（宽度-8px，高度+4px）
- **自定义尺寸测试**：页面显示301x202px，Excel导出274x202px（宽度-27px，高度0px）

### 优化内容：
- **精确转换算法**：基于测量数据创建`convertWithMeasuredData()`函数
- **行高转换优化**：移除默认行高的过度调整，使用标准0.75转换比例
- **列宽转换优化**：使用8.2px/char的精确比例（从8.8优化而来）
- **空单元格优化**：确保空单元格的尺寸转换精度，消除字体渲染影响

### 技术实现：
```javascript
// 基于测量数据的精确转换
function convertWithMeasuredData(pixelValue, type, isDefault = false) {
    if (type === 'rowHeight') {
        return pixelValue * 0.75; // 标准转换，目标：25px->25px
    } else if (type === 'columnWidth') {
        return pixelValue / 8.2; // 优化比例，目标：89px->89px
    }
}
```

### 预期效果：
- **默认行高**：25px → 25px（0差异）
- **默认列宽**：89px → 89px（0差异）
- **自定义行高**：保持精确转换
- **自定义列宽**：301px → 减少差异至约10px以内

