/**
 * ExcelJS样式工具模块
 * 专门用于ExcelJS库的单元格样式设置函数
 *
 * ⚠️ 注意：本模块仅适用于ExcelJS对象，不适用于UniverJS对象
 *
 * <AUTHOR>
 * @version 2.0.0
 * @date 2024-06-29
 */

/**
 * 设置ExcelJS单元格文本对齐方式
 * @param {ExcelJS.Cell} excelCell - ExcelJS单元格对象
 * @param {Object} alignment - 对齐配置对象
 * @param {string} alignment.horizontal - 水平对齐: 'left'|'center'|'right'|'fill'|'justify'|'centerContinuous'|'distributed'
 * @param {string} alignment.vertical - 垂直对齐: 'top'|'middle'|'bottom'|'distributed'|'justify'
 * @param {number} alignment.indent - 缩进级别 (0-250)
 * @param {boolean} alignment.wrapText - 是否自动换行
 * @param {number} alignment.textRotation - 文本旋转角度 (-90 到 90)
 *
 * @example
 * // ExcelJS API - 设置居中对齐
 * setCellAlignment(excelCell, { horizontal: 'center', vertical: 'middle' });
 *
 * // ExcelJS API - 设置左对齐并自动换行
 * setCellAlignment(excelCell, { horizontal: 'left', vertical: 'top', wrapText: true });
 */
export function setCellAlignment(excelCell, alignment) {
    if (!excelCell || !alignment) return;

    try {
        // ExcelJS API - 设置单元格对齐属性
        excelCell.alignment = {
            horizontal: alignment.horizontal || 'left',
            vertical: alignment.vertical || 'top',
            indent: alignment.indent || 0,
            wrapText: alignment.wrapText || false,
            textRotation: alignment.textRotation || 0
        };
    } catch (error) {
        console.warn('ExcelJS单元格对齐设置失败:', error);
    }
}

/**
 * 设置ExcelJS单元格边框样式
 * @param {ExcelJS.Cell} excelCell - ExcelJS单元格对象
 * @param {Object} borders - 边框配置对象
 * @param {Object} borders.top - 上边框: { style: 'thin'|'medium'|'thick'|'dotted'|'dashed'|'double', color: { argb: 'FF000000' } }
 * @param {Object} borders.left - 左边框
 * @param {Object} borders.bottom - 下边框
 * @param {Object} borders.right - 右边框
 * @param {Object} borders.diagonal - 对角线边框
 *
 * @example
 * // ExcelJS API - 设置全边框
 * setCellBorders(excelCell, {
 *   top: { style: 'thin', color: { argb: 'FF000000' } },
 *   left: { style: 'thin', color: { argb: 'FF000000' } },
 *   bottom: { style: 'thin', color: { argb: 'FF000000' } },
 *   right: { style: 'thin', color: { argb: 'FF000000' } }
 * });
 *
 * // ExcelJS API - 设置粗边框
 * setCellBorders(excelCell, {
 *   top: { style: 'thick', color: { argb: 'FFFF0000' } }
 * });
 */
export function setCellBorders(excelCell, borders) {
    if (!excelCell || !borders) return;

    try {
        // ExcelJS API - 设置单元格边框属性
        excelCell.border = {
            top: borders.top || undefined,
            left: borders.left || undefined,
            bottom: borders.bottom || undefined,
            right: borders.right || undefined,
            diagonal: borders.diagonal || undefined
        };
    } catch (error) {
        console.warn('ExcelJS单元格边框设置失败:', error);
    }
}

/**
 * 设置ExcelJS单元格字体样式
 * @param {ExcelJS.Cell} excelCell - ExcelJS单元格对象
 * @param {Object} fontStyle - 字体样式配置对象
 * @param {string} fontStyle.name - 字体名称: 'Arial'|'宋体'|'微软雅黑'|'Times New Roman'等
 * @param {number} fontStyle.size - 字体大小 (1-409)
 * @param {boolean} fontStyle.bold - 是否粗体
 * @param {boolean} fontStyle.italic - 是否斜体
 * @param {boolean} fontStyle.underline - 是否下划线: true|false|'single'|'double'
 * @param {boolean} fontStyle.strike - 是否删除线
 * @param {Object} fontStyle.color - 字体颜色: { argb: 'FFFF0000' }
 * @param {string} fontStyle.family - 字体族: 1|2|3|4
 * @param {string} fontStyle.scheme - 字体方案: 'minor'|'major'|'none'
 *
 * @example
 * // ExcelJS API - 设置粗体红色字体
 * setCellFont(excelCell, {
 *   name: '微软雅黑',
 *   size: 14,
 *   bold: true,
 *   color: { argb: 'FFFF0000' }
 * });
 *
 * // ExcelJS API - 设置斜体下划线字体
 * setCellFont(excelCell, {
 *   name: 'Arial',
 *   size: 12,
 *   italic: true,
 *   underline: true
 * });
 */
export function setCellFont(excelCell, fontStyle) {
    if (!excelCell || !fontStyle) return;

    try {
        // ExcelJS API - 设置单元格字体属性
        excelCell.font = {
            name: fontStyle.name || 'Arial',
            size: fontStyle.size || 11,
            bold: fontStyle.bold || false,
            italic: fontStyle.italic || false,
            underline: fontStyle.underline || false,
            strike: fontStyle.strike || false,
            color: fontStyle.color || { argb: 'FF000000' },
            family: fontStyle.family || 2,
            scheme: fontStyle.scheme || 'minor'
        };
    } catch (error) {
        console.warn('ExcelJS单元格字体设置失败:', error);
    }
}

/**
 * 设置ExcelJS单元格背景颜色
 * @param {ExcelJS.Cell} excelCell - ExcelJS单元格对象
 * @param {Object} background - 背景配置对象
 * @param {string} background.type - 填充类型: 'pattern'|'gradient'
 * @param {string} background.pattern - 图案类型: 'solid'|'darkGray'|'mediumGray'|'lightGray'等
 * @param {Object} background.fgColor - 前景色: { argb: 'FFFF0000' }
 * @param {Object} background.bgColor - 背景色: { argb: 'FF00FF00' }
 * @param {Object} background.gradient - 渐变配置 (当type为'gradient'时)
 *
 * @example
 * // ExcelJS API - 设置纯色背景
 * setCellBackground(excelCell, {
 *   type: 'pattern',
 *   pattern: 'solid',
 *   fgColor: { argb: 'FFFF0000' }
 * });
 *
 * // ExcelJS API - 设置渐变背景
 * setCellBackground(excelCell, {
 *   type: 'gradient',
 *   gradient: {
 *     type: 'linear',
 *     degree: 0,
 *     stops: [
 *       { position: 0, color: { argb: 'FFFF0000' } },
 *       { position: 1, color: { argb: 'FF0000FF' } }
 *     ]
 *   }
 * });
 */
export function setCellBackground(excelCell, background) {
    if (!excelCell || !background) return;

    try {
        // ExcelJS API - 设置单元格填充属性
        if (background.type === 'pattern') {
            excelCell.fill = {
                type: 'pattern',
                pattern: background.pattern || 'solid',
                fgColor: background.fgColor || { argb: 'FFFFFFFF' },
                bgColor: background.bgColor || { argb: 'FF000000' }
            };
        } else if (background.type === 'gradient') {
            excelCell.fill = {
                type: 'gradient',
                gradient: background.gradient
            };
        }
    } catch (error) {
        console.warn('ExcelJS单元格背景设置失败:', error);
    }
}

/**
 * 设置ExcelJS工作表行高和列宽
 * @param {ExcelJS.Worksheet} excelWorksheet - ExcelJS工作表对象
 * @param {Object} rowCol - 行列配置对象
 * @param {number} rowCol.row - 行号 (1-based)
 * @param {number} rowCol.col - 列号 (1-based) 或列字母 ('A', 'B', 'C'...)
 * @param {Object} dimensions - 尺寸配置对象
 * @param {number} dimensions.height - 行高 (磅值)
 * @param {number} dimensions.width - 列宽 (字符宽度)
 * @param {boolean} dimensions.hidden - 是否隐藏
 * @param {number} dimensions.outlineLevel - 大纲级别
 *
 * @example
 * // ExcelJS API - 设置第1行高度为30磅
 * setCellDimensions(excelWorksheet, { row: 1 }, { height: 30 });
 *
 * // ExcelJS API - 设置A列宽度为20字符
 * setCellDimensions(excelWorksheet, { col: 'A' }, { width: 20 });
 *
 * // ExcelJS API - 同时设置行高和列宽
 * setCellDimensions(excelWorksheet, { row: 1, col: 'A' }, { height: 25, width: 15 });
 */
export function setCellDimensions(excelWorksheet, rowCol, dimensions) {
    if (!excelWorksheet || !rowCol || !dimensions) return;

    try {
        // ExcelJS API - 设置行高
        if (rowCol.row && dimensions.height !== undefined) {
            const excelRow = excelWorksheet.getRow(rowCol.row);
            excelRow.height = dimensions.height;
            if (dimensions.hidden !== undefined) excelRow.hidden = dimensions.hidden;
            if (dimensions.outlineLevel !== undefined) excelRow.outlineLevel = dimensions.outlineLevel;
        }

        // ExcelJS API - 设置列宽
        if (rowCol.col && dimensions.width !== undefined) {
            const excelColumn = excelWorksheet.getColumn(rowCol.col);
            excelColumn.width = dimensions.width;
            if (dimensions.hidden !== undefined) excelColumn.hidden = dimensions.hidden;
            if (dimensions.outlineLevel !== undefined) excelColumn.outlineLevel = dimensions.outlineLevel;
        }
    } catch (error) {
        console.warn('ExcelJS行高列宽设置失败:', error);
    }
}

/**
 * 合并ExcelJS工作表单元格
 * @param {ExcelJS.Worksheet} excelWorksheet - ExcelJS工作表对象
 * @param {string|Object} range - 合并范围
 *   - 字符串格式: 'A1:C3'
 *   - 对象格式: { startRow: 1, startColumn: 1, endRow: 3, endColumn: 3 }
 *
 * @example
 * // ExcelJS API - 使用字符串范围合并
 * mergeCells(excelWorksheet, 'A1:C3');
 *
 * // ExcelJS API - 使用对象范围合并
 * mergeCells(excelWorksheet, {
 *   startRow: 1,
 *   startColumn: 1,
 *   endRow: 3,
 *   endColumn: 3
 * });
 */
export function mergeCells(excelWorksheet, range) {
    if (!excelWorksheet || !range) return;

    try {
        // ExcelJS API - 合并单元格
        if (typeof range === 'string') {
            excelWorksheet.mergeCells(range);
        } else if (typeof range === 'object') {
            excelWorksheet.mergeCells(range.startRow, range.startColumn, range.endRow, range.endColumn);
        }
    } catch (error) {
        console.warn('ExcelJS合并单元格失败:', error);
    }
}

/**
 * 预定义样式常量
 */
export const PRESET_STYLES = {
    // 对齐方式
    ALIGNMENT: {
        LEFT_TOP: { horizontal: 'left', vertical: 'top' },
        CENTER_MIDDLE: { horizontal: 'center', vertical: 'middle' },
        RIGHT_BOTTOM: { horizontal: 'right', vertical: 'bottom' },
        CENTER_TOP: { horizontal: 'center', vertical: 'top' },
        LEFT_MIDDLE: { horizontal: 'left', vertical: 'middle' }
    },

    // 边框样式
    BORDERS: {
        ALL_THIN: {
            top: { style: 'thin', color: { argb: 'FF000000' } },
            left: { style: 'thin', color: { argb: 'FF000000' } },
            bottom: { style: 'thin', color: { argb: 'FF000000' } },
            right: { style: 'thin', color: { argb: 'FF000000' } }
        },
        ALL_THICK: {
            top: { style: 'thick', color: { argb: 'FF000000' } },
            left: { style: 'thick', color: { argb: 'FF000000' } },
            bottom: { style: 'thick', color: { argb: 'FF000000' } },
            right: { style: 'thick', color: { argb: 'FF000000' } }
        },
        BOTTOM_ONLY: {
            bottom: { style: 'thin', color: { argb: 'FF000000' } }
        }
    },

    // 字体样式
    FONTS: {
        HEADER: { name: '微软雅黑', size: 14, bold: true, color: { argb: 'FF000000' } },
        BODY: { name: '宋体', size: 11, bold: false, color: { argb: 'FF000000' } },
        TITLE: { name: '黑体', size: 16, bold: true, color: { argb: 'FF000000' } }
    },

    // 背景颜色
    BACKGROUNDS: {
        HEADER: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6E6FA' } },
        WARNING: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFF00' } },
        ERROR: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFF0000' } },
        SUCCESS: { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF90EE90' } }
    }
};

/**
 * 应用ExcelJS预定义样式组合
 * @param {ExcelJS.Cell} excelCell - ExcelJS单元格对象
 * @param {string} styleType - 样式类型: 'header'|'title'|'body'|'warning'|'error'|'success'
 *
 * @example
 * // ExcelJS API - 应用标题样式
 * applyPresetStyle(excelCell, 'header');
 */
export function applyPresetStyle(excelCell, styleType) {
    if (!excelCell || !styleType) return;

    try {
        // ExcelJS API - 应用预定义样式组合
        switch (styleType.toLowerCase()) {
            case 'header':
                setCellFont(excelCell, PRESET_STYLES.FONTS.HEADER);
                setCellBackground(excelCell, PRESET_STYLES.BACKGROUNDS.HEADER);
                setCellAlignment(excelCell, PRESET_STYLES.ALIGNMENT.CENTER_MIDDLE);
                setCellBorders(excelCell, PRESET_STYLES.BORDERS.ALL_THIN);
                break;

            case 'title':
                setCellFont(excelCell, PRESET_STYLES.FONTS.TITLE);
                setCellAlignment(excelCell, PRESET_STYLES.ALIGNMENT.CENTER_MIDDLE);
                break;

            case 'body':
                setCellFont(excelCell, PRESET_STYLES.FONTS.BODY);
                setCellAlignment(excelCell, PRESET_STYLES.ALIGNMENT.LEFT_MIDDLE);
                break;

            case 'warning':
                setCellFont(excelCell, PRESET_STYLES.FONTS.BODY);
                setCellBackground(excelCell, PRESET_STYLES.BACKGROUNDS.WARNING);
                setCellAlignment(excelCell, PRESET_STYLES.ALIGNMENT.CENTER_MIDDLE);
                break;

            case 'error':
                setCellFont(excelCell, { ...PRESET_STYLES.FONTS.BODY, color: { argb: 'FFFFFFFF' } });
                setCellBackground(excelCell, PRESET_STYLES.BACKGROUNDS.ERROR);
                setCellAlignment(excelCell, PRESET_STYLES.ALIGNMENT.CENTER_MIDDLE);
                break;

            case 'success':
                setCellFont(excelCell, PRESET_STYLES.FONTS.BODY);
                setCellBackground(excelCell, PRESET_STYLES.BACKGROUNDS.SUCCESS);
                setCellAlignment(excelCell, PRESET_STYLES.ALIGNMENT.CENTER_MIDDLE);
                break;

            default:
                console.warn(`未知的ExcelJS预定义样式类型: ${styleType}`);
        }
    } catch (error) {
        console.warn('ExcelJS预定义样式应用失败:', error);
    }
}
