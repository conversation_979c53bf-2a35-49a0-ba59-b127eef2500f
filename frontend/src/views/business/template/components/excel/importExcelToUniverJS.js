/**
 * ExcelJS 到 UniverJS 数据转换导入工具模块 - 优化版本
 *
 * 本模块负责将 Excel 文件通过 ExcelJS 解析后转换为 UniverJS 格式并导入到编辑器
 *
 * 优化重点：
 * - 模块化架构：拆分为独立的处理器类，提高代码可维护性
 * - 性能优化：批量处理、内存管理、减少重复计算
 * - 与导出功能对齐：使用相同的数据转换逻辑和API调用模式
 * - 错误处理统一：标准化的错误处理和用户反馈机制
 * - API封装：统一的UniverJS API调用接口
 *
 * 技术特性：
 * - 完整的数据类型支持（字符串、数字、布尔值、公式、日期）
 * - 与导出功能一致的样式处理（字体、对齐、背景、边框）
 * - 高效的合并单元格处理
 * - 精确的行列尺寸转换
 * - 完整的图片导入支持
 * - 批量数据处理和内存优化
 *
 * <AUTHOR> - 优化版本
 * @version 3.0.0
 * @date 2024-07-01
 */

// ExcelJS 库 - 用于读取和解析Excel文件
import * as ExcelJS from 'exceljs';

// UniverJS 数据类型枚举 - 与导出功能保持一致
const CellValueType = {
    STRING: 1,
    NUMBER: 2,
    BOOLEAN: 3,
    FORCE_STRING: 4
};

// 导入错误类型定义
class ImportError extends Error {
    constructor(type, message, context = {}) {
        super(message);
        this.name = 'ImportError';
        this.type = type;
        this.context = context;
    }
}

/**
 * UniverJS API 封装器 - 统一API调用接口
 */
class UniverAPIWrapper {
    constructor(univerAPI) {
        this.api = univerAPI;
        this.validateAPI();
    }

    validateAPI() {
        if (!this.api || typeof this.api.getActiveWorkbook !== 'function') {
            throw new ImportError('API_INVALID', 'univerAPI 对象无效，缺少必要方法');
        }
    }

    getActiveWorkbook() {
        return this.api.getActiveWorkbook();
    }

    getActiveSheet() {
        const workbook = this.getActiveWorkbook();
        return workbook?.getActiveSheet();
    }

    /**
     * 安全的Range获取方法 - 支持多种参数格式
     * @param {object} sheet - 工作表对象
     * @param {...any} args - 范围参数
     * @returns {object|null} - Range对象或null
     */
    safeGetRange(sheet, ...args) {
        if (!sheet || typeof sheet.getRange !== 'function') {
            return null;
        }

        // 验证参数
        if (args.length === 0) {
            return null;
        }

        try {
            // 方法1：尝试直接调用
            return sheet.getRange(...args);
        } catch (error) {
            console.warn('getRange直接调用失败，尝试其他方式:', error);

            // 方法2：如果是单个字符串参数，确保格式正确
            if (args.length === 1 && typeof args[0] === 'string') {
                try {
                    const rangeStr = args[0].toString().trim().toUpperCase();
                    if (rangeStr.match(/^[A-Z]+\d+:[A-Z]+\d+$/)) {
                        return sheet.getRange(rangeStr);
                    }
                } catch (error2) {
                    console.warn('字符串范围调用失败:', error2);
                }
            }

            // 方法3：如果是数字参数，转换为字符串格式
            if (args.length >= 2 && typeof args[0] === 'number' && typeof args[1] === 'number') {
                try {
                    const row = Math.max(0, Math.floor(args[0]));
                    const col = Math.max(0, Math.floor(args[1]));
                    const numRows = args.length > 2 ? Math.max(1, Math.floor(args[2])) : 1;
                    const numCols = args.length > 3 ? Math.max(1, Math.floor(args[3])) : 1;

                    // 转换为A1格式
                    const startCell = this.numberToA1(row, col);
                    const endCell = this.numberToA1(row + numRows - 1, col + numCols - 1);
                    const rangeStr = numRows === 1 && numCols === 1 ? startCell : `${startCell}:${endCell}`;

                    return sheet.getRange(rangeStr);
                } catch (error3) {
                    console.warn('数字参数转换失败:', error3);
                }
            }

            return null;
        }
    }

    /**
     * 将行列数字转换为A1格式
     * @param {number} row - 行号（0基索引）
     * @param {number} col - 列号（0基索引）
     * @returns {string} - A1格式字符串
     */
    numberToA1(row, col) {
        let colStr = '';
        let colNum = col + 1; // 转换为1基索引

        while (colNum > 0) {
            colNum--;
            colStr = String.fromCharCode(65 + (colNum % 26)) + colStr;
            colNum = Math.floor(colNum / 26);
        }

        return colStr + (row + 1); // 转换为1基索引
    }

    safeCall(operation, fallback = null) {
        try {
            return operation();
        } catch (error) {
            console.warn('API调用失败:', error);
            return fallback;
        }
    }
}

/**
 * Excel数据处理器 - 负责解析Excel文件
 */
class ExcelDataProcessor {
    constructor() {
        this.workbook = null;
    }

    async loadFile(excelFile) {
        try {
            this.workbook = new ExcelJS.Workbook();
            const arrayBuffer = await excelFile.arrayBuffer();
            await this.workbook.xlsx.load(arrayBuffer);

            return this.workbook;
        } catch (error) {
            throw new ImportError('FILE_PARSE', `Excel文件解析失败: ${error.message}`);
        }
    }

    getWorksheets() {
        return this.workbook?.worksheets || [];
    }

    calculateDataRange(worksheet) {
        let maxRow = 0;
        let maxCol = 0;

        worksheet.eachRow((row, rowNumber) => {
            maxRow = Math.max(maxRow, rowNumber);
            row.eachCell((cell, colNumber) => {
                maxCol = Math.max(maxCol, colNumber);
            });
        });

        return { maxRow, maxCol };
    }
}

/**
 * @async
 * @function importExcelToUniverJS
 * @description 将 Excel 文件导入到 UniverJS 编辑器中（优化版本）
 * @param {File} excelFile - Excel文件对象
 * @param {object} univerAPI - UniverJS 实例对象 (univerAPI)
 * @param {object} [options={}] - 导入选项配置
 * @param {boolean} [options.clearExisting=true] - 是否清空现有数据
 * @param {boolean} [options.importImages=true] - 是否导入图片
 * @param {boolean} [options.importStyles=true] - 是否导入样式
 * @returns {Promise<object>} - 导入结果信息
 */
export async function importExcelToUniverJS(excelFile, univerAPI, options = {}) {
    // 参数验证
    if (!excelFile) {
        throw new ImportError('PARAM_INVALID', 'excelFile 参数不能为空');
    }

    // 默认选项
    const importOptions = {
        clearExisting: true,
        importImages: true,
        importStyles: true,
        ...options
    };

    try {

        // 初始化处理器
        const apiWrapper = new UniverAPIWrapper(univerAPI);
        const excelProcessor = new ExcelDataProcessor();
        const importer = new ExcelImporter(apiWrapper, importOptions);

        // 解析Excel文件
        await excelProcessor.loadFile(excelFile);
        const worksheets = excelProcessor.getWorksheets();

        if (worksheets.length === 0) {
            throw new ImportError('NO_WORKSHEETS', 'Excel文件中没有工作表');
        }

        // 执行导入（当前版本处理第一个工作表）
        const worksheet = worksheets[0];
        const result = await importer.importWorksheet(worksheet);

        return result;

    } catch (error) {
        if (error instanceof ImportError) {
            console.error('❌ 导入失败:', error.message);
            throw error;
        } else {
            console.error('❌ 系统错误:', error);
            throw new ImportError('SYSTEM_ERROR', `系统错误: ${error.message}`);
        }
    }
}

/**
 * Excel导入器 - 主要导入逻辑
 */
class ExcelImporter {
    constructor(apiWrapper, options) {
        this.api = apiWrapper;
        this.options = options;
        this.dataConverter = new DataConverter();
        this.styleConverter = new StyleConverter();
        this.dimensionConverter = new DimensionConverter();
    }

    async importWorksheet(excelWorksheet) {
        const result = {
            totalSheets: 1,
            importedSheets: 0,
            totalCells: 0,
            importedCells: 0,
            totalImages: 0,
            importedImages: 0,
            errors: []
        };

        try {
            // 清空现有数据
            if (this.options.clearExisting) {
                await this.clearExistingData();
            }

            // 构建数据
            const univerData = await this.buildUniverData(excelWorksheet);

            // 应用数据
            await this.applyDataToUniver(univerData);

            // 更新结果
            result.importedSheets = 1;
            result.totalCells = univerData.totalCells;
            result.importedCells = univerData.importedCells;
            result.totalImages = univerData.totalImages;
            result.importedImages = univerData.importedImages;
            result.errors = univerData.errors;

        } catch (error) {
            console.error('工作表处理失败:', error);
            result.errors.push({
                type: 'worksheet',
                error: error.message
            });
        }

        return result;
    }

    async buildUniverData(excelWorksheet) {
        const univerData = {
            cellData: {},
            styles: {},
            mergeData: [],
            rowData: {},
            columnData: {},
            images: [],
            totalCells: 0,
            importedCells: 0,
            totalImages: 0,
            importedImages: 0,
            errors: []
        };

        // 计算数据范围
        const dataRange = this.calculateDataRange(excelWorksheet);

        // 批量处理单元格数据
        await this.processCellDataBatch(excelWorksheet, univerData, dataRange);

        // 处理其他数据
        if (this.options.importStyles) {
            this.processMergedCells(excelWorksheet, univerData);
        }

        this.processDimensions(excelWorksheet, univerData);

        if (this.options.importImages) {
            await this.processImages(excelWorksheet, univerData);
        }

        return univerData;
    }

    calculateDataRange(worksheet) {
        let maxRow = 0;
        let maxCol = 0;

        worksheet.eachRow((row, rowNumber) => {
            maxRow = Math.max(maxRow, rowNumber);
            row.eachCell((cell, colNumber) => {
                maxCol = Math.max(maxCol, colNumber);
            });
        });

        return { maxRow, maxCol };
    }

    async processCellDataBatch(excelWorksheet, univerData, dataRange) {
        let styleIdCounter = 1;
        const batchSize = 100; // 批量处理大小

        for (let rowNum = 1; rowNum <= dataRange.maxRow; rowNum += batchSize) {
            const endRow = Math.min(rowNum + batchSize - 1, dataRange.maxRow);

            for (let currentRow = rowNum; currentRow <= endRow; currentRow++) {
                const excelRow = excelWorksheet.getRow(currentRow);

                for (let colNum = 1; colNum <= dataRange.maxCol; colNum++) {
                    const excelCell = excelRow.getCell(colNum);
                    univerData.totalCells++;

                    if (this.shouldProcessCell(excelCell)) {
                        try {
                            const result = this.processSingleCell(
                                excelCell,
                                currentRow - 1,
                                colNum - 1,
                                univerData.styles,
                                styleIdCounter,
                                excelRow
                            );

                            if (result) {
                                // 初始化行数据
                                if (!univerData.cellData[result.row]) {
                                    univerData.cellData[result.row] = {};
                                }

                                univerData.cellData[result.row][result.col] = result.cellData;
                                univerData.importedCells++;

                                if (result.styleId) {
                                    styleIdCounter = result.styleId + 1;
                                }
                            }
                        } catch (error) {
                            univerData.errors.push({
                                type: 'cell',
                                position: `${currentRow},${colNum}`,
                                error: error.message
                            });
                        }
                    }
                }
            }

            // 批量处理间隔，避免阻塞UI
            if (rowNum % (batchSize * 5) === 1) {
                await new Promise(resolve => setTimeout(resolve, 0));
            }
        }
    }

    shouldProcessCell(excelCell) {
        return (excelCell.value !== null && excelCell.value !== undefined) ||
               (excelCell.style && this.styleConverter.hasSignificantStyle(excelCell.style));
    }

    processSingleCell(excelCell, row, col, stylesMap, styleIdCounter, excelRow) {
        // 转换单元格值，传入完整的单元格对象以检查软换行
        const valueResult = this.dataConverter.convertExcelValue(excelCell);
        if (!valueResult && !excelCell.style) {
            return null;
        }

        const cellData = {};

        // 设置值和类型
        if (valueResult) {
            if (valueResult.hasLineBreaks && valueResult.richTextData) {
                // 对于包含换行符的文本，使用富文本格式
                cellData.p = valueResult.richTextData;
                cellData.v = valueResult.value; // 保留原始值作为备用
                cellData.t = valueResult.type;
            } else if (valueResult.value !== undefined && valueResult.value !== null) {
                cellData.v = valueResult.value;
                cellData.t = valueResult.type;
            }

            // 处理公式
            if (excelCell.formula) {
                cellData.f = this.dataConverter.convertFormula(excelCell.formula);
            }
        }

        // 处理样式
        let currentStyleId = styleIdCounter;
        if (this.options.importStyles && excelCell.style) {
            const styleResult = this.styleConverter.convertExcelStyle(
                excelCell.style,
                valueResult?.hasLineBreaks || false,
                valueResult?.hasSoftWrap || false,
                excelRow
            );

            if (styleResult) {
                const styleId = currentStyleId.toString();
                stylesMap[styleId] = styleResult;
                cellData.s = styleId;
                currentStyleId++;
            }
        }

        return {
            row,
            col,
            cellData,
            styleId: currentStyleId > styleIdCounter ? currentStyleId - 1 : null
        };
    }
}

/**
 * 数据转换器 - 负责Excel到UniverJS的数据类型转换
 */
class DataConverter {
    convertExcelValue(excelCell) {
        const excelValue = excelCell.value;

        // 处理公式单元格的结果值
        if (excelCell.formula && excelCell.result !== undefined) {
            return this.convertExcelValue({ value: excelCell.result });
        }

        // 空值处理
        if (excelValue === null || excelValue === undefined || excelValue === '') {
            return { value: '', type: CellValueType.STRING, hasLineBreaks: false };
        }

        // 数字类型
        if (typeof excelValue === 'number') {
            return { value: excelValue, type: CellValueType.NUMBER, hasLineBreaks: false };
        }

        // 布尔类型
        if (typeof excelValue === 'boolean') {
            return { value: excelValue, type: CellValueType.BOOLEAN, hasLineBreaks: false };
        }

        // 日期类型
        if (excelValue instanceof Date) {
            return { value: excelValue, type: CellValueType.NUMBER, hasLineBreaks: false };
        }

        // 字符串类型
        if (typeof excelValue === 'string') {
            const hasHardLineBreaks = /[\r\n]/.test(excelValue);
            const hasSoftWrap = excelCell.style?.alignment?.wrapText === true;

            if (hasHardLineBreaks) {
                // 对于包含实际换行符的文本，转换为UniverJS格式（\n -> \r）
                const processedValue = excelValue.replace(/\r\n/g, '\r').replace(/\n/g, '\r');

                return {
                    value: processedValue,
                    type: CellValueType.STRING,
                    hasLineBreaks: true,
                    richTextData: this.createRichTextForLineBreaks(processedValue)
                };
            } else if (hasSoftWrap) {
                // 对于设置了软换行但没有实际换行符的文本，标记为需要软换行
                return {
                    value: excelValue, // 保留原始空格，不使用trim()
                    type: CellValueType.STRING,
                    hasLineBreaks: false,
                    hasSoftWrap: true
                };
            } else {
                return {
                    value: excelValue, // 保留原始空格，不使用trim()
                    type: CellValueType.STRING,
                    hasLineBreaks: false
                };
            }
        }

        // 富文本类型
        if (excelValue && typeof excelValue === 'object' && excelValue.richText) {
            const plainText = excelValue.richText.map(rt => rt.text || '').join('');
            const hasLineBreaks = /[\r\n]/.test(plainText);

            if (hasLineBreaks) {
                // 对于富文本中的换行符，也转换为UniverJS格式
                const processedText = plainText.replace(/\r\n/g, '\r').replace(/\n/g, '\r');

                return {
                    value: processedText,
                    type: CellValueType.STRING,
                    hasLineBreaks: true,
                    richTextData: this.createRichTextForLineBreaks(processedText)
                };
            } else {
                return {
                    value: plainText, // 保留原始空格，不使用trim()
                    type: CellValueType.STRING,
                    hasLineBreaks: false
                };
            }
        }

        // 其他类型转换为字符串
        const stringValue = String(excelValue);
        const hasLineBreaks = /[\r\n]/.test(stringValue);

        if (hasLineBreaks) {
            const processedValue = stringValue.replace(/\r\n/g, '\r').replace(/\n/g, '\r');

            return {
                value: processedValue,
                type: CellValueType.STRING,
                hasLineBreaks: true,
                richTextData: this.createRichTextForLineBreaks(processedValue)
            };
        } else {
            return {
                value: stringValue, // 保留原始空格，不使用trim()
                type: CellValueType.STRING,
                hasLineBreaks: false
            };
        }
    }

    /**
     * 为包含换行符的文本创建UniverJS富文本格式
     * @param {string} text - 包含换行符的文本（已转换为\r格式）
     * @returns {object} - UniverJS富文本数据结构
     */
    createRichTextForLineBreaks(text) {
        // 根据UniverJS源码分析，正确的富文本格式应该：
        // 1. dataStream以\r\n结尾
        // 2. 每个\r位置创建一个段落
        // 3. 最后的\n位置创建sectionBreak

        const dataStream = text + '\r\n'; // UniverJS标准格式
        const paragraphs = [];
        const sectionBreaks = [];

        // 分析dataStream，为每个\r创建段落，为\n创建章节分隔
        for (let i = 0; i < dataStream.length; i++) {
            const char = dataStream[i];
            if (char === '\r') {
                paragraphs.push({
                    startIndex: i,
                    paragraphStyle: {
                        spaceAbove: { v: 0 },
                        lineSpacing: 1.2,
                        spaceBelow: { v: 0 }
                    }
                });
            } else if (char === '\n') {
                sectionBreaks.push({
                    startIndex: i
                });
            }
        }

        // 创建textRuns，覆盖整个文本内容（不包括结尾的\r\n）
        const textRuns = [
            {
                st: 0,
                ed: text.length,
                ts: {
                    ff: '宋体', // 默认字体
                    fs: 11,    // 默认字体大小
                    cl: { rgb: '#000000' } // 默认颜色
                }
            }
        ];

        return {
            id: 'd',
            body: {
                dataStream: dataStream,
                textRuns: textRuns,
                paragraphs: paragraphs,
                sectionBreaks: sectionBreaks,
                customBlocks: [],
                customRanges: [],
                customDecorations: [],
                tables: []
            },
            documentStyle: {
                pageSize: {
                    width: Number.POSITIVE_INFINITY,
                    height: Number.POSITIVE_INFINITY
                },
                marginTop: 0,
                marginBottom: 2,
                marginRight: 2,
                marginLeft: 2,
                renderConfig: {
                    horizontalAlign: 0,
                    verticalAlign: 0,
                    centerAngle: 0,
                    vertexAngle: 0,
                    wrapStrategy: 1, // WrapStrategy.OVERFLOW - 只在原始换行符位置换行
                    zeroWidthParagraphBreak: 1
                }
            },
            drawings: {},
            drawingsOrder: [],
            settings: {
                zoomRatio: 1
            }
        };
    }

    convertFormula(excelFormula) {
        // 确保公式以=开头
        if (!excelFormula.startsWith('=')) {
            return '=' + excelFormula;
        }
        return excelFormula;
    }
}

/**
 * 样式转换器 - 负责Excel到UniverJS的样式转换
 */
class StyleConverter {
    hasSignificantStyle(excelStyle) {
        if (!excelStyle) return false;

        // 检查字体样式
        if (excelStyle.font) {
            const fontProps = ['name', 'size', 'bold', 'italic', 'color', 'underline', 'strike'];
            if (fontProps.some(prop => excelStyle.font[prop])) {
                return true;
            }
        }

        // 检查对齐方式
        if (excelStyle.alignment) {
            const alignProps = ['horizontal', 'vertical', 'wrapText'];
            if (alignProps.some(prop => excelStyle.alignment[prop])) {
                return true;
            }
        }

        // 检查背景色
        if (excelStyle.fill && excelStyle.fill.type === 'pattern' && excelStyle.fill.fgColor) {
            return true;
        }

        // 检查边框
        if (excelStyle.border) {
            const borders = ['top', 'right', 'bottom', 'left'];
            return borders.some(border =>
                excelStyle.border[border] &&
                excelStyle.border[border].style &&
                excelStyle.border[border].style !== 'none'
            );
        }

        // 检查数字格式
        if (excelStyle.numFmt) {
            return true;
        }

        return false;
    }

    convertExcelStyle(excelStyle, hasLineBreaks = false, hasSoftWrap = false, excelRow = null) {
        const univerStyle = {};

        try {
            // 字体样式
            if (excelStyle.font) {
                this.convertFontStyle(excelStyle.font, univerStyle, excelRow);
            }

            // 对齐方式
            if (excelStyle.alignment) {
                this.convertAlignmentStyle(excelStyle.alignment, univerStyle, hasLineBreaks, hasSoftWrap);
            }

            // 背景色
            if (excelStyle.fill) {
                this.convertFillStyle(excelStyle.fill, univerStyle);
            }

            // 边框
            if (excelStyle.border) {
                this.convertBorderStyle(excelStyle.border, univerStyle);
            }

            // 多行文本换行设置 - 区分原始换行符、软换行和自动换行
            if (hasLineBreaks) {
                // 对于包含原始换行符的文本，不设置换行策略
                // 让UniverJS的富文本系统自然处理段落换行
                // 不设置 univerStyle.tb，让富文本自然换行
            } else if (hasSoftWrap || (excelStyle.alignment && excelStyle.alignment.wrapText)) {
                // 对于软换行或Excel明确设置了wrapText的文本，使用自动换行
                // WrapStrategy枚举值: UNSPECIFIED=0, OVERFLOW=1, CLIP=2, WRAP=3
                univerStyle.tb = 3; // WrapStrategy.WRAP - 自动换行
            }

        } catch (error) {
            console.warn('样式转换失败:', error);
        }

        return Object.keys(univerStyle).length > 0 ? univerStyle : null;
    }

    convertFontStyle(excelFont, univerStyle, excelRow) {
        if (excelFont.name) univerStyle.ff = excelFont.name;
        if (excelFont.size) {
            let fontSize = Number(excelFont.size);
            // 兜底：如果字体为10且行高为24.95（或接近），推断原始为10.5，设为11pt
            if (fontSize === 10 && excelRow && Math.abs(excelRow.height - 24.95) < 0.2) {
                fontSize = 11;
            }
            univerStyle.fs = fontSize;
        }
        if (excelFont.bold) univerStyle.bl = 1;
        if (excelFont.italic) univerStyle.it = 1;

        if (excelFont.color && excelFont.color.argb) {
            univerStyle.cl = { rgb: this.convertColor(excelFont.color.argb) };
        }

        if (excelFont.underline) {
            univerStyle.ul = { s: 1 };
        }

        if (excelFont.strike) {
            univerStyle.st = { s: 1 };
        }
    }

    convertAlignmentStyle(excelAlignment, univerStyle, hasLineBreaks = false, hasSoftWrap = false) {
        if (excelAlignment.horizontal) {
            univerStyle.ht = this.convertHorizontalAlign(excelAlignment.horizontal);
        }

        if (excelAlignment.vertical) {
            univerStyle.vt = this.convertVerticalAlign(excelAlignment.vertical);
        }

        // 处理文本换行 - 区分原始换行符、软换行和自动换行
        if (hasLineBreaks) {
            // 对于包含原始换行符的文本，不设置换行策略
            // 让UniverJS的富文本系统自然处理段落换行
            // 不设置 univerStyle.tb，让富文本自然换行
        } else if (hasSoftWrap || excelAlignment.wrapText) {
            // 对于软换行或Excel设置了wrapText的文本，使用自动换行
            // WrapStrategy枚举值: UNSPECIFIED=0, OVERFLOW=1, CLIP=2, WRAP=3
            univerStyle.tb = 3; // WrapStrategy.WRAP - 自动换行
        }
    }

    convertFillStyle(excelFill, univerStyle) {
        if (excelFill.type === 'pattern' && excelFill.fgColor && excelFill.fgColor.argb) {
            univerStyle.bg = { rgb: this.convertColor(excelFill.fgColor.argb) };
        }
    }

    convertBorderStyle(excelBorder, univerStyle) {
        const borderData = {};
        const directions = [
            { excel: 'top', univer: 't' },
            { excel: 'right', univer: 'r' },
            { excel: 'bottom', univer: 'b' },
            { excel: 'left', univer: 'l' }
        ];

        for (const direction of directions) {
            const excelBorderData = excelBorder[direction.excel];

            if (excelBorderData && excelBorderData.style && excelBorderData.style !== 'none') {
                const borderStyle = this.convertBorderStyleType(excelBorderData.style);
                const borderColor = this.convertBorderColor(excelBorderData.color);

                borderData[direction.univer] = {
                    s: borderStyle,
                    cl: { rgb: borderColor }
                };
            }
        }

        if (Object.keys(borderData).length > 0) {
            univerStyle.bd = borderData;
        }
    }

    convertColor(excelColor) {
        // Excel ARGB格式: FF000000 -> UniverJS RGB格式: #000000
        if (excelColor && excelColor.length === 8) {
            return '#' + excelColor.substring(2);
        }
        return excelColor || '#000000';
    }

    convertHorizontalAlign(excelAlign) {
        // 根据导出功能的反向映射，确保对齐方式正确转换
        // ExcelJS -> UniverJS 水平对齐映射
        const alignMap = {
            'left': 1,           // HorizontalAlign.LEFT
            'center': 2,         // HorizontalAlign.CENTER
            'right': 3,          // HorizontalAlign.RIGHT
            'justify': 4,        // HorizontalAlign.JUSTIFIED
            'fill': 1,           // 填充模式映射为左对齐
            'centerContinuous': 2, // 跨列居中映射为居中
            'distributed': 5     // HorizontalAlign.DISTRIBUTED
        };

        return alignMap[excelAlign] || 1;
    }

    convertVerticalAlign(excelAlign) {
        // 根据导出功能的反向映射，确保对齐方式正确转换
        // ExcelJS -> UniverJS 垂直对齐映射
        const alignMap = {
            'top': 1,        // VerticalAlign.TOP
            'middle': 2,     // VerticalAlign.MIDDLE
            'bottom': 3,     // VerticalAlign.BOTTOM
            'justify': 4,    // VerticalAlign.JUSTIFY
            'distributed': 5 // VerticalAlign.DISTRIBUTED
        };

        return alignMap[excelAlign] || 1;
    }

    convertBorderStyleType(excelStyle) {
        const styleMap = {
            'none': 0, 'thin': 1, 'hair': 2, 'dotted': 3, 'dashed': 4,
            'dashDot': 5, 'dashDotDot': 6, 'double': 7, 'medium': 8,
            'mediumDashed': 9, 'mediumDashDot': 10, 'mediumDashDotDot': 11,
            'slantDashDot': 12, 'thick': 13
        };

        return styleMap[excelStyle] || 1;
    }

    convertBorderColor(excelColor) {
        if (!excelColor) return '#000000';
        if (excelColor.argb) return this.convertColor(excelColor.argb);
        if (excelColor.rgb) return '#' + excelColor.rgb;
        return '#000000';
    }
}

/**
 * 尺寸转换器 - 负责行高列宽的转换
 */
class DimensionConverter {
    convertExcelRowHeight(excelHeight) {
        // Excel行高单位是点(pt)，UniverJS使用像素(px)
        // 小数pt向上取整
        const pt = !Number.isInteger(excelHeight) ? Math.ceil(excelHeight) : excelHeight;
        return Math.round(pt * 1.33);
    }

    convertExcelColumnWidth(excelWidth) {
        // Excel列宽单位是字符，UniverJS使用像素(px)
        // 使用与导出功能一致的转换比例
        return Math.round(excelWidth * 8.2);
    }
}

/**
 * 继续ExcelImporter类的其他方法
 */
// 为ExcelImporter类添加其他方法
Object.assign(ExcelImporter.prototype, {
    async clearExistingData() {
        try {
            const activeSheet = this.api.getActiveSheet();

            if (!activeSheet) {
                return;
            }

            // 1. 清空所有单元格数据和格式
            const clearRange = this.api.safeGetRange(activeSheet, 'A1:ZZ1000');

            if (clearRange) {
                // 清空内容和格式
                this.api.safeCall(() => clearRange.clear());
            } else {
                // 降级方案：逐行清空
                for (let row = 0; row < 100; row++) {
                    for (let col = 0; col < 50; col++) {
                        const cellRange = this.api.safeGetRange(activeSheet, row, col);
                        if (cellRange) {
                            this.api.safeCall(() => cellRange.clear());
                        }
                    }
                }
            }

            // 2. 清空所有合并单元格
            try {
                const mergedRanges = this.api.safeCall(() => activeSheet.getMergedRanges());
                if (mergedRanges && mergedRanges.length > 0) {
                    for (const mergedRange of mergedRanges) {
                        this.api.safeCall(() => mergedRange.breakApart());
                    }
                }
            } catch (mergeError) {
                console.warn('清空合并单元格失败:', mergeError);
            }

            // 3. 重置行高列宽到默认值
            try {
                // 重置行高（前100行）
                for (let row = 0; row < 100; row++) {
                    this.api.safeCall(() => {
                        if (typeof activeSheet.setRowHeight === 'function') {
                            activeSheet.setRowHeight(row, 24); // 默认行高24px
                        }
                    });
                }

                // 重置列宽（前50列）
                for (let col = 0; col < 50; col++) {
                    this.api.safeCall(() => {
                        if (typeof activeSheet.setColumnWidth === 'function') {
                            activeSheet.setColumnWidth(col, 88); // 默认列宽88px
                        }
                    });
                }
            } catch (dimensionError) {
                console.warn('重置行高列宽失败:', dimensionError);
            }

        } catch (error) {
            console.warn('清空数据失败:', error);
            // 即使清空失败，也继续导入过程
        }
    },

    processMergedCells(excelWorksheet, univerData) {
        try {
            let mergedCells = [];

            // 获取合并单元格信息
            if (excelWorksheet.model && Array.isArray(excelWorksheet.model.merges)) {
                mergedCells = excelWorksheet.model.merges;
            } else if (excelWorksheet.mergeCells && Array.isArray(excelWorksheet.mergeCells)) {
                mergedCells = excelWorksheet.mergeCells;
            }

            if (mergedCells.length === 0) {
                return;
            }

            for (const merge of mergedCells) {
                try {
                    const range = this.parseExcelRange(merge);
                    if (range && this.isValidMergeRange(range)) {
                        univerData.mergeData.push({
                            startRow: range.startRow - 1,
                            startColumn: range.startCol - 1,
                            endRow: range.endRow - 1,
                            endColumn: range.endCol - 1
                        });
                    }
                } catch (error) {
                    univerData.errors.push({
                        type: 'merge',
                        range: merge,
                        error: error.message
                    });
                }
            }
        } catch (error) {
            console.warn('处理合并单元格失败:', error);
        }
    },

    processDimensions(excelWorksheet, univerData) {
        try {
            // 处理行高
            excelWorksheet.eachRow((row, rowNumber) => {
                if (row.height) {
                    const univerHeight = this.dimensionConverter.convertExcelRowHeight(row.height);
                    const univerRowIndex = rowNumber - 1;

                    if (!univerData.rowData[univerRowIndex]) {
                        univerData.rowData[univerRowIndex] = {};
                    }

                    univerData.rowData[univerRowIndex].h = univerHeight;
                }
            });

            // 处理列宽
            excelWorksheet.columns.forEach((column, index) => {
                if (column && column.width) {
                    const univerWidth = this.dimensionConverter.convertExcelColumnWidth(column.width);

                    if (!univerData.columnData[index]) {
                        univerData.columnData[index] = {};
                    }

                    univerData.columnData[index].w = univerWidth;
                }
            });
        } catch (error) {
            console.warn('处理尺寸失败:', error);
        }
    },

    async processImages(excelWorksheet, univerData) {
        try {
            const images = excelWorksheet.getImages();
            univerData.totalImages = images.length;

            if (images.length === 0) {
                return;
            }

            // 保存当前工作表引用，供图片尺寸计算使用
            this.currentWorksheet = excelWorksheet;

            for (const image of images) {
                try {
                    const imageData = await this.getExcelImageData(excelWorksheet, image);
                    const imagePosition = this.convertExcelImagePosition(image);

                    if (imageData && imagePosition) {
                        univerData.images.push({
                            imageData: imageData,
                            position: imagePosition
                        });
                        univerData.importedImages++;
                    }
                } catch (error) {
                    univerData.errors.push({
                        type: 'image',
                        error: error.message
                    });
                }
            }
        } catch (error) {
            console.warn('处理图片失败:', error);
        }
    },

    parseExcelRange(rangeStr) {
        try {
            const cleanRangeStr = rangeStr.trim().toUpperCase();
            const parts = cleanRangeStr.split(':');

            if (parts.length !== 2) {
                throw new Error(`无效的范围格式: ${rangeStr}`);
            }

            const startCell = this.parseExcelCellAddress(parts[0].trim());
            const endCell = this.parseExcelCellAddress(parts[1].trim());

            return {
                startRow: Math.min(startCell.row, endCell.row),
                startCol: Math.min(startCell.col, endCell.col),
                endRow: Math.max(startCell.row, endCell.row),
                endCol: Math.max(startCell.col, endCell.col)
            };
        } catch (error) {
            console.error(`解析范围失败: ${rangeStr}`, error);
            return null;
        }
    },

    parseExcelCellAddress(cellAddr) {
        const cleanAddr = cellAddr.trim().toUpperCase();
        const match = cleanAddr.match(/^([A-Z]+)(\d+)$/);

        if (!match) {
            throw new Error(`无效的单元格地址: ${cellAddr}`);
        }

        const colStr = match[1];
        const rowStr = match[2];

        // 转换列字母到数字
        let col = 0;
        for (let i = 0; i < colStr.length; i++) {
            col = col * 26 + (colStr.charCodeAt(i) - 64);
        }

        const row = parseInt(rowStr);

        if (row <= 0 || col <= 0) {
            throw new Error(`无效的行列值: 行=${row}, 列=${col}`);
        }

        return { row, col };
    },

    isValidMergeRange(range) {
        return range.startRow < range.endRow || range.startCol < range.endCol;
    }
});

// 继续ExcelImporter类的数据应用方法
Object.assign(ExcelImporter.prototype, {
    async applyDataToUniver(univerData) {
        try {
            const activeSheet = this.api.getActiveSheet();
            if (!activeSheet) {
                throw new ImportError('NO_ACTIVE_SHEET', '没有活动工作表');
            }

            // 第一步：应用单元格数据（不包括边框）
            await this.applyCellDataBatch(activeSheet, univerData, { skipBorders: true });

            // 第二步：应用合并单元格
            if (univerData.mergeData.length > 0) {
                await this.applyMergedCells(activeSheet, univerData.mergeData);
            }

            // 第三步：应用边框（在合并单元格之后）
            await this.applyBordersAfterMerge(activeSheet, univerData);

            // 第四步：应用行高列宽
            await this.applyDimensions(activeSheet, univerData);

            // 第五步：应用图片（如果启用图片导入）
            if (this.options.importImages && univerData.images.length > 0) {
                await this.applyImages(activeSheet, univerData.images);
            }

        } catch (error) {
            console.error('应用数据失败:', error);
            throw error;
        }
    },

    async applyCellDataBatch(activeSheet, univerData, options = {}) {
        let appliedCells = 0;
        let appliedStyles = 0;
        const batchSize = 50;
        const { skipBorders = false } = options;

        const cellEntries = Object.entries(univerData.cellData);

        for (let i = 0; i < cellEntries.length; i += batchSize) {
            const batch = cellEntries.slice(i, i + batchSize);

            for (const [rowIndex, rowData] of batch) {
                for (const [colIndex, cellData] of Object.entries(rowData)) {
                    try {
                        const row = parseInt(rowIndex);
                        const col = parseInt(colIndex);

                        // 验证行列索引
                        if (isNaN(row) || isNaN(col) || row < 0 || col < 0) {
                            console.warn(`无效的行列索引: (${rowIndex},${colIndex})`);
                            continue;
                        }

                        // 获取单元格范围 - 确保使用正确的参数格式
                        const range = this.api.safeCall(() => {
                            // 尝试多种Range获取方式
                            if (typeof activeSheet.getRange === 'function') {
                                // 方法1：使用行列索引 (row, col, numRows, numCols)
                                return activeSheet.getRange(row, col, 1, 1);
                            }
                            return null;
                        });

                        if (range) {
                            // 优先设置富文本内容（包含换行符的文本）
                            if (cellData.p) {
                                this.api.safeCall(() => {
                                    if (typeof range.setRichTextValueForCell === 'function') {
                                        // 使用富文本数据设置单元格内容
                                        range.setRichTextValueForCell(cellData.p);
                                    } else {
                                        // 降级处理：使用普通值
                                        if (cellData.v !== undefined && cellData.v !== null) {
                                            if (typeof range.setValue === 'function') {
                                                range.setValue(cellData.v);
                                            } else if (typeof range.setValues === 'function') {
                                                range.setValues([[cellData.v]]);
                                            }
                                        }
                                    }
                                });
                            } else if (cellData.v !== undefined && cellData.v !== null) {
                                // 设置普通单元格值
                                this.api.safeCall(() => {
                                    if (typeof range.setValue === 'function') {
                                        range.setValue(cellData.v);
                                    } else if (typeof range.setValues === 'function') {
                                        range.setValues([[cellData.v]]);
                                    }
                                });
                            }

                            // 设置公式
                            if (cellData.f) {
                                this.api.safeCall(() => {
                                    if (typeof range.setFormula === 'function') {
                                        range.setFormula(cellData.f);
                                    }
                                });
                            }

                            // 应用样式
                            if (cellData.s && univerData.styles[cellData.s]) {
                                const styleData = univerData.styles[cellData.s];
                                await this.applyStyleToRange(range, styleData, { skipBorders });
                                appliedStyles++;
                            }

                            appliedCells++;
                        } else {
                            console.warn(`无法获取单元格范围: (${row},${col})`);
                        }
                    } catch (error) {
                        console.warn(`设置单元格 (${rowIndex},${colIndex}) 失败:`, error);
                    }
                }
            }

            // 批量处理间隔
            if (i % (batchSize * 2) === 0) {
                await new Promise(resolve => setTimeout(resolve, 0));
            }
        }


    },

    async applyStyleToRange(range, styleData, options = {}) {
        try {
            const { skipBorders = false } = options;

            // 字体样式
            if (styleData.ff && typeof range.setFontFamily === 'function') {
                this.api.safeCall(() => range.setFontFamily(styleData.ff));
            }

            if (styleData.fs && typeof range.setFontSize === 'function') {
                this.api.safeCall(() => range.setFontSize(styleData.fs));
            }

            if (styleData.bl === 1) {
                this.api.safeCall(() => {
                    if (typeof range.setFontWeight === 'function') {
                        range.setFontWeight('bold');
                    } else if (typeof range.setBold === 'function') {
                        range.setBold(true);
                    }
                });
            }

            if (styleData.it === 1) {
                this.api.safeCall(() => {
                    if (typeof range.setFontStyle === 'function') {
                        range.setFontStyle('italic');
                    } else if (typeof range.setItalic === 'function') {
                        range.setItalic(true);
                    }
                });
            }

            if (styleData.cl && styleData.cl.rgb && typeof range.setFontColor === 'function') {
                this.api.safeCall(() => range.setFontColor(styleData.cl.rgb));
            }

            // 对齐方式 - 使用与导出功能一致的映射
            if (styleData.ht !== undefined && typeof range.setHorizontalAlignment === 'function') {
                // UniverJS Core -> Facade API 水平对齐映射
                // 根据 univer/packages/sheets/src/facade/utils.ts 中的映射关系
                const alignMap = {
                    1: 'left',      // HorizontalAlign.LEFT -> 'left'
                    2: 'center',    // HorizontalAlign.CENTER -> 'center'
                    3: 'normal',    // HorizontalAlign.RIGHT -> 'normal' (Facade API中右对齐使用'normal')
                    4: 'left',      // HorizontalAlign.JUSTIFIED -> 降级为'left'
                    5: 'left'       // HorizontalAlign.DISTRIBUTED -> 降级为'left'
                };
                const alignment = alignMap[styleData.ht] || 'left';
                this.api.safeCall(() => range.setHorizontalAlignment(alignment));
            }

            if (styleData.vt !== undefined && typeof range.setVerticalAlignment === 'function') {
                // UniverJS -> Facade API 垂直对齐映射（与导出功能保持一致）
                const alignMap = {
                    1: 'top',       // VerticalAlign.TOP
                    2: 'middle',    // VerticalAlign.MIDDLE
                    3: 'bottom',    // VerticalAlign.BOTTOM
                    4: 'middle',    // VerticalAlign.JUSTIFY (降级为居中)
                    5: 'middle'     // VerticalAlign.DISTRIBUTED (降级为居中)
                };
                const alignment = alignMap[styleData.vt] || 'top';
                this.api.safeCall(() => range.setVerticalAlignment(alignment));
            }

            // 文本换行处理 - 根据换行策略设置不同的换行行为
            if (styleData.tb !== undefined) {
                this.api.safeCall(() => {
                    if (typeof range.setWrapStrategy === 'function') {
                        // 使用setWrapStrategy方法设置具体的换行策略
                        range.setWrapStrategy(styleData.tb);
                    } else if (typeof range.setWrap === 'function') {
                        // 降级方案：使用setWrap方法
                        // WrapStrategy枚举值: UNSPECIFIED=0, OVERFLOW=1, CLIP=2, WRAP=3
                        const shouldWrap = styleData.tb === 3; // 只有WRAP策略才启用自动换行
                        range.setWrap(shouldWrap);
                    }
                });
            }

            // 背景色
            if (styleData.bg && styleData.bg.rgb) {
                this.api.safeCall(() => {
                    if (typeof range.setBackgroundColor === 'function') {
                        range.setBackgroundColor(styleData.bg.rgb);
                    } else if (typeof range.setBackground === 'function') {
                        range.setBackground(styleData.bg.rgb);
                    }
                });
            }

            // 边框（只有在不跳过边框时才应用）
            if (!skipBorders && styleData.bd) {
                await this.applyBordersToRange(range, styleData.bd);
            }

        } catch (error) {
            console.warn('应用样式失败:', error);
        }
    },

    async applyBordersToRange(range, borderData) {
        try {
            // 检查是否有有效的边框数据
            const hasBorders = Object.keys(borderData).some(key =>
                borderData[key] && borderData[key].s !== undefined && borderData[key].s > 0
            );

            if (!hasBorders) {
                return;
            }

            // 根据UniverJS源码，setBorder方法需要正确的BorderType和BorderStyleTypes参数
            // 优先尝试使用'all'类型设置所有边框（如果所有边框样式相同）
            const borderValues = Object.values(borderData).filter(b => b && b.s !== undefined && b.s > 0);

            if (borderValues.length > 0) {
                const firstBorder = borderValues[0];
                const allSameStyle = borderValues.every(b =>
                    b.s === firstBorder.s &&
                    (b.cl?.rgb || '#000000') === (firstBorder.cl?.rgb || '#000000')
                );

                // 如果所有边框样式相同且有3个或以上边框，使用'all'类型
                if (allSameStyle && borderValues.length >= 3) {
                    const borderStyle = firstBorder.s;
                    const borderColor = firstBorder.cl?.rgb || '#000000';

                    this.api.safeCall(() => {
                        if (typeof range.setBorder === 'function') {
                            range.setBorder('all', borderStyle, borderColor);
                        }
                    });
                    return;
                }
            }

            // 逐个设置每个方向的边框
            const borderDirections = [
                { univer: 't', type: 'top' },
                { univer: 'r', type: 'right' },
                { univer: 'b', type: 'bottom' },
                { univer: 'l', type: 'left' }
            ];

            for (const direction of borderDirections) {
                const borderInfo = borderData[direction.univer];
                if (borderInfo && borderInfo.s !== undefined && borderInfo.s > 0) {
                    const borderStyle = borderInfo.s;
                    const borderColor = borderInfo.cl?.rgb || '#000000';

                    this.api.safeCall(() => {
                        if (typeof range.setBorder === 'function') {
                            range.setBorder(direction.type, borderStyle, borderColor);
                        }
                    });
                }
            }

        } catch (error) {
            console.warn('应用边框失败:', error);
        }
    },

    /**
     * 在合并单元格之后应用边框
     * 这确保边框正确覆盖整个合并区域
     */
    async applyBordersAfterMerge(activeSheet, univerData) {
        try {

            // 遍历所有单元格，找到有边框样式的单元格
            for (const [rowIndex, rowData] of Object.entries(univerData.cellData)) {
                for (const [colIndex, cellData] of Object.entries(rowData)) {
                    if (cellData.s && univerData.styles[cellData.s]) {
                        const styleData = univerData.styles[cellData.s];

                        // 只处理有边框的样式
                        if (styleData.bd) {
                            const row = parseInt(rowIndex);
                            const col = parseInt(colIndex);

                            // 检查这个单元格是否是合并单元格的一部分
                            const mergedRange = this.findMergedRangeForCell(activeSheet, row, col, univerData.mergeData);

                            if (mergedRange) {
                                // 如果是合并单元格，应用边框到整个合并区域
                                const range = this.api.safeCall(() => {
                                    return activeSheet.getRange(
                                        mergedRange.startRow,
                                        mergedRange.startColumn,
                                        mergedRange.endRow - mergedRange.startRow + 1,
                                        mergedRange.endColumn - mergedRange.startColumn + 1
                                    );
                                });

                                if (range) {
                                    await this.applyBordersToRange(range, styleData.bd);
                                }
                            } else {
                                // 如果不是合并单元格，正常应用边框
                                const range = this.api.safeCall(() => {
                                    return activeSheet.getRange(row, col, 1, 1);
                                });

                                if (range) {
                                    await this.applyBordersToRange(range, styleData.bd);
                                }
                            }
                        }
                    }
                }
            }



        } catch (error) {
            console.warn('合并后应用边框失败:', error);
        }
    },

    /**
     * 查找指定单元格所属的合并区域
     */
    findMergedRangeForCell(activeSheet, row, col, mergeData) {
        for (const mergeRange of mergeData) {
            if (row >= mergeRange.startRow && row <= mergeRange.endRow &&
                col >= mergeRange.startColumn && col <= mergeRange.endColumn) {
                return mergeRange;
            }
        }
        return null;
    }
});

// 继续ExcelImporter类的其他方法
Object.assign(ExcelImporter.prototype, {
    async applyMergedCells(activeSheet, mergeData) {
        try {
            let successCount = 0;
            let failCount = 0;

            for (const merge of mergeData) {
                try {
                    // 验证合并范围
                    if (merge.startRow < 0 || merge.startColumn < 0 ||
                        merge.endRow < merge.startRow || merge.endColumn < merge.startColumn) {
                        throw new Error(`无效的合并范围: (${merge.startRow},${merge.startColumn}):(${merge.endRow},${merge.endColumn})`);
                    }

                    // 获取合并范围
                    const numRows = merge.endRow - merge.startRow + 1;
                    const numColumns = merge.endColumn - merge.startColumn + 1;

                    // 验证合并范围参数
                    if (numRows <= 0 || numColumns <= 0) {
                        throw new Error(`无效的合并范围大小: ${numRows}x${numColumns}`);
                    }

                    const range = this.api.safeGetRange(
                        activeSheet,
                        merge.startRow,
                        merge.startColumn,
                        numRows,
                        numColumns
                    );

                    if (range) {
                        // 执行合并
                        const merged = this.api.safeCall(() => {
                            if (typeof range.merge === 'function') {
                                range.merge(true);
                                return true;
                            }
                            return false;
                        });

                        if (merged) {
                            successCount++;
                        } else {
                            // 尝试备用方法
                            const backupMerged = this.api.safeCall(() => {
                                if (typeof activeSheet.mergeCells === 'function') {
                                    activeSheet.mergeCells(
                                        merge.startRow,
                                        merge.startColumn,
                                        merge.endRow,
                                        merge.endColumn
                                    );
                                    return true;
                                }
                                return false;
                            });

                            if (backupMerged) {
                                successCount++;
                            } else {
                                failCount++;
                            }
                        }
                    } else {
                        failCount++;
                    }
                } catch (error) {
                    console.warn('合并单元格失败:', error);
                    failCount++;
                }
            }


        } catch (error) {
            console.error('应用合并单元格失败:', error);
        }
    },

    async applyDimensions(activeSheet, univerData) {
        try {
            // 应用行高
            for (const [rowIndex, data] of Object.entries(univerData.rowData)) {
                if (data.h) {
                    this.api.safeCall(() => {
                        if (typeof activeSheet.setRowHeights === 'function') {
                            activeSheet.setRowHeights(parseInt(rowIndex), 1, data.h);
                        }
                    });
                }
            }

            // 应用列宽
            for (const [colIndex, data] of Object.entries(univerData.columnData)) {
                if (data.w) {
                    this.api.safeCall(() => {
                        if (typeof activeSheet.setColumnWidths === 'function') {
                            activeSheet.setColumnWidths(parseInt(colIndex), 1, data.w);
                        }
                    });
                }
            }
        } catch (error) {
            console.error('应用行高列宽失败:', error);
        }
    },

    /**
     * 应用图片到UniverJS工作表
     * @param {object} univerActiveSheet - UniverJS活动工作表对象
     * @param {Array} univerImages - 图片数据数组
     */
    async applyImages(univerActiveSheet, univerImages) {
        try {

            if (!univerImages || univerImages.length === 0) {
                return;
            }

            // 验证工作表对象
            if (!univerActiveSheet || typeof univerActiveSheet.newOverGridImage !== 'function') {
                console.warn('⚠️ 工作表对象无效或不支持图片插入功能');
                return;
            }

            const univerSheetImages = [];
            let univerSuccessCount = 0;
            let univerFailCount = 0;

            // 批量创建图片对象
            for (let i = 0; i < univerImages.length; i++) {
                const univerImageInfo = univerImages[i];

                try {
                    // 验证图片数据
                    if (!univerImageInfo.imageData || !univerImageInfo.imageData.source) {
                        univerFailCount++;
                        continue;
                    }

                    if (!univerImageInfo.position) {
                        univerFailCount++;
                        continue;
                    }

                    // 创建UniverJS图片构建器
                    const univerImageBuilder = univerActiveSheet.newOverGridImage();

                    // 设置图片源（BASE64格式）
                    univerImageBuilder.setSource(
                        univerImageInfo.imageData.source,
                        'BASE64' // 使用字符串而不是枚举，确保兼容性
                    );

                    // 获取图片位置和尺寸
                    const univerCol = univerImageInfo.position.col || 0;
                    const univerRow = univerImageInfo.position.row || 0;
                    const univerWidth = univerImageInfo.position.width || 100;
                    const univerHeight = univerImageInfo.position.height || 100;

                    // 设置图片位置和尺寸
                    univerImageBuilder
                        .setColumn(univerCol)
                        .setRow(univerRow)
                        .setWidth(univerWidth)
                        .setHeight(univerHeight);

                    // 构建图片对象
                    const univerSheetImage = await univerImageBuilder.buildAsync();
                    univerSheetImages.push(univerSheetImage);
                    univerSuccessCount++;
                } catch (error) {
                    univerFailCount++;
                }
            }

            // 批量插入图片到工作表
            if (univerSheetImages.length > 0) {
                try {
                    univerActiveSheet.insertImages(univerSheetImages);
                } catch (error) {
                    throw error;
                }
            }
        } catch (error) {
            throw error;
        }
    },

    async getExcelImageData(excelWorksheet, image) {
        try {
            const workbook = excelWorksheet.workbook;
            const media = workbook.media.find(m => m.index === image.imageId);

            if (!media) {
                return null;
            }

            const buffer = media.buffer;
            const extension = media.extension || 'png';
            const base64Data = buffer.toString('base64');
            const mimeType = this.getMimeTypeFromExtension(extension);
            const dataUrl = `data:${mimeType};base64,${base64Data}`;

            return {
                source: dataUrl,
                imageSourceType: 'BASE64',
                extension: extension,
                buffer: buffer
            };
        } catch (error) {
            console.warn('获取图片数据失败:', error);
            return null;
        }
    },

    convertExcelImagePosition(image) {
        try {
            if (!image.range) {
                console.warn('⚠️ 图片缺少range信息');
                return null;
            }

            const topLeft = image.range.tl || {};
            const bottomRight = image.range.br || {};

            // 获取锚点信息
            const tlCol = topLeft.nativeCol || 0;
            const tlRow = topLeft.nativeRow || 0;
            const tlColOff = topLeft.nativeColOff || 0;
            const tlRowOff = topLeft.nativeRowOff || 0;

            const brCol = bottomRight.nativeCol || 0;
            const brRow = bottomRight.nativeRow || 0;
            const brColOff = bottomRight.nativeColOff || 0;
            const brRowOff = bottomRight.nativeRowOff || 0;

            // 使用正确的算法计算图片物理尺寸
            const dimensions = this.getImageDimensions(image, this.currentWorksheet);

            if (!dimensions) {
                console.warn('⚠️ 无法计算图片尺寸，使用默认值');
                return {
                    row: tlRow,
                    col: tlCol,
                    width: 100,
                    height: 100
                };
            }

            return {
                row: tlRow,
                col: tlCol,
                width: Math.round(dimensions.width),
                height: Math.round(dimensions.height),
                // 保留原始数据用于调试
                _debug: {
                    tlCol, tlRow, tlColOff, tlRowOff,
                    brCol, brRow, brColOff, brRowOff,
                    calculatedWidth: dimensions.width,
                    calculatedHeight: dimensions.height,
                    algorithm: 'EMU_BASED_CALCULATION'
                }
            };
        } catch (error) {
            console.warn('转换图片位置失败:', error);
            return null;
        }
    },

    /**
     * 将Excel列宽（字符单位）转换为像素
     * @param {number} colWidth - Excel列宽（字符单位）
     * @returns {number} - 像素宽度
     */
    colWidthToPixels(colWidth) {
        // Excel默认列宽8.43字符，1字符≈8.2像素，padding≈5像素（与导出端保持一致）
        const width = colWidth === undefined ? 8.43 : colWidth;
        return Math.floor(8.2 * width + 5);
    },

    /**
     * 将Excel行高（磅单位）转换为像素
     * @param {number} rowHeight - Excel行高（磅单位）
     * @returns {number} - 像素高度
     */
    rowHeightToPixels(rowHeight) {
        // Excel默认行高15磅，1磅=1/72英寸，1英寸=96像素
        const height = rowHeight === undefined ? 15 : rowHeight;
        return Math.floor(height * 96 / 72);
    },

    /**
     * 根据图片对象和工作表信息计算图片的物理尺寸（以像素为单位）
     * 实现算法：遍历锚点所跨列/行，累加像素宽高，加终止偏移减起始偏移（EMU转像素）
     * @param {object} image - ExcelJS图片对象
     * @param {object} worksheet - ExcelJS工作表对象
     * @returns {object|null} - 包含width和height的对象（像素）
     */
    getImageDimensions(image, worksheet) {
        try {
            if (!image.range || !worksheet) {
                return null;
            }

            // EMU到像素的转换常量
            const EMU_PER_PIXEL = 9525;

            const tl = image.range.tl || {};
            const br = image.range.br || {};

            // 计算总宽度（像素）
            let totalWidthPx = 0;
            for (let col = tl.nativeCol; col < br.nativeCol; col++) {
                const column = worksheet.getColumn(col + 1); // ExcelJS使用1基索引
                totalWidthPx += this.colWidthToPixels(column.width);
            }
            totalWidthPx += (br.nativeColOff || 0) / EMU_PER_PIXEL;
            totalWidthPx -= (tl.nativeColOff || 0) / EMU_PER_PIXEL;

            // 计算总高度（像素）
            let totalHeightPx = 0;
            for (let row = tl.nativeRow; row < br.nativeRow; row++) {
                const excelRow = worksheet.getRow(row + 1); // ExcelJS使用1基索引
                totalHeightPx += this.rowHeightToPixels(excelRow.height);
            }
            totalHeightPx += (br.nativeRowOff || 0) / EMU_PER_PIXEL;
            totalHeightPx -= (tl.nativeRowOff || 0) / EMU_PER_PIXEL;

            return {
                width: Math.max(1, Math.round(totalWidthPx)),
                height: Math.max(1, Math.round(totalHeightPx))
            };
        } catch (error) {
            console.warn('计算图片尺寸失败:', error);
            return null;
        }
    },

    getMimeTypeFromExtension(extension) {
        const mimeMap = {
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'gif': 'image/gif',
            'bmp': 'image/bmp',
            'webp': 'image/webp'
        };
        return mimeMap[extension.toLowerCase()] || 'image/png';
    }
});

// 导出主要函数和类
export { importExcelToUniverJS as default };