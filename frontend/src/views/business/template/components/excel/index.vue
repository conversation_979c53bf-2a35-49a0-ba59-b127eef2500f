<template>
    <div ref="univerContainer" class="univer-instance"></div>
</template>

<script setup>
    import { onBeforeUnmount, onMounted, ref, watch } from 'vue';
    import { createUniver, defaultTheme, LocaleType } from '@univerjs/presets';
    import { UniverSheetsCorePreset } from '@univerjs/presets/preset-sheets-core';
    import UniverPresetSheetsCoreZhCN from '@univerjs/presets/preset-sheets-core/locales/zh-CN';
    import { UniverSheetsDrawingPreset } from '@univerjs/preset-sheets-drawing';
    import { CalculationMode } from '@univerjs/sheets-formula';
    import '@univerjs/presets/lib/styles/preset-sheets-core.css';

    // props: initJson 可选，初始json数据
    const props = defineProps({
        initJson: Object
    });

    // DOM 容器的引用
    const univerContainer = ref(null);
    // 暴露的API
    const univerAPI = ref(null);
    const univerWorkbook = ref(null);

    const emit = defineEmits(['ready', 'sheetChange']);

    // 生成唯一ID
    const generateUniqueId = () => {
        return `workbook_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    };

    // 为工作簿数据生成唯一ID
    const generateUniqueWorkbookData = (originalJson) => {
        const uniqueId = generateUniqueId();
        
        return {
            ...originalJson,
            id: uniqueId
        };
    };

    // 初始化Univer
    const initUniver = async (json) => {
        if (!univerContainer.value) return;
        // 清理旧实例
        if (univerAPI.value) {
            univerAPI.value.dispose();
            univerAPI.value = null;
            univerWorkbook.value = null;
        }
        // 创建新实例
        const result = createUniver({
            locale: LocaleType.ZH_CN,
            locales: {
                [LocaleType.ZH_CN]: {
                    ...UniverPresetSheetsCoreZhCN
                }
            },
            theme: defaultTheme,
            presets: [
                UniverSheetsCorePreset({
                    container: univerContainer.value,
                    // 添加公式配置，强制重新计算所有公式
                    formula: {
                        initialFormulaComputing: CalculationMode.FORCED
                    }
                }),
                UniverSheetsDrawingPreset()
            ]
        });
        univerAPI.value = result.univerAPI;
        
        // 创建或恢复工作簿，关键：使用唯一ID避免缓存冲突
        if (json) {
            const uniqueJson = generateUniqueWorkbookData(json);
            console.log('使用唯一ID创建工作簿:', uniqueJson.id);
            univerWorkbook.value = univerAPI.value.createWorkbook(uniqueJson);
        } else {
            // 默认工作簿也使用唯一ID
            const uniqueId = generateUniqueId();
            console.log('使用唯一ID创建默认工作簿:', uniqueId);
            univerWorkbook.value = univerAPI.value.createWorkbook({
                id: uniqueId,
                name: 'Excel工作簿',
                sheetOrder: ['sheet1'],
                styles: {
                    0: {
                        ff: 'SimSun',
                        fs: 11,
                        cl: { rgb: '#000000' }
                    }
                },
                sheets: {
                    sheet1: {
                        id: 'sheet1',
                        name: 'Sheet1',
                        cellData: {},
                        defaultStyle: '0'
                    }
                }
            });
        }
        
        // 设置sheet切换监听和初始状态
        setTimeout(() => {
            setupSheetChangeListener();
            updateSheetState();
        }, 100);
        
        emit('ready', univerAPI.value);
    };

    onMounted(() => {
        initUniver(props.initJson);
    });

    onBeforeUnmount(() => {
        if (univerAPI.value) {
            univerAPI.value.dispose();
            univerAPI.value = null;
            univerWorkbook.value = null;
        }
    });

    // 支持外部动态切换initJson
    watch(
        () => props.initJson,
        (val) => {
            if (val) {
                initUniver(val);
            }
        }
    );

    // 当前激活的sheet状态
    const currentSheetId = ref(null);
    const currentSheetIndex = ref(0);

    // 获取当前激活sheet的索引
    const getCurrentSheetIndex = () => {
        try {
            if (!univerAPI.value || !univerWorkbook.value) {
                return 0;
            }
            
            const workbook = univerAPI.value.getActiveWorkbook();
            if (!workbook) {
                return 0;
            }
            
            const activeSheet = workbook.getActiveSheet();
            if (!activeSheet) {
                return 0;
            }
            
            const sheetId = activeSheet.getSheetId();
            const workbookData = univerWorkbook.value.save();
            
            if (workbookData && workbookData.sheetOrder) {
                const index = workbookData.sheetOrder.indexOf(sheetId);
                return index >= 0 ? index : 0;
            }
            
            return 0;
        } catch (error) {
            console.warn('获取当前sheet索引失败:', error);
            return 0;
        }
    };

    // 获取当前激活sheet的ID
    const getCurrentSheetId = () => {
        try {
            if (!univerAPI.value) {
                return null;
            }
            
            const workbook = univerAPI.value.getActiveWorkbook();
            if (!workbook) {
                return null;
            }
            
            const activeSheet = workbook.getActiveSheet();
            if (!activeSheet) {
                return null;
            }
            
            return activeSheet.getSheetId();
        } catch (error) {
            console.warn('获取当前sheet ID失败:', error);
            return null;
        }
    };

    // 获取所有sheet信息
    const getAllSheets = () => {
        try {
            if (!univerWorkbook.value) {
                return [];
            }
            
            const workbookData = univerWorkbook.value.save();
            if (!workbookData || !workbookData.sheets || !workbookData.sheetOrder) {
                return [];
            }
            
            return workbookData.sheetOrder.map((sheetId, index) => {
                const sheet = workbookData.sheets[sheetId];
                return {
                    id: sheetId,
                    name: sheet ? sheet.name : `Sheet${index + 1}`,
                    index: index
                };
            });
        } catch (error) {
            console.warn('获取所有sheet信息失败:', error);
            return [];
        }
    };

    // 监听sheet切换事件
    const setupSheetChangeListener = () => {
        try {
            if (!univerAPI.value) {
                return;
            }
            
            // 监听工作表切换事件
            const workbook = univerAPI.value.getActiveWorkbook();
            if (workbook && workbook.onSelectionChange) {
                workbook.onSelectionChange(() => {
                    const newSheetId = getCurrentSheetId();
                    const newSheetIndex = getCurrentSheetIndex();
                    
                    if (newSheetId !== currentSheetId.value) {
                        currentSheetId.value = newSheetId;
                        currentSheetIndex.value = newSheetIndex;
                        
                        // 触发sheet切换事件
                        emit('sheetChange', {
                            sheetId: newSheetId,
                            sheetIndex: newSheetIndex,
                            allSheets: getAllSheets()
                        });
                    }
                });
            }
        } catch (error) {
            console.warn('设置sheet切换监听失败:', error);
        }
    };

    // 更新sheet状态
    const updateSheetState = () => {
        currentSheetId.value = getCurrentSheetId();
        currentSheetIndex.value = getCurrentSheetIndex();
    };

    // 暴露API给父组件
    defineExpose({
        univerAPI,
        univerWorkbook,
        getCurrentSheetIndex,
        getCurrentSheetId,
        getAllSheets,
        currentSheetId,
        currentSheetIndex
    });
</script>

<style scoped>
    .univer-instance {
        width: 100%;
        height: 100vh;
        overflow: hidden;
    }
</style>
