

现在来处理导入的图片，下面是exceljs的一种图片格式（也是我这里常用到的）
参考项目根目录的 exceljs源码和Univer源码来处理图片导入的问题

{"image_0kx6l_1750672834506":{"src":"data:image/png;base64,iVBxxxxxxxxx==","fromCol":0,"fromColOff":0,"fromRow":0,"fromRowOff":0,"toCol":1,"toColOff":"60.93333333333333","toRow":2,"toRowOff":"2.933333333333333","originWidth":"131.93333333333334","originHeight":"48.93333333333333","type":"2","isFixedPos":false,"fixedLeft":0,"fixedTop":0,"border":{"color":"#000","radius":0,"style":"solid","width":0},"crop":{"height":"48.93333333333333","offsetLeft":0,"offsetTop":0,"width":"131.93333333333334"},"default":{"height":"48.93333333333333","left":0,"top":0,"width":"131.93333333333334"}}}


注意：交付干净、专业的生产代码，确保用户界面简洁，功能稳定可靠。所有开发和测试活动都应该通过真实的业务场景完成，不污染最终产品。
