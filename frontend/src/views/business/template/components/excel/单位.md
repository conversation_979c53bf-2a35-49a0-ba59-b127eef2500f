# Excel、ExcelJS 与 UniverJS 中的单位与转换原理

## 第一章：单位原理的基石 (The Rosetta Stone)

要理解不同系统间的转换，首先必须掌握所有单位的“通用货币”——**EMU (English Metric Unit)**。Excel (Office OpenXML) 创造这个单位的初衷，就是为了在不同度量衡（公制、英制）和不同显示环境（打印、屏幕）之间，使用整数进行无损、精确的转换。

| 单位 (Unit)  | 英文/缩写           | 核心原理和用途                                                                                                                                                                                         |
| :----------- | :------------------ | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **EMU**      | English Metric Unit | **绝对物理单位**，是所有尺寸和位置计算的底层基础。它与设备、分辨率（DPI）完全无关。可以将它想象成物理世界里固定不变的“纳米”。<br>**关键换算率：**<br>● `1 英寸 = 914,400 EMU`<br>● `1 磅 = 12,700 EMU` |
| **磅**       | Point (pt)          | 印刷业的传统单位，`1 磅 = 1/72 英寸`。 在 Excel 中，它是一个对用户非常友好的、常用的物理单位，主要用于**行高**和**字体大小**。                                                                         |
| **像素**     | Pixel (px)          | 屏幕显示的最小单位，是**相对单位**，其物理大小依赖于屏幕的DPI（每英寸点数）。在 Web 环境下，所有渲染都基于像素。标准约定下，`1 英寸 ≈ 96 像素`（在 96 DPI 时）。                                       |
| **字符单位** | Character Unit      | **Excel 列宽的独有单位**，这是最特殊、最易混淆的单位。它**不是一个固定的物理长度**，其定义为“在工作簿‘常规’样式下，0-9 这10个数字中最宽的那个字符的像素宽度”。 因此，它会随默认字体的改变而改变。      |

---

## 第二章：四大元素的原理剖析

### 1. 行高 (Row Height)

| 系统                     | 存储/访问方式             | 单位              | 转换原理 (以 ExcelJS 为中转)                                               |
| :----------------------- | :------------------------ | :---------------- | :------------------------------------------------------------------------- |
| **Excel 文件 (OpenXML)** | XML 属性 `ht`             | **磅 (Points)**   | (源头) `ht="15"` 表示 15 磅。                                              |
| **ExcelJS 对象**         | `row.height`              | **磅 (Points)**   | **直接映射**。`exceljs` 读取 `ht` 属性后，直接将其作为 `row.height` 的值。 |
| **UniverJS / Web 环境**  | 核心模型，如 `IRowData.h` | **像素 (Pixels)** | UniverJS 作为 Canvas 渲染引擎，其内部模型以像素为基础，便于渲染。          |

#### 转换原理

- **磅 → 像素**: 基于 `1 磅 = 1/72 英寸` 和 `1 英寸 = 96 像素` (标准DPI) 的关系。
  `像素值 = 磅值 * (96 / 72)`
- **像素 → 磅**:
  `磅值 = 像素值 * (72 / 96)`

### 2. 列宽 (Column Width)

| 系统                     | 存储/访问方式                | 单位              | 转换原理 (以 ExcelJS 为中转)                                                  |
| :----------------------- | :--------------------------- | :---------------- | :---------------------------------------------------------------------------- |
| **Excel 文件 (OpenXML)** | XML 属性 `width`             | **字符单位**      | (源头) 这是最复杂的部分。                                                     |
| **ExcelJS 对象**         | `column.width`               | **字符单位**      | **直接映射**。`exceljs` 读取 XML 中的 `width` 后，直接赋值给 `column.width`。 |
| **UniverJS / Web 环境**  | 核心模型，如 `IColumnData.w` | **像素 (Pixels)** | 同样，为了渲染方便，UniverJS 模型使用像素。                                   |

#### 转换原理

这是最复杂的转换，必须使用 EMU 作为桥梁。

1.  **字符单位 → 像素 (正向)**:
    a. **`字符 → EMU`**: 这一步的精确公式考虑了字符本身的宽度和单元格的内边距。公式大致为： `像素宽度 = ((字符数 * 标准字符像素宽度) + 内边距像素) `，然后再将像素转为 EMU。
    b. **`EMU → 像素`**: 这是一个简单的除法。 `像素值 = EMU值 / 9525` (其中 `9525 = 914400 EMU / 96 DPI`)。
2.  **像素 → 字符单位 (逆向)**:
    a. **`像素 → EMU`**: `EMU值 = 像素值 * 9525`。
    b. **`EMU → 字符`**: 使用上述精确公式的反向运算。

### 3. 字体大小 (Font Size)

| 系统                     | 存储/访问方式                | 单位            | 转换原理 (以 ExcelJS 为中转)                                                 |
| :----------------------- | :--------------------------- | :-------------- | :--------------------------------------------------------------------------- |
| **Excel 文件 (OpenXML)** | XML 属性 `sz`                | **磅 (Points)** | (源头) 简单直接。                                                            |
| **ExcelJS 对象**         | `cell.font.size`             | **磅 (Points)** | **直接映射**。                                                               |
| **UniverJS / Web 环境**  | 核心模型，如 `IStyleData.fs` | **磅 (Points)** | UniverJS 在字体大小上通常也遵循 CSS 约定，可以使用磅 (`pt`)，与 Excel 对齐。 |

#### 转换原理

- **几乎是 1:1 直接赋值**。这是所有单位中最简单、最直接的转换，因为两端都原生支持并使用“磅”作为字体大小的度量。

### 4. 图片大小及位置 (Image Size & Position)

| 系统                     | 存储/访问方式                                                                                                        | 单位              | 转换原理 (以 ExcelJS 为中转)             |
| :----------------------- | :------------------------------------------------------------------------------------------------------------------- | :---------------- | :--------------------------------------- |
| **Excel 文件 (OpenXML)** | **两个锚点** (`<xdr:from>`, `<xdr:to>`)。每个锚点包含 `(列, 行)` 索引和 **EMU** 偏移量。**大小是隐式的**，需要计算。 | **EMU**           | (源头) 这就是您最初问题的根源。          |
| **ExcelJS 对象**         | `image.range` 对象，忠实反映 OpenXML 的结构。大小**仍是隐式的**。                                                    | **EMU**           | **直接映射** OpenXML 的锚点数据。        |
| **UniverJS / Web 环境**  | **显式的位置和大小**，如 `{ x, y, width, height }` 对象。                                                            | **像素 (Pixels)** | 必须是显式的，便于直接在 Canvas 上绘制。 |

#### 转换原理

1.  **ExcelJS → UniverJS (正向计算)**:
    a. **计算总宽高 (EMU)**: 必须遍历图片所跨越的所有行列。`总宽度EMU = (所有完整列宽之和EMU) + (终点列偏移EMU) - (起点列偏移EMU)`。行高同理。
    b. **计算绝对位置 (EMU)**: `图片X坐标EMU = (起点锚点之前所有列宽之和EMU) + (起点列偏移EMU)`。Y 坐标同理。
    c. **`EMU → 像素`**: 将计算出的总宽高和绝对位置（单位均为 EMU）全部除以 `9525`，得到像素值。
2.  **UniverJS → ExcelJS (逆向计算)**:
    a. **`像素 → EMU`**: 将 UniverJS 提供的图片 x, y, width, height（单位均为像素）全部乘以 `9525`，得到 EMU 值。
    b. **定位锚点**: 这是一个“寻路”过程。通过不断累加列宽（EMU），找到图片左右边缘所在的列及在该列内的偏移量，从而反向构造出两个锚点的信息。行定位同理。

---

## 第三章：总结与对比

| 元素     | 在 Excel 中如何思考          | 在 Web (UniverJS) 中如何思考       | 转换的关键挑战                                                                         |
| :------- | :--------------------------- | :--------------------------------- | :------------------------------------------------------------------------------------- |
| **行高** | “这个高度等于多少磅？”       | “这个高度占多少像素？”             | 简单的数学比例换算                                                                     |
| **列宽** | “这一列能放下几个标准数字？” | “这一列占多少像素？”               | **单位不匹配**：必须通过 EMU 作为中间语言进行复杂的公式转换。                          |
| **字体** | “字号是多少磅？”             | “字号是多少磅/像素？”              | **几乎无挑战**：两边都原生支持磅单位。                                                 |
| **图片** | “它的角钉在哪两个单元格？”   | “它的左上角坐标和宽高是多少像素？” | **思维模式不匹配**：从“基于单元格的相对锚定”到“基于画布的绝对坐标和尺寸”的根本性转变。 |
