<!--
  * 标书生成表单组件
-->
<template>
  <div class="tender-form-container">
    <a-card class="form-card" :bordered="false">
      <template #title>
        <div class="card-title">
          <InfoCircleOutlined />
          <span>基本信息</span>
        </div>
      </template>
      <a-form :model="form" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="标书标题" name="title">
          <a-input 
            v-model:value="form.title" 
            placeholder="请输入标书标题" 
            :maxLength="100"
            show-count
          />
        </a-form-item>
        
        <a-form-item label="标书类型" name="tenderType" required>
          <a-select 
            v-model:value="form.tenderType" 
            placeholder="请选择标书类型"
            @change="handleTenderTypeChange"
          >
            <a-select-option 
              v-for="(item, key) in TENDER_TYPE_ENUM" 
              :key="key" 
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="项目名称" name="projectName">
          <a-input 
            v-model:value="form.projectName" 
            placeholder="请输入项目名称" 
            :maxLength="100"
            show-count
          />
        </a-form-item>

        <a-form-item label="招标编号" name="tenderNo">
          <a-input 
            v-model:value="form.tenderNo" 
            placeholder="请输入招标编号" 
            :maxLength="50"
          />
        </a-form-item>

        <a-form-item label="投标截止时间" name="deadline">
          <a-date-picker 
            v-model:value="form.deadline" 
            show-time 
            placeholder="请选择投标截止时间"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="预估金额" name="estimatedAmount">
          <a-input-number 
            v-model:value="form.estimatedAmount" 
            placeholder="请输入预估金额"
            :min="0"
            :precision="2"
            style="width: 100%"
            addon-after="元"
          />
        </a-form-item>
      </a-form>
    </a-card>

    <a-card title="技术文档上传" class="form-card" :bordered="false">
      <template #title>
        <div class="card-title">
          <UploadOutlined />
          <span>技术文档上传</span>
        </div>
      </template>
      <div class="upload-area">
        <FileUpload
          ref="fileUploadRef"
          v-model:value="form.fileIds"
          :defaultFileList="fileList"
          :accept="acceptFileTypes"
          :folder="FILE_FOLDER_TYPE_ENUM.COMMON.value"
          buttonText="上传文档"
          listType="text"
          :maxSize="20"
          @change="handleFileChange"
        />
        <div class="file-tips">
          <InfoCircleOutlined />
          <span>支持上传 .md, .txt, .docx 格式文件，单个文件大小不超过20MB</span>
        </div>
      </div>
    </a-card>

    <a-card class="form-card" :bordered="false">
      <template #title>
        <div class="card-title">
          <FormOutlined />
          <span>AI 提示词</span>
        </div>
      </template>
      <div class="prompt-area">
        <a-textarea
          v-model:value="form.prompt"
          placeholder="请详细描述标书要求、项目背景、技术需求、商务条件等。"
          :auto-size="{ minRows: 8, maxRows: 15 }"
          :maxLength="3000"
          show-count
          class="prompt-textarea"
        />
        <div class="prompt-templates">
          <span class="template-title">提示词模板：</span>
          <a-dropdown>
            <a-button type="primary" size="small" ghost>
              选择模板
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleTemplateSelect">
                <a-menu-item v-for="(template, index) in TENDER_PROMPT_TEMPLATES" :key="index">
                  {{ template.name }}
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </a-card>

    <div class="form-actions">
      <a-button 
        type="primary" 
        :loading="generating" 
        @click="generateTender" 
        :disabled="!isFormValid"
        size="large"
        class="generate-button"
      >
        <template #icon><ThunderboltOutlined /></template>
        {{ generating ? '生成中...' : '生成标书' }}
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { message } from 'ant-design-vue';
import {
  InfoCircleOutlined,
  DownOutlined,
  UploadOutlined,
  FormOutlined,
  ThunderboltOutlined
} from '@ant-design/icons-vue';
import { FILE_FOLDER_TYPE_ENUM } from '/@/constants/support/file-const';
import { TENDER_TYPE_ENUM, TENDER_PROMPT_TEMPLATES, TENDER_FILE_TYPES } from '/@/constants/business/tender-const';
import FileUpload from '/@/components/support/file-upload/index.vue';
import { tenderApi } from '/@/api/business/tender-api';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { smartSentry } from '/@/lib/smart-sentry';

// 定义事件
const emit = defineEmits(['tender-generated']);

// 组件状态
const fileUploadRef = ref(null);
const fileList = ref([]);
const generating = ref(false);
const acceptFileTypes = TENDER_FILE_TYPES;

// 表单数据
const form = reactive({
  prompt: '',
  fileIds: '',
  title: '标书文档',
  tenderType: '',
  projectName: '',
  tenderNo: '',
  deadline: null,
  estimatedAmount: null
});

// 计算属性：表单是否有效
const isFormValid = computed(() => {
  return form.prompt && form.tenderType && form.fileIds;
});

// 文件变化处理
function handleFileChange(fileList) {
  // 将文件列表转换为逗号分隔的文件ID字符串
  if (Array.isArray(fileList) && fileList.length > 0) {
    form.fileIds = fileList.map(file => file.fileId).join(',');
  } else {
    form.fileIds = '';
  }
}

// 标书类型变化处理
function handleTenderTypeChange(value) {
  // 如果有对应类型的模板，自动填充提示词
  const template = TENDER_PROMPT_TEMPLATES.find(t => t.type === value);
  if (template && !form.prompt) {
    form.prompt = template.content;
  }
}

// 模板选择处理
function handleTemplateSelect({ key }) {
  const template = TENDER_PROMPT_TEMPLATES[key];
  if (template) {
    form.prompt = template.content;
    message.success(`已应用${template.name}`);
  }
}

// 生成标书
async function generateTender() {
  if (!isFormValid.value) {
    message.warning('请填写所有必填项');
    return;
  }

  try {
    generating.value = true;
    SmartLoading.show();

    // 调用生成API，不再传递apiKey参数
    const res = await tenderApi.generateTender({
      prompt: form.prompt,
      fileIds: form.fileIds,
      title: form.title,
      tenderType: form.tenderType,
      projectName: form.projectName,
      tenderNo: form.tenderNo,
      deadline: form.deadline,
      estimatedAmount: form.estimatedAmount
    });

    if (res.ok) {
      message.success('标书生成成功');
      // 触发事件，通知父组件更新
      emit('tender-generated', res.data);
    } else {
      message.error(res.msg || '生成失败');
    }
  } catch (e) {
    console.error('生成标书出错:', e);
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    generating.value = false;
    SmartLoading.hide();
  }
}

// 清空表单
function resetForm() {
  form.prompt = '';
  form.fileIds = '';
  form.title = '标书文档';
  form.tenderType = '';
  form.projectName = '';
  form.tenderNo = '';
  form.deadline = null;
  form.estimatedAmount = null;
  fileList.value = [];
  if (fileUploadRef.value) {
    fileUploadRef.value.clear();
  }
}

// 定义组件对外暴露的方法
defineExpose({
  resetForm,
  generateTender
});
</script>

<style scoped lang="less">
.tender-form-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding-right: 16px;

  .form-card {
    margin-bottom: 16px;
    border-radius: 8px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    
    &:hover {
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    }
    
    :deep(.ant-card-head) {
      min-height: 48px;
      padding: 0 16px;
      border-bottom: 1px solid #f0f0f0;
      
      .ant-card-head-title {
        padding: 12px 0;
      }
    }
    
    :deep(.ant-card-body) {
      padding: 16px;
    }
  }
  
  .card-title {
    display: flex;
    align-items: center;
    font-weight: 500;
    
    .anticon {
      margin-right: 8px;
      color: #1890ff;
    }
  }

  .upload-area {
    .file-tips {
      margin-top: 10px;
      color: #999;
      font-size: 12px;
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background-color: #f9f9f9;
      border-radius: 4px;

      .anticon {
        margin-right: 6px;
        color: #1890ff;
      }
    }
  }

  .prompt-area {
    .prompt-textarea {
      border-radius: 6px;
      resize: none;
      
      &:focus {
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
    
    .prompt-templates {
      margin-top: 12px;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .template-title {
        margin-right: 8px;
        color: #666;
      }
    }
  }

  .form-actions {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    
    .generate-button {
      min-width: 140px;
      font-weight: 500;
      border-radius: 6px;
      height: 40px;
      transition: all 0.3s;
      
      &:not(:disabled):hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25);
      }
    }
  }
}
</style>
