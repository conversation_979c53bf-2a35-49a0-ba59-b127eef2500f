<!--
  * 标书历史记录
-->
<template>
  <div class="tender-history">
    <a-modal
      v-model:open="visible"
      title="历史标书记录"
      width="800px"
      :footer="null"
      @cancel="handleCancel"
    >
      <div class="history-content">
        <div class="history-header">
          <a-input-search
            v-model:value="params.keyword"
            placeholder="搜索标书标题"
            style="width: 250px"
            @search="handleSearch"
          />
          <a-button type="primary" danger @click="handleBatchDelete" :disabled="!selectedRowKeys.length">
            批量删除
          </a-button>
        </div>

        <a-table
          :columns="columns"
          :data-source="tableData"
          :pagination="false"
          :loading="loading"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          row-key="id"
          size="small"
          style="margin-top: 16px"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(record.status)">{{ getStatusText(record.status) }}</a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'createTime'">
              {{ text }}
            </template>
            <template v-else-if="column.dataIndex === 'action'">
              <a-space>
                <a @click="handleView(record)">查看</a>
                <a-divider type="vertical" />
                <a-popconfirm
                  title="确定要删除这条记录吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="handleDelete(record.id)"
                >
                  <a>删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>

        <div class="pagination-wrapper" v-if="total > 0">
          <a-pagination
            v-model:current="params.pageNum"
            v-model:page-size="params.pageSize"
            :total="total"
            :show-size-changer="true"
            :show-quick-jumper="true"
            :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
            @change="queryHistory"
            @show-size-change="queryHistory"
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { createVNode } from 'vue';
import { tenderApi } from '/@/api/business/tender-api';
import { TENDER_STATUS_ENUM } from '/@/constants/business/tender-const';
import { PAGE_SIZE, PAGE_SIZE_OPTIONS, showTableTotal } from '/@/constants/common-const';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { smartSentry } from '/@/lib/smart-sentry';

// 组件状态
const visible = ref(false);
const loading = ref(false);
const tableData = ref([]);
const total = ref(0);
const selectedRowKeys = ref([]);

// 查询参数
const params = reactive({
  pageNum: 1,
  pageSize: PAGE_SIZE,
  keyword: ''
});

// 表格列配置
const columns = [
  {
    title: '标题',
    dataIndex: 'title',
    ellipsis: true
  },
  {
    title: '创建人',
    dataIndex: 'creatorName'
  },
  {
    title: '状态',
    dataIndex: 'status'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime'
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120
  }
];

// 获取状态文字
function getStatusText(status) {
  const statusMap = {
    'PROCESSING': '处理中',
    'COMPLETED': '已完成',
    'FAILED': '失败',
    'SUBMITTED': '已提交'
  };
  return statusMap[status] || status;
}

// 获取状态颜色
function getStatusColor(status) {
  const colorMap = {
    'PROCESSING': 'blue',
    'COMPLETED': 'green',
    'FAILED': 'red',
    'SUBMITTED': 'default'
  };
  return colorMap[status] || '';
}

// 显示弹窗
function show() {
  visible.value = true;
  queryHistory();
}

// 隐藏弹窗
function handleCancel() {
  visible.value = false;
}

// 搜索
function handleSearch() {
  params.pageNum = 1;
  queryHistory();
}

// 选择行变更
function onSelectChange(keys) {
  selectedRowKeys.value = keys;
}

// 查看标书
function handleView(record) {
  emit('view-tender', record);
  visible.value = false;
}

// 删除单个标书
async function handleDelete(id) {
  try {
    SmartLoading.show();
    const res = await tenderApi.deleteTender(id);
    if (res.ok) {
      message.success('删除成功');
      queryHistory();
    } else {
      message.error(res.msg || '删除失败');
    }
  } catch (e) {
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    SmartLoading.hide();
  }
}

// 批量删除
function handleBatchDelete() {
  if (selectedRowKeys.value.length === 0) {
    return;
  }

  Modal.confirm({
    title: '确认删除',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个标书吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        SmartLoading.show();
        const res = await tenderApi.batchDeleteTender(selectedRowKeys.value);
        if (res.ok) {
          message.success('批量删除成功');
          selectedRowKeys.value = [];
          queryHistory();
        } else {
          message.error(res.msg || '批量删除失败');
        }
      } catch (e) {
        smartSentry.captureError(e);
        message.error('系统异常，请稍后重试');
      } finally {
        SmartLoading.hide();
      }
    }
  });
}

// 查询历史记录
async function queryHistory() {
  try {
    loading.value = true;
    const res = await tenderApi.queryTender({
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      keyword: params.keyword
    });

    if (res.ok) {
      tableData.value = res.data.list || [];
      total.value = res.data.total || 0;
    } else {
      message.error(res.msg || '查询失败');
    }
  } catch (e) {
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    loading.value = false;
  }
}

// 暴露方法给父组件
defineExpose({
  show
});
</script>

<style scoped lang="less">
.tender-history {
  .history-content {
    .history-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .pagination-wrapper {
      margin-top: 16px;
      text-align: center;
    }
  }
}
</style>
