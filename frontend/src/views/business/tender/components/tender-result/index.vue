<!--
  * 标书结果展示
-->
<template>
  <div class="tender-result">
    <!-- 结果为空时的提示 -->
    <div v-if="!tender || !tender.id" class="empty-result">
      <a-empty description="请在左侧填写表单并点击「生成标书」按钮" />
    </div>

    <!-- 结果展示 -->
    <div v-else class="result-content">
      <div class="result-header">
        <div class="result-title">
          <a-typography-title :level="5">{{ tender.title || '标书文档' }}</a-typography-title>
          <a-tag
            :color="tender.status === 'COMPLETED' ? 'success' : (tender.status === 'FAILED' ? 'error' : 'processing')"
            class="status-tag"
          >
            {{ getStatusText(tender.status) }}
          </a-tag>
        </div>
        <div class="result-actions">
          <a-tooltip title="目录">
            <a-button type="text" @click="toggleToc" :disabled="!tender.content" shape="circle">
              <template #icon><UnorderedListOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="复制内容">
            <a-button type="text" @click="copyContent" :disabled="!tender.content" shape="circle">
              <template #icon><CopyOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="下载文档">
            <a-button type="text" @click="downloadContent" :disabled="!tender.content" shape="circle">
              <template #icon><DownloadOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="重新生成">
            <a-button type="text" @click="retryGenerate" shape="circle">
              <template #icon><ReloadOutlined /></template>
            </a-button>
          </a-tooltip>
        </div>
      </div>

      <!-- 目录侧边栏 -->
      <div v-if="showToc && tender.content" class="toc-sidebar">
        <div class="toc-header">
          <span>目录</span>
          <a-button type="text" size="small" @click="toggleToc">
            <template #icon><CloseOutlined /></template>
          </a-button>
        </div>
        <div class="toc-content">
          <div v-for="(item, index) in tocItems" :key="index"
               :class="['toc-item', `toc-level-${item.level}`]"
               @click="scrollToHeading(item.id)">
            {{ item.text }}
          </div>
        </div>
      </div>


      <!-- 文档内容 -->
      <div class="markdown-content">
        <!-- 加载中 -->
        <div v-if="tender.status === 'PROCESSING'" class="processing-content">
          <a-spin :spinning="true" tip="AI正在生成标书，请稍候...">
            <div class="loading-content">
              <a-progress :percent="70" status="active" stroke-color="#1890ff" />
              <div class="loading-text">
                <div class="loading-message">分析文档中，这可能需要一点时间...</div>
              </div>
            </div>
          </a-spin>
        </div>

        <!-- 失败提示 -->
        <div v-else-if="tender.status === 'FAILED'" class="failed-content">
          <a-result
            status="error"
            title="生成失败"
            sub-title="标书生成失败，请重试或联系管理员"
          >
            <template #extra>
              <a-button type="primary" @click="retryGenerate">
                重新生成
              </a-button>
            </template>
          </a-result>
        </div>

        <!-- 成功结果 - 富文本编辑器区域 -->
        <div v-else-if="tender.content" class="rich-editor-content">
          <WangEditor
            v-model="editorContent"
            :height="500"
            @update:modelValue="handleEditorChange"
            ref="richEditor"
          />
        </div>

        <!-- 无内容 -->
        <div v-else class="empty-content">
          <a-empty description="暂无内容" />
        </div>
      </div>

      <!-- 编辑状态提示 -->
      <div v-if="isEdited && tender.content" class="edit-status">
        <a-alert type="info" show-icon banner>
          <template #message>
            <div class="edit-tip">
              <span>内容已修改，请点击<SaveOutlined class="save-icon" />按钮保存更改</span>
              <a-button type="primary" size="small" @click="saveContent">
                保存修改
              </a-button>
            </div>
          </template>
        </a-alert>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import {
  UnorderedListOutlined,
  CopyOutlined,
  DownloadOutlined,
  ReloadOutlined,
  CloseOutlined,
  SaveOutlined
} from '@ant-design/icons-vue';
import { TENDER_STATUS_ENUM } from '/@/constants/business/tender-const';
import { tenderApi } from '/@/api/business/tender-api';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { smartSentry } from '/@/lib/smart-sentry';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import WangEditor from '/@/components/framework/wangeditor/index.vue';

// 定义属性
const props = defineProps({
  tender: {
    type: Object,
    default: () => ({})
  }
});

// 组件事件
const emit = defineEmits(['retry', 'content-updated']);

// 状态
const showToc = ref(false);
const isEdited = ref(false);
const originalContent = ref('');
const editorContent = ref('');
const richEditor = ref(null);
const tocItems = ref([]);

// 监听tender变化，重置编辑状态和富文本内容
watch(() => props.tender, (newVal) => {
  if (newVal) {
    // 更新内容状态
    if (newVal.content) {
      originalContent.value = newVal.content;
      // 将Markdown内容转换为HTML并设置到编辑器
      try {
        const rawHtml = marked(newVal.content);
        const safeHtml = DOMPurify.sanitize(rawHtml);
        nextTick(() => {
          editorContent.value = safeHtml;
          isEdited.value = false;
          generateToc(newVal.content);
        });
      } catch (error) {
        console.error('HTML渲染错误:', error);
        editorContent.value = `<div class="markdown-error">渲染错误: ${error.message}</div>`;
      }
    }
  }
}, { deep: true, immediate: true });

// 获取状态文字
function getStatusText(status) {
  const statusMap = {
    'PROCESSING': '处理中',
    'COMPLETED': '已完成',
    'FAILED': '失败',
    'SUBMITTED': '已提交'
  };
  return statusMap[status] || status;
}

// 生成目录
function generateToc(content) {
  if (!content) {
    tocItems.value = [];
    return;
  }

  const headings = content.match(/^#{1,6}\s+.+$/gm);
  if (!headings) {
    tocItems.value = [];
    return;
  }

  tocItems.value = headings.map((heading, index) => {
    const level = heading.match(/^#+/)[0].length;
    const text = heading.replace(/^#+\s+/, '');
    const id = `heading-${index}`;
    return { level, text, id };
  });
}

// 切换目录显示
function toggleToc() {
  showToc.value = !showToc.value;
}

// 滚动到指定标题
function scrollToHeading(id) {
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
}

// 编辑器内容变化处理
function handleEditorChange(content) {
  isEdited.value = content !== originalContent.value;
}

// 保存内容
async function saveContent() {
  try {
    SmartLoading.show();

    const res = await tenderApi.updateTender({
      id: props.tender.id,
      content: editorContent.value
    });

    if (res.ok) {
      message.success('保存成功');
      originalContent.value = editorContent.value;
      isEdited.value = false;
      emit('content-updated', res.data);
    } else {
      message.error(res.msg || '保存失败');
    }
  } catch (e) {
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    SmartLoading.hide();
  }
}

// 下载文档
function downloadContent() {
  if (!props.tender.content) {
    message.warning('暂无内容可下载');
    return;
  }

  try {
    // 创建Word格式的HTML内容
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${props.tender.title || '标书文档'}</title>
        <style>
          body { font-family: SimSun, sans-serif; margin: 20px; }
          h1 { text-align: center; font-size: 18pt; }
          h2 { margin-top: 20px; border-bottom: 1px solid #ddd; padding-bottom: 5px; font-size: 14pt; }
          h3 { font-size: 12pt; }
          table { width: 100%; border-collapse: collapse; margin: 15px 0; }
          table, th, td { border: 1px solid #000; }
          th, td { padding: 8px; text-align: left; }
        </style>
      </head>
      <body>
        <h1>${props.tender.title || '标书文档'}</h1>
        <div>${marked(props.tender.content)}</div>
      </body>
      </html>
    `;

    // 创建Blob对象
    const blob = new Blob([htmlContent], { type: 'application/msword' });

    // 创建下载链接
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${props.tender.title || '标书文档'}.doc`;

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    message.success('下载成功');
  } catch (error) {
    console.error('下载失败:', error);
    smartSentry.captureError(error);
    message.error('下载失败，请稍后重试');
  }
}

// 复制内容
async function copyContent() {
  if (!props.tender.content) {
    message.warning('暂无内容可复制');
    return;
  }

  try {
    await navigator.clipboard.writeText(props.tender.content);
    message.success('内容已复制到剪贴板');
  } catch (e) {
    console.error('复制失败:', e);
    message.error('复制失败');
  }
}

// 重新生成
function retryGenerate() {
  emit('retry');
}
</script>

<style scoped lang="less">
.tender-result {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;

  .empty-result {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
  }

  .result-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

    .result-header {
      padding: 16px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .result-title {
        display: flex;
        align-items: center;

        :deep(.ant-typography) {
          margin-bottom: 0;
          margin-right: 10px;
          color: #0050b3;
        }

        .status-tag {
          font-weight: 500;
          border-radius: 4px;
        }
      }

      .result-actions {
        display: flex;
        gap: 4px;

        :deep(.ant-btn) {
          color: #1890ff;

          &:hover {
            color: #40a9ff;
            background-color: rgba(24, 144, 255, 0.1);
          }

          &:disabled {
            color: rgba(0, 0, 0, 0.25);
          }
        }
      }
    }

    .toc-sidebar {
      position: absolute;
      top: 60px;
      right: 16px;
      width: 250px;
      max-height: 400px;
      background: #fff;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 10;

      .toc-header {
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 500;
        background: #fafafa;
      }

      .toc-content {
        max-height: 300px;
        overflow-y: auto;
        padding: 8px 0;

        .toc-item {
          padding: 6px 16px;
          cursor: pointer;
          font-size: 13px;
          line-height: 1.4;

          &:hover {
            background: #f5f5f5;
          }

          &.toc-level-1 {
            font-weight: 500;
            color: #333;
          }

          &.toc-level-2 {
            padding-left: 24px;
            color: #666;
          }

          &.toc-level-3 {
            padding-left: 32px;
            color: #999;
          }
        }
      }
    }

    .markdown-content {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .processing-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;

        .loading-content {
          text-align: center;

          .loading-text {
            margin-top: 16px;

            .loading-message {
              color: #666;
              font-size: 14px;
            }
          }
        }
      }

      .failed-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .rich-editor-content {
        flex: 1;
        padding: 16px;
        overflow: hidden;

        :deep(.w-e-text-container) {
          height: 100% !important;
        }
      }

      .empty-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .edit-status {
      padding: 0 16px 16px;

      .edit-tip {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .save-icon {
          margin: 0 4px;
          color: #1890ff;
        }
      }
    }
  }
}
</style>
