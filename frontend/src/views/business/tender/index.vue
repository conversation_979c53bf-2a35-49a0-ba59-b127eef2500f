<!--
  * AI标书生成主页面
-->
<template>
  <div class="tender-container">
    <a-card class="tender-header">
      <div class="header-content">
        <div class="header-title">
          <a-typography-title :level="4">AI标书生成</a-typography-title>
        </div>
      </div>
    </a-card>

    <div class="tender-content">
      <!-- 左侧表单 -->
      <div class="form-container">
        <TenderForm ref="formRef" @tender-generated="handleTenderGenerated" />
      </div>

      <!-- 右侧结果 -->
      <div class="result-container">
        <TenderResult :tender="currentTender" @retry="handleRetry" />
      </div>
    </div>

    <!-- 历史记录模态框保留，但不在主界面显示按钮 -->
    <TenderHistory ref="historyRef" @view-tender="handleViewTender" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import TenderForm from './components/tender-form/index.vue';
import TenderResult from './components/tender-result/index.vue';
import TenderHistory from './components/tender-history/index.vue';
import { tenderApi } from '/@/api/business/tender-api';
import { message } from 'ant-design-vue';
import { smartSentry } from '/@/lib/smart-sentry';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { useRouter } from 'vue-router';

// 获取router实例
const router = useRouter();

// 组件引用
const formRef = ref(null);
const historyRef = ref(null);

// 当前选中的标书
const currentTender = ref({});

// 处理标书生成
function handleTenderGenerated(tender) {
  currentTender.value = tender;
}

// 处理查看历史标书
async function handleViewTender(tender) {
  try {
    SmartLoading.show();
    const res = await tenderApi.getTenderDetail(tender.id);
    if (res.ok) {
      currentTender.value = res.data;
    } else {
      message.error(res.msg || '获取标书详情失败');
    }
  } catch (e) {
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    SmartLoading.hide();
  }
}

// 处理重试
function handleRetry() {
  if (formRef.value) {
    formRef.value.generateTender();
  }
}

// 跳转到标书列表页面
function goToTenderList() {
  router.push('/business/tender-list');
}
</script>

<style scoped lang="less">
.tender-container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);

  .tender-header {
    margin-bottom: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-title {
        margin: 0;
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .tender-content {
    display: flex;
    gap: 16px;
    height: calc(100vh - 180px);

    .form-container {
      flex: 0 0 480px;
      overflow-y: auto;
      
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
        
        &:hover {
          background: #a8a8a8;
        }
      }
    }

    .result-container {
      flex: 1;
      overflow-y: auto;
      
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
        
        &:hover {
          background: #a8a8a8;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .tender-container {
    .tender-content {
      flex-direction: column;
      height: auto;

      .form-container {
        flex: none;
        max-height: none;
      }

      .result-container {
        flex: none;
        min-height: 500px;
      }
    }
  }
}
</style>
