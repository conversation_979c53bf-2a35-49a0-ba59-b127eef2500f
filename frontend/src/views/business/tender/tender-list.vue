<!--
  * 标书列表页面
-->
<template>
  <div class="tender-list-container">
    <a-card class="search-card" :bordered="false">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="标书标题">
          <a-input 
            v-model:value="searchForm.title" 
            placeholder="请输入标书标题"
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="标书类型">
          <a-select 
            v-model:value="searchForm.tenderType" 
            placeholder="请选择标书类型"
            style="width: 150px"
            allow-clear
          >
            <a-select-option 
              v-for="(item, key) in TENDER_TYPE_ENUM" 
              :key="key" 
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="项目名称">
          <a-input 
            v-model:value="searchForm.projectName" 
            placeholder="请输入项目名称"
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="状态">
          <a-select 
            v-model:value="searchForm.status" 
            placeholder="请选择状态"
            style="width: 120px"
            allow-clear
          >
            <a-select-option 
              v-for="(item, key) in TENDER_STATUS_ENUM" 
              :key="key" 
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon><SearchOutlined /></template>
              搜索
            </a-button>
            <a-button @click="handleReset">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <a-card class="table-card" :bordered="false">
      <template #title>
        <span>标书列表</span>
      </template>
      <template #extra>
        <a-space>
          <!-- <a-button type="primary" @click="goToGenerate">
            <template #icon><PlusOutlined /></template>
            新建标书
          </a-button> -->
          <a-button 
            type="primary" 
            danger 
            :disabled="selectedRowKeys.length === 0"
            @click="handleBatchDelete"
          >
            <template #icon><DeleteOutlined /></template>
            批量删除
          </a-button>
        </a-space>
      </template>

      <a-table
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="tableLoading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'title'">
            <a @click="handleView(record)">{{ record.title }}</a>
          </template>
          <template v-else-if="column.dataIndex === 'tenderType'">
            <a-tag :color="getTenderTypeColor(record.tenderType)">
              {{ getTenderTypeLabel(record.tenderType) }}
            </a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusLabel(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'estimatedAmount'">
            {{ formatAmount(record.estimatedAmount) }}
          </template>
          <template v-else-if="column.dataIndex === 'createTime'">
            {{ formatDate(record.createTime) }}
          </template>
          <template v-else-if="column.dataIndex === 'deadline'">
            <span :class="{ 'deadline-warning': isDeadlineWarning(record.deadline) }">
              {{ formatDate(record.deadline) }}
            </span>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <a-space>
              <a-button 
                type="link" 
                size="small" 
                @click="handleView(record)"
              >
                查看
              </a-button>
              <a-button 
                type="link" 
                size="small" 
                @click="handleEdit(record)"
                v-if="record.status === 'COMPLETED'"
              >
                编辑
              </a-button>
              <!-- <a-button 
                type="link" 
                size="small" 
                @click="handleExport(record)"
                v-if="record.status === 'COMPLETED'"
              >
                导出
              </a-button> -->
              <a-popconfirm
                title="确定要删除这个标书吗？"
                @confirm="handleDelete(record)"
              >
                <a-button 
                  type="link" 
                  size="small" 
                  danger
                >
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 查看标书弹窗 -->
    <a-modal
      v-model:visible="viewModalVisible"
      title="查看标书"
      width="1200px"
      :footer="null"
    >
      <div v-if="currentTender.id">
        <a-descriptions :column="2" size="small" bordered>
          <a-descriptions-item label="标书标题">
            {{ currentTender.title }}
          </a-descriptions-item>
          <a-descriptions-item label="标书类型">
            <a-tag :color="getTenderTypeColor(currentTender.tenderType)">
              {{ getTenderTypeLabel(currentTender.tenderType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="项目名称" v-if="currentTender.projectName">
            {{ currentTender.projectName }}
          </a-descriptions-item>
          <a-descriptions-item label="招标编号" v-if="currentTender.tenderNo">
            {{ currentTender.tenderNo }}
          </a-descriptions-item>
          <a-descriptions-item label="投标截止时间" v-if="currentTender.deadline">
            {{ formatDate(currentTender.deadline) }}
          </a-descriptions-item>
          <a-descriptions-item label="预估金额" v-if="currentTender.estimatedAmount">
            {{ formatAmount(currentTender.estimatedAmount) }}
          </a-descriptions-item>
          <a-descriptions-item label="生成状态">
            <a-tag :color="getStatusColor(currentTender.status)">
              {{ getStatusLabel(currentTender.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDate(currentTender.createTime) }}
          </a-descriptions-item>
        </a-descriptions>
        <a-divider />
        <div class="tender-header">
          <h3>标书内容</h3>
          <a-button type="primary" @click="handleExportWord(currentTender)">导出Word</a-button>
        </div>
        <div class="tender-content" v-html="sanitizeHtml(formatContent(currentTender.content))"></div>
      </div>
    </a-modal>

    <!-- 编辑标书弹窗 -->
    <a-modal
      v-model:visible="editModalVisible"
      title="编辑标书"
      width="1200px"
      @ok="handleSaveEdit"
      ok-text="保存"
      cancel-text="取消"
      :confirm-loading="saving"
    >
      <div v-if="currentTender.id">
        <a-form :model="editForm" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
          <a-form-item label="标题" name="title">
            <a-input v-model:value="editForm.title" placeholder="请输入标题" />
          </a-form-item>
          <a-form-item label="内容" name="content">
            <WangEditor v-model="editForm.content" :height="1150" />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import { TENDER_TYPE_ENUM, TENDER_STATUS_ENUM } from '/@/constants/business/tender-const';
import { tenderApi } from '/@/api/business/tender-api';
import { smartSentry } from '/@/lib/smart-sentry';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { useRouter } from 'vue-router';
import { marked } from 'marked';
import WangEditor from '/@/components/framework/wangeditor/index.vue';
import DOMPurify from 'dompurify';

// 获取router实例
const router = useRouter();

// 状态变量
const tableLoading = ref(false);
const tableData = ref([]);
const selectedRowKeys = ref([]);
const viewModalVisible = ref(false);
const editModalVisible = ref(false);
const saving = ref(false);
const currentTender = ref({});

// 搜索表单
const searchForm = reactive({
  title: '',
  tenderType: '',
  projectName: '',
  status: ''
});

// 编辑表单
const editForm = reactive({
  id: null,
  title: '',
  content: ''
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
});

// 表格列配置
const columns = [
  {
    title: '标书标题',
    dataIndex: 'title',
    width: 200,
    ellipsis: true
  },
  {
    title: '标书类型',
    dataIndex: 'tenderType',
    width: 100
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
    width: 150,
    ellipsis: true
  },
  {
    title: '招标编号',
    dataIndex: 'tenderNo',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80
  },
  {
    title: '预估金额',
    dataIndex: 'estimatedAmount',
    width: 120
  },
  {
    title: '截止时间',
    dataIndex: 'deadline',
    width: 150
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
    width: 100
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 200,
    fixed: 'right'
  }
];

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});

// 获取标书类型标签
function getTenderTypeLabel(type) {
  return TENDER_TYPE_ENUM[type]?.label || type;
}

// 获取标书类型颜色
function getTenderTypeColor(type) {
  const colorMap = {
    'TECHNICAL': 'blue',
    'COMMERCIAL': 'green',
    'COMPREHENSIVE': 'purple'
  };
  return colorMap[type] || 'default';
}

// 获取状态标签
function getStatusLabel(status) {
  return TENDER_STATUS_ENUM[status]?.label || status;
}

// 获取状态颜色
function getStatusColor(status) {
  return TENDER_STATUS_ENUM[status]?.color || 'default';
}

// 格式化日期
function formatDate(date) {
  if (!date) return '';
  return new Date(date).toLocaleString('zh-CN');
}

// 格式化金额
function formatAmount(amount) {
  if (!amount) return '';
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount);
}

// 格式化内容
function formatContent(content) {
  if (!content) return '';
  return content.replace(/\n/g, '<br>');
}

// 清理HTML内容
function sanitizeHtml(html) {
  return DOMPurify.sanitize(html);
}

// 检查截止时间是否临近
function isDeadlineWarning(deadline) {
  if (!deadline) return false;
  const now = new Date();
  const deadlineDate = new Date(deadline);
  const diffDays = (deadlineDate - now) / (1000 * 60 * 60 * 24);
  return diffDays <= 3 && diffDays >= 0; // 3天内截止
}

// 搜索
function handleSearch() {
  pagination.current = 1;
  loadData();
}

// 重置
function handleReset() {
  Object.assign(searchForm, {
    title: '',
    tenderType: '',
    projectName: '',
    status: ''
  });
  pagination.current = 1;
  loadData();
}

// 表格变化处理
function handleTableChange(pag) {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadData();
}

// 选择变化处理
function onSelectChange(keys) {
  selectedRowKeys.value = keys;
}

// 跳转到生成页面
function goToGenerate() {
  router.push('/business/tender');
}

// 查看标书
async function handleView(record) {
  try {
    const res = await tenderApi.getTenderDetail(record.id);
    if (res.ok) {
      currentTender.value = res.data;
      viewModalVisible.value = true;
    } else {
      message.error(res.msg || '获取详情失败');
    }
  } catch (e) {
    console.error('获取标书详情出错:', e);
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  }
}

// 编辑标书
async function handleEdit(record) {
  try {
    SmartLoading.show();
    // 获取完整的标书详情数据
    const res = await tenderApi.getTenderDetail(record.id);

    if (res.ok) {
      const detailData = res.data;
      currentTender.value = detailData;
      editForm.id = detailData.id;
      editForm.title = detailData.title;
      editForm.content = detailData.content || '';
      editModalVisible.value = true;
    } else {
      message.error(res.msg || '获取标书详情失败');
    }
  } catch (e) {
    console.error('获取标书详情出错:', e);
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    SmartLoading.hide();
  }
}

// 保存编辑
async function handleSaveEdit() {
  try {
    saving.value = true;

    const res = await tenderApi.updateTender({
      id: editForm.id,
      title: editForm.title,
      content: editForm.content
    });

    if (res.ok) {
      message.success('保存成功');
      editModalVisible.value = false;
      loadData(); // 重新加载数据
    } else {
      message.error(res.msg || '保存失败');
    }
  } catch (e) {
    console.error('保存标书出错:', e);
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    saving.value = false;
  }
}

// 导出Word文档
function handleExportWord(record) {
  if (!record.content) {
    message.warning('暂无内容可导出');
    return;
  }

  try {
    SmartLoading.show();

    // 创建Word格式的HTML内容
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${record.title || '标书文档'}</title>
        <style>
          body { font-family: SimSun, sans-serif; margin: 20px; }
          h1 { text-align: center; font-size: 18pt; }
          h2 { margin-top: 20px; border-bottom: 1px solid #ddd; padding-bottom: 5px; font-size: 14pt; }
          h3 { font-size: 12pt; }
          table { width: 100%; border-collapse: collapse; margin: 15px 0; }
          table, th, td { border: 1px solid #000; }
          th, td { padding: 8px; text-align: left; }
        </style>
      </head>
      <body>
        <h1>${record.title || '标书文档'}</h1>
        <div>${marked(record.content)}</div>
      </body>
      </html>
    `;

    // 创建Blob对象
    const blob = new Blob([htmlContent], { type: 'application/msword' });

    // 创建下载链接
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${record.title || '标书文档'}.doc`;

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    smartSentry.captureError(error);
    message.error('导出失败，请稍后重试');
  } finally {
    SmartLoading.hide();
  }
}



// 删除标书
async function handleDelete(record) {
  try {
    const res = await tenderApi.deleteTender(record.id);
    if (res.ok) {
      message.success('删除成功');
      loadData();
    } else {
      message.error(res.msg || '删除失败');
    }
  } catch (e) {
    console.error('删除标书出错:', e);
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  }
}

// 批量删除
async function handleBatchDelete() {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的标书');
    return;
  }

  try {
    const res = await tenderApi.batchDeleteTender(selectedRowKeys.value);
    if (res.ok) {
      message.success('批量删除成功');
      selectedRowKeys.value = [];
      loadData();
    } else {
      message.error(res.msg || '批量删除失败');
    }
  } catch (e) {
    console.error('批量删除标书出错:', e);
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  }
}

// 加载数据
async function loadData() {
  try {
    tableLoading.value = true;

    const res = await tenderApi.queryTender({
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm
    });

    if (res.ok) {
      tableData.value = res.data.list || [];
      pagination.total = res.data.total || 0;
    } else {
      message.error(res.msg || '查询失败');
    }
  } catch (e) {
    console.error('查询标书列表出错:', e);
    smartSentry.captureError(e);
    message.error('系统异常，请稍后重试');
  } finally {
    tableLoading.value = false;
  }
}
</script>

<style scoped lang="less">
.tender-list-container {
  padding: 16px;

  .search-card {
    margin-bottom: 16px;
  }

  .table-card {
    .deadline-warning {
      color: #ff4d4f;
      font-weight: 500;
    }
  }

  .tender-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .tender-content {
    background-color: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    padding: 16px;
    max-height: 400px;
    overflow-y: auto;
    line-height: 1.6;
    font-size: 14px;
    color: #333;
    white-space: pre-wrap;
    word-wrap: break-word;
  }
}
</style>
