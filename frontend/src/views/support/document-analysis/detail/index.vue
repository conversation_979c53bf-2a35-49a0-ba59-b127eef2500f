<template>
    <a-card class="document-analysis-detail-container">
        <div class="header">
            <a-typography-title :level="5">分析结果详情</a-typography-title>
            <a-space>
                <a-button type="primary" @click="handleSave">保存</a-button>
                <a-button @click="goBack">返回</a-button>
                <a-button type="default" @click="handleExportQuotation">导出报价单</a-button>
            </a-space>
        </div>

        <div class="content">
            <a-typography-title :level="5">硬件部分</a-typography-title>
            <a-table
                :columns="hardwareColumns"
                :data-source="hardwareData"
                :pagination="false"
                bordered
                row-key="key"
            >
                <template #bodyCell="{ column, text, record }">
                    <template v-if="column.dataIndex !== 'operation'">
                        <div>
                            <a-textarea
                                v-if="editableData[record.key] && (column.dataIndex === 'config' || column.dataIndex === 'xconfig')"
                                v-model:value="editableData[record.key][column.dataIndex]"
                                :auto-size="{ minRows: 2, maxRows: 6 }"
                                style="margin: -5px 0"
                            />
                            <a-input
                                v-else-if="editableData[record.key]"
                                v-model:value="editableData[record.key][column.dataIndex]"
                                style="margin: -5px 0"
                            />
                            <template v-else>{{ text || '' }}</template>
                        </div>
                    </template>
                    <template v-else-if="column.dataIndex === 'operation'">
                        <div class="editable-row-operations">
                            <template v-if="!editableData[record.key]">
                                <a-typography-link @click="edit(record.key)">编辑</a-typography-link>
                            </template>
                            <template v-else>
                                <a-typography-link @click="save(record.key)">保存</a-typography-link>
                                <a-typography-link @click="cancel(record.key)" class="smart-margin-left10">取消</a-typography-link>
                            </template>
                            <a-popconfirm title="确定删除吗？" @confirm="deleteRow(record.key, 'hardware')">
                                <a-button type="link" danger size="small">删除</a-button>
                            </a-popconfirm>
                        </div>
                    </template>
                </template>
            </a-table>
            <a-button @click="addRow('hardware')" style="margin-top: 16px">添加行</a-button>

            <a-typography-title :level="5" style="margin-top: 32px">软件部分</a-typography-title>
            <a-table
                :columns="softwareColumns"
                :data-source="softwareData"
                :pagination="false"
                bordered
                row-key="key"
            >
                <template #bodyCell="{ column, text, record }">
                    <template v-if="column.dataIndex !== 'operation'">
                        <div>
                            <a-textarea
                                v-if="editableData[record.key] && (column.dataIndex === 'config' || column.dataIndex === 'xconfig')"
                                v-model:value="editableData[record.key][column.dataIndex]"
                                :auto-size="{ minRows: 2, maxRows: 6 }"
                                style="margin: -5px 0"
                            />
                            <a-input
                                v-else-if="editableData[record.key]"
                                v-model:value="editableData[record.key][column.dataIndex]"
                                style="margin: -5px 0"
                            />
                            <template v-else>{{ text || '' }}</template>
                        </div>
                    </template>
                    <template v-else-if="column.dataIndex === 'operation'">
                        <div class="editable-row-operations">
                            <template v-if="!editableData[record.key]">
                                <a-typography-link @click="edit(record.key)">编辑</a-typography-link>
                            </template>
                            <template v-else>
                                <a-typography-link @click="save(record.key)">保存</a-typography-link>
                                <a-typography-link @click="cancel(record.key)" class="smart-margin-left10">取消</a-typography-link>
                            </template>
                            <a-popconfirm title="确定删除吗？" @confirm="deleteRow(record.key, 'software')">
                                <a-button type="link" danger size="small">删除</a-button>
                            </a-popconfirm>
                        </div>
                    </template>
                </template>
            </a-table>
            <a-button @click="addRow('software')" style="margin-top: 16px">添加行</a-button>
        </div>
    </a-card>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { documentAnalysisApi } from '/@/api/support/document-analysis-api';
import { message } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { cloneDeep } from 'lodash';
import { saveAs } from 'file-saver';

const route = useRoute();
const router = useRouter();

const recordId = ref(null); // Assuming we're passing the analysis result ID directly

const hardwareData = ref([]);
const softwareData = ref([]);
const editableData = reactive({});

const hardwareColumns = ref([
    { title: '设备名称', dataIndex: 'deviceName', width: '10%' },
    { title: '原始配置', dataIndex: 'config', width: '25%' },
    { title: '相似配置', dataIndex: 'xconfig', width: '25%' },
    { title: '相似度', dataIndex: 'similarityScore', width: '5%' },
    { title: '价格', dataIndex: 'price', width: '5%' },
    { title: '数量', dataIndex: 'quantity', width: '5%' },
    { title: '备注', dataIndex: 'remark', width: '12%' },
    { title: '操作', dataIndex: 'operation', width: '8%' },
]);

const softwareColumns = ref([
    { title: '设备名称', dataIndex: 'deviceName', width: '10%' },
    { title: '原始配置', dataIndex: 'config', width: '25%' },
    { title: '相似配置', dataIndex: 'xconfig', width: '25%' },
    { title: '相似度', dataIndex: 'similarityScore', width: '5%' },
    { title: '价格', dataIndex: 'price', width: '5%' },
    { title: '数量', dataIndex: 'quantity', width: '5%' },
    { title: '备注', dataIndex: 'remark', width: '12%' },
    { title: '操作', dataIndex: 'operation', width: '8%' },
]);

// Fetch data
const fetchAnalysisResult = async () => {
    SmartLoading.show();
    try {
        const res = await documentAnalysisApi.getAnalysisResult(recordId.value);
        if (res.ok && res.data && res.data.analysisResult) {
            const parsedResult = JSON.parse(res.data.analysisResult);
            hardwareData.value = parsedResult.filter(item => item.category === '硬件部分').map((item, index) => ({ ...item, key: `hardware-${index}` }));
            softwareData.value = parsedResult.filter(item => item.category === '软件部分').map((item, index) => ({ ...item, key: `software-${index}` }));
        } else {
            message.error(res.msg || '获取分析结果失败');
        }
    } catch (e) {
        message.error('系统异常，获取分析结果失败');
    } finally {
        SmartLoading.hide();
    }
};

// Editable table functions
const edit = key => {
    const target = hardwareData.value.find(item => item.key === key) || 
                  softwareData.value.find(item => item.key === key);
    if (target) {
        // 创建一个新对象，确保所有字段都有值（即使是空字符串）
        editableData[key] = {};
        Object.keys(target).forEach(field => {
            editableData[key][field] = target[field] || '';
        });
    }
};

const save = key => {
    const target = hardwareData.value.find(item => item.key === key) || 
                  softwareData.value.find(item => item.key === key);
    if (target) {
        Object.keys(editableData[key]).forEach(field => {
            target[field] = editableData[key][field];
        });
    }
    delete editableData[key];
    message.success('保存成功，请点击上方"保存"按钮提交更改');
};

const cancel = key => {
    delete editableData[key];
};

const deleteRow = (key, category) => {
    if (category === 'hardware') {
        hardwareData.value = hardwareData.value.filter(item => item.key !== key);
    } else if (category === 'software') {
        softwareData.value = softwareData.value.filter(item => item.key !== key);
    }
    message.success('行已删除，请点击上方"保存"按钮提交更改');
};

const addRow = category => {
    const newKey = `new-${Date.now()}`;
    // 获取所有字段
    const columns = category === 'hardware' ? hardwareColumns.value : softwareColumns.value;
    const newRow = { key: newKey, category: category === 'hardware' ? '硬件部分' : '软件部分' };
    columns.forEach(col => {
        if (col.dataIndex !== 'operation' && col.dataIndex !== 'category') {
            newRow[col.dataIndex] = '';
        }
    });
    if (category === 'hardware') {
        hardwareData.value.push(newRow);
    } else if (category === 'software') {
        softwareData.value.push(newRow);
    }
    editableData[newKey] = cloneDeep(newRow);
};

// Save all changes to backend
const handleSave = async () => {
    SmartLoading.show();
    try {
        // 过滤掉 key 字段，仅保留后端需要的字段
        const filterFields = (arr) => arr.map(row => {
            const obj = {};
            Object.keys(row).forEach(k => {
                if (k !== 'key' && k !== 'operation') {
                    obj[k] = row[k] === undefined || row[k] === null ? '' : row[k];
                }
            });
            return obj;
        });
        const combinedData = [...filterFields(hardwareData.value), ...filterFields(softwareData.value)];
        // 直接传对象，不做 JSON.stringify
        const res = await documentAnalysisApi.updateAnalysisResult(recordId.value, combinedData);
        if (res.ok) {
            message.success('分析结果已成功保存');
        } else {
            message.error(res.msg || '保存分析结果失败');
        }
    } catch (e) {
        message.error('系统异常，保存分析结果失败');
    } finally {
        SmartLoading.hide();
    }
};

const goBack = () => {
    router.push({ name: 'DocumentAnalysisResultList' });
};

// 导出报价单
const handleExportQuotation = async () => {
    if (!recordId.value) {
        message.error('未找到分析结果ID');
        return;
    }
    try {
        SmartLoading.show();
        console.log('开始导出报价单，ID:', recordId.value);
        
        // 使用API调用导出，依赖axios的拦截器和handleDownloadData正确处理blob响应
        await documentAnalysisApi.exportQuotation(recordId.value);
        // 成功消息已在axios的handleDownloadData中显示
    } catch (e) {
        console.error('导出报价单失败:', e);
        // 错误消息已在axios拦截器中处理
    } finally {
        SmartLoading.hide();
    }
};

onMounted(() => {
    recordId.value = route.query.id; // Get the ID from route query
    if (recordId.value) {
        fetchAnalysisResult();
    } else {
        message.error('未找到分析结果ID');
    }
});
</script>

<style scoped lang="less">
.document-analysis-detail-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
    }

    .content {
        flex: 1;
        overflow-y: auto;
        padding-right: 16px; // For scrollbar
    }

    .editable-row-operations a {
        margin-right: 8px;
    }

    :deep(.ant-card-body) {
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 16px;
    }
}
</style> 