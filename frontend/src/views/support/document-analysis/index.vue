<template>
  <div class="document-analysis">
    <!-- 步骤1：文件上传 -->
    <a-card :bordered="false" class="upload-section">
      <h3>步骤1：上传文件</h3>
      <a-upload
        :file-list="fileList"
        :before-upload="beforeUpload"
        :customRequest="customRequest"
        :headers="{ Authorization: 'Bearer ' + useUserStore().getToken }"
        @change="handleChange"
      >
        <a-button>
          <upload-outlined /> 选择文件
        </a-button>
      </a-upload>
      <div class="upload-tip">
        <p>支持的文件格式：PDF、Word文档</p>
        <p>文件大小限制：10MB</p>
      </div>
    </a-card>

    <!-- 步骤2：选择模板 -->
    <a-card :bordered="false" class="template-section">
      <h3>步骤2：选择分析模板</h3>
      <div class="template-selection">
        <a-form-item label="分析模板">
          <div class="template-select-container">
            <a-input
              v-model:value="selectedTemplateName"
              placeholder="请选择分析模板"
              readonly
              style="flex: 1"
            />
            <a-button
              type="primary"
              @click="showTemplateSelectModal"
              style="margin-left: 8px"
            >
              选择模板
            </a-button>
          </div>
        </a-form-item>
        <div class="template-tip" v-if="!selectedTemplate">
          <p>请选择一个分析模板以继续进行文档分析</p>
        </div>
      </div>
    </a-card>

    <!-- 步骤3：文档分析 -->
    <a-card :bordered="false" class="analysis-section">
      <h3>步骤3：AI文档分析-->AI匹配物料清单</h3>
      <a-form-item>
        <a-button
          type="primary"
          @click="handleAnalyze"
          :loading="analyzing"
          :disabled="!currentFileId || !selectedTemplate"
        >
          开始分析匹配
        </a-button>
        <a-button
          class="smart-margin-left20"
          @click="handleViewResultsList"
        >
          查看结果列表
        </a-button>
      </a-form-item>
    </a-card>

    <!-- 分析结果展示 -->
    <a-card v-if="parsedSections.length > 0" :bordered="false" class="result-section">
      <h3>分析结果</h3>
      <div v-for="section in parsedSections" :key="section.header">
        <h4>{{ section.header }}</h4>
        <a-table
          :dataSource="section.data"
          :columns="section.columns"
          rowKey="deviceName"
          :bordered="true"
          :pagination="false"
          class="smart-margin-bottom20"
        >
          <template #bodyCell="{ column, record }">
            <div v-html="record[column.dataIndex]"></div>
          </template>
        </a-table>
      </div>
    </a-card>

    <!-- 模板选择弹窗 -->
    <TemplateSelectModal ref="templateSelectModal" :onlyAiTemplate="true" @selectData="handleTemplateSelect" />
  </div>
</template>

<script>
import { defineComponent, ref, watch } from 'vue';
import { message } from 'ant-design-vue';
import { documentAnalysisApi } from '/@/api/support/document-analysis-api';
import { fileApi } from '/@/api/support/file-api';
import { useUserStore } from '/@/store/modules/system/user';
import { FILE_FOLDER_TYPE_ENUM } from '/@/constants/support/file-const';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { useRouter } from 'vue-router';
import TemplateSelectModal from '/@/components/business/template-select-modal/index.vue';

export default defineComponent({
  name: 'DocumentAnalysis',
  components: {
    TemplateSelectModal
  },
  setup() {
    const fileList = ref([]);
    const analysisResult = ref(null);
    const currentFileId = ref(null);
    const analyzing = ref(false);
    const router = useRouter();

    // 模板选择相关状态
    const templateSelectModal = ref();
    const selectedTemplate = ref(null);
    const selectedTemplateName = ref('');

    const parsedSections = ref([]); // 用于存储按类别分组的表格数据

    watch(analysisResult, (newVal) => {
      const columnTitleMap = {
        deviceName: '设备名称',
        config: '配置',
        quantity: '数量',
        remark: '备注',
        category: '类别', // 虽然不直接显示在子表格中，但作为数据属性保留
      };

      parsedSections.value = [];
      if (newVal && newVal.analysisResult) {
        try {
          const allTableData = JSON.parse(newVal.analysisResult);
          if (Array.isArray(allTableData) && allTableData.length > 0) {
            // 按 category 分组数据
            const groupedData = allTableData.reduce((acc, item) => {
              const category = item.category || '未分类'; // 确保有类别，如果没有则归为'未分类'
              if (!acc[category]) {
                acc[category] = [];
              }
              // 过滤掉所有值都为虚线的行，例如：`{quantity=----------, remark=----------, category=软件部分, deviceName=--------------------------, config=-------------------------------------------------------------------------------}`
              const isDividerRow = Object.values(item).every(val => typeof val === 'string' && (val.includes('---') || val.includes('===')));
              if (!isDividerRow) {
                acc[category].push(item);
              }
              return acc;
            }, {});

            for (const category in groupedData) {
              const dataForCategory = groupedData[category];
              if (dataForCategory.length > 0) {
                const desiredColumnOrder = ['deviceName', 'config', 'quantity', 'remark']; // 定义期望的列顺序
                const columnsForCategory = desiredColumnOrder.map(key => {
                  if (dataForCategory[0].hasOwnProperty(key)) { // 确保数据中存在该键
                    return {
                      title: columnTitleMap[key] || key,
                      dataIndex: key,
                      ellipsis: true,
                      width: key === 'deviceName' ? 200 : (key === 'quantity' ? 80 : undefined),
                    };
                  }
                  return null; // 如果数据中不存在该键，则返回null
                }).filter(column => column !== null); // 过滤掉null值

                parsedSections.value.push({
                  header: category,
                  data: dataForCategory,
                  columns: columnsForCategory,
                });
              }
            }
          }
        } catch (e) {
          console.error("解析 analysisResult JSON 失败:", e);
          parsedSections.value = [];
        }
      } else {
        parsedSections.value = [];
      }
    }, { immediate: true });

    const beforeUpload = (file) => {
      const isPDF = file.type === 'application/pdf';
      const isDOC = file.type === 'application/msword' || 
                   file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      
      if (!isPDF && !isDOC) {
        message.error('只能上传PDF或Word文档!');
        return false;
      }
      
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('文件大小不能超过10MB!');
        return false;
      }
      
      return true;
    };

    const customRequest = async ({ file, onSuccess, onError }) => {
      try {
        const formData = new FormData();
        formData.append('file', file);
        const res = await fileApi.uploadFile(formData, 1);
        if (res.data && res.data.fileId) {
          currentFileId.value = res.data.fileId;
          fileList.value = [{
            uid: res.data.fileId,
            name: res.data.fileName,
            status: 'done',
            url: res.data.fileUrl
          }];
          onSuccess(res);
        } else {
          onError(new Error('上传失败'));
        }
      } catch (error) {
        console.error('文件上传失败:', error);
        onError(error);
      }
    };

    const handleAnalyze = async () => {
      if (!currentFileId.value) {
        message.error({
          content: '请先上传文件',
          duration: 3
        });
        return;
      }

      if (!selectedTemplate.value) {
        message.error({
          content: '请先选择分析模板',
          duration: 3
        });
        return;
      }

      analyzing.value = true;
      try {
        const res = await documentAnalysisApi.analyzeDocument({
          fileId: currentFileId.value,
          templateId: selectedTemplate.value.id,
          analysisPrompt: '根据配置参数优先匹配最相似的配置，配置相同客户指定品牌优先，其次价格最低优先。'
        });
        
        if (res.code === 0 && res.data) {
          analysisResult.value = res.data;
          console.log('API响应数据 (res.data):', res.data);
          console.log('API响应数据 ID (res.data.id):', res.data ? res.data.id : 'undefined');
          console.log('文档分析完成，analysisResult:', analysisResult.value);
          console.log('文档分析完成，analysisResult.id:', analysisResult.value.id);
          message.success({
            content: '文档分析完成！请点前往结果列表查看分析结果。',
            duration: 0,
          });
        } else {
          message.error({
            content: res.msg || '分析失败',
            duration: 0
          });
        }
      } catch (error) {
        console.error('分析失败:', error);
        message.error({
          content: '分析失败，请稍后重试',
          duration: 0
        });
      } finally {
        analyzing.value = false;
      }
    };

    const handleChange = (info) => {
      fileList.value = info.fileList;
    };

    // 显示模板选择弹窗
    const showTemplateSelectModal = () => {
      templateSelectModal.value.showModal();
    };

    // 处理模板选择
    const handleTemplateSelect = (template) => {
      if (!template) return;

      selectedTemplate.value = template;
      selectedTemplateName.value = template.name;
      message.success(`已选择模板：${template.name}`);
    };

    const handleViewResultsList = () => {
      router.push({ name: 'DocumentAnalysisResultList' });
    };

    return {
      fileList,
      analysisResult,
      currentFileId,
      analyzing,
      beforeUpload,
      customRequest,
      handleChange,
      handleAnalyze,
      useUserStore,
      parsedSections,
      handleViewResultsList,
      // 模板选择相关
      templateSelectModal,
      selectedTemplate,
      selectedTemplateName,
      showTemplateSelectModal,
      handleTemplateSelect
    };
  }
});
</script>

<style lang="less" scoped>
.document-analysis {
  .upload-section,
  .template-section,
  .analysis-section,
  .result-section {
    margin-bottom: 16px;

    h3 {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .upload-tip {
    margin-top: 8px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;

    p {
      margin-bottom: 4px;
    }
  }

  .template-selection {
    .template-select-container {
      display: flex;
      align-items: center;
    }
  }

  .template-tip {
    margin-top: 8px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;

    p {
      margin-bottom: 4px;
    }
  }

  .result-section {
    h4 {
      margin-bottom: 12px;
      font-size: 14px;
      font-weight: 500;
    }
  }
}
</style> 