<!--
  * 文档分析结果列表
-->
<template>
    <a-card class="document-analysis-result-container">
        <div class="header">
            <a-typography-title :level="5">文档分析结果列表</a-typography-title>
            <div class="query-operate">
                <a-input-search v-model:value.trim="params.keyword" placeholder="文档名称/创建人" @search="queryResultsByKeyword(true)">
                    <template #enterButton>
                        <a-button type="primary">
                            <template #icon>
                                <SearchOutlined />
                            </template>
                            查询
                        </a-button>
                    </template>
                </a-input-search>
                <a-button @click="reset" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </div>
        </div>

        <a-table
            size="small"
            :columns="columns"
            :data-source="tableData"
            :pagination="false"
            :loading="tableLoading"
            :scroll="{ x: 1200 }"
            row-key="id"
            bordered
        >
            <template #bodyCell="{ text, record, index, column }">
                <template v-if="column.dataIndex === 'analysisResult'">
                    <a-tooltip :title="text">
                        <div class="analysis-result-content">{{ text }}</div>
                    </a-tooltip>
                </template>
                <template v-else-if="column.dataIndex === 'operate'">
                    <div class="smart-table-operate">
                        <a-button type="link" size="small" @click="editQuotation(record)">编辑</a-button>
                        <a-button type="link" size="small" @click="viewDetail(record)">查看详情</a-button>
                        <a-button danger type="link" size="small" @click="handleDelete(record)">删除</a-button>
                    </div>
                </template>
            </template>
        </a-table>
        <div class="smart-query-table-page">
            <a-pagination
                showSizeChanger
                showQuickJumper
                show-less-items
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="params.pageSize"
                v-model:current="params.pageNum"
                v-model:pageSize="params.pageSize"
                :total="total"
                @change="queryResults"
                @showSizeChange="queryResults"
                :show-total="showTableTotal"
            />
        </div>
    </a-card>
</template>

<script setup>
    import { ExclamationCircleOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
    import { message, Modal } from 'ant-design-vue';
    import { computed, createVNode, reactive, ref, watch } from 'vue';
    import { documentAnalysisApi } from '/@/api/support/document-analysis-api';
    import { PAGE_SIZE } from '/@/constants/common-const';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { PAGE_SIZE_OPTIONS, showTableTotal } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import { useRouter } from 'vue-router';

    const router = useRouter();

    //字段
    const columns = ref([
        {
            title: '文档名称',
            dataIndex: 'fileName',
            width: 200
        },
        {
            title: '分析提示词',
            dataIndex: 'analysisPrompt',
            width: 300
        },
        {
            title: '分析结果',
            dataIndex: 'analysisResult',
            width: 300
        },
        {
            title: '创建人',
            dataIndex: 'creatorName',
            width: 120
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 180
        },
        {
            title: '操作',
            dataIndex: 'operate',
            width: 180,
            fixed: 'right'
        }
    ]);
    const tableData = ref([]);
    const total = ref(0);
    const tableLoading = ref(false);
    const params = reactive({
        pageNum: 1,
        pageSize: Number(PAGE_SIZE),
        keyword: undefined
    });

    // 查询
    async function queryResults() {
        try {
            tableLoading.value = true;
            const queryParams = {
                pageNum: Number(params.pageNum) || 1,
                pageSize: Number(params.pageSize) || Number(PAGE_SIZE),
                keyword: params.keyword || ''
            };
            console.log('Querying analysis results with params:', queryParams);
            const res = await documentAnalysisApi.queryAnalysisResults(queryParams);
            console.log('Query response:', res);
            
            if (res.ok) {
                if (Array.isArray(res.data.list)) {
                    tableData.value = res.data.list;
                    total.value = res.data.total || 0;
                    console.log('Query success, data size:', res.data.list.length);
                } else {
                    console.error('Invalid response data format:', res.data);
                    message.error('数据格式错误');
                    tableData.value = [];
                    total.value = 0;
                }
            } else {
                console.error('Query failed:', res);
                tableData.value = [];
                total.value = 0;
                message.error(res.msg || '查询失败');
            }
        } catch (e) {
            console.error('Query error:', e);
            smartSentry.captureError(e);
            message.error('系统异常，请稍后重试');
            tableData.value = [];
            total.value = 0;
        } finally {
            tableLoading.value = false;
        }
    }

    // 关键词搜索
    function queryResultsByKeyword(research) {
        if (research) {
            params.pageNum = 1;
        }
        queryResults();
    }

    // 重置
    function reset() {
        params.keyword = undefined;
        params.pageNum = 1;
        params.pageSize = Number(PAGE_SIZE);
        queryResultsByKeyword(true);
    }

    // 编辑报价
    function editQuotation(record) {
        router.push({
            name: 'BaojiaAdd',
            query: {
                fromAnalysis: true,
                analysisId: record.id
            }
        });
    }

    // 查看详情
    function viewDetail(record) {
        router.push({
            name: 'DocumentAnalysisDetail',
            query: { id: record.id }
        });
    }

    // 删除分析结果
    function handleDelete(record) {
        Modal.confirm({
            title: '提示',
            icon: createVNode(ExclamationCircleOutlined),
            content: '确定要删除这条分析结果吗？',
            okText: '确定',
            cancelText: '取消',
            onOk() {
                SmartLoading.show();
                documentAnalysisApi.deleteAnalysisResult(record.id)
                    .then((res) => {
                        message.success('删除成功');
                        queryResults();
                    })
                    .catch((err) => {
                        smartSentry.captureError(err);
                        message.error(err.msg || '删除失败');
                    })
                    .finally(() => {
                        SmartLoading.hide();
                    });
            },
        });
    }

    // 首次加载
    queryResults();
</script>

<style scoped lang="less">
    .document-analysis-result-container {
        height: 100%;
        display: flex;
        flex-direction: column;

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;

            .query-operate {
                display: flex;
                align-items: center;
            }
        }

        .analysis-result-content {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        :deep(.ant-card-body) {
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 16px;
            overflow: hidden;
        }

        :deep(.ant-pagination) {
            margin-top: 16px;
            text-align: right;
        }

        :deep(.ant-table-wrapper) {
            flex: 1;
            overflow: auto;
        }
    }
</style> 