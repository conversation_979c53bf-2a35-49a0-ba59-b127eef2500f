<!--
  * 知识库管理
-->
<template>
    <div class="knowledge-container">
        <a-row class="knowledge-content" :gutter="16">
            <a-col :span="6">
                <a-card class="folder-tree-card" :bordered="false">
                    <template #title>
                        <span>知识库文件夹</span>
                    </template>
                    <KnowledgeFolderTree ref="folderTreeRef" @select="handleFolderSelect" />
                </a-card>
            </a-col>
            <a-col :span="18">
                <a-card class="file-list-card" :bordered="false">
                    <template #title>
                        <div class="file-list-header">
                            <span>文件列表</span>
                            <span class="breadcrumb">
                                <template v-if="breadcrumbList.length > 0">
                                    <a-breadcrumb separator=">">
                                        <a-breadcrumb-item v-for="(item, index) in breadcrumbList" :key="index">
                                            {{ item }}
                                        </a-breadcrumb-item>
                                    </a-breadcrumb>
                                </template>
                            </span>
                        </div>
                    </template>
                    <KnowledgeFileList ref="fileListRef" :folderId="selectedFolderId" @refresh="refreshFileList" />
                </a-card>
            </a-col>
        </a-row>
    </div>
</template>

<script setup>
    import { ref, watch } from 'vue';
    import KnowledgeFolderTree from '/@/components/support/knowledge-folder-tree/index.vue';
    import KnowledgeFileList from '/@/components/support/knowledge-file-list/index.vue';

    // 文件夹树相关
    const folderTreeRef = ref();
    const selectedFolderId = ref(null);
    const breadcrumbList = ref([]);

    // 文件列表相关
    const fileListRef = ref();

    // 监听文件夹选择
    function handleFolderSelect(id, path) {
        selectedFolderId.value = id;
        breadcrumbList.value = path || [];
    }

    // 监听文件夹树的面包屑变化
    watch(
        () => folderTreeRef.value?.breadcrumb,
        (val) => {
            if (val) {
                breadcrumbList.value = val;
            }
        },
        { deep: true }
    );

    // 监听选中的文件夹ID变化
    watch(
        () => folderTreeRef.value?.selectedKeys,
        (val) => {
            if (val && val.length > 0) {
                selectedFolderId.value = val[0];
            }
        },
        { deep: true }
    );

    // 刷新文件列表
    function refreshFileList() {
        if (fileListRef.value) {
            fileListRef.value.queryFiles();
        }
    }
</script>

<style scoped lang="less">
    .knowledge-container {
        height: 100%;
        padding: 16px;
        background-color: #f0f2f5;

        .knowledge-content {
            height: 100%;

            .ant-col {
                height: 100%;
            }

            .folder-tree-card,
            .file-list-card {
                height: 100%;
                display: flex;
                flex-direction: column;

                :deep(.ant-card-body) {
                    flex: 1;
                    overflow: auto;
                    padding: 0;
                }
            }

            .file-list-header {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .breadcrumb {
                    color: #999;
                    font-size: 14px;
                }
            }
        }
    }
</style> 