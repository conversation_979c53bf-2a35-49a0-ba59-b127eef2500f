<template>
  <div class="test-page">
    <h1>简化版AI助手测试页面</h1>
    <div class="test-container">
      <SimpleAiAssistant />
    </div>
  </div>
</template>

<script>
import SimpleAiAssistant from '/@/components/business/ai-assistant/SimpleAiAssistant.vue'

export default {
  name: 'SimpleAiTest',
  components: {
    SimpleAiAssistant
  }
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  background: #f0f2f5;
  min-height: 100vh;
}

.test-container {
  max-width: 800px;
  margin: 0 auto;
  height: 600px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 20px;
}
</style>
